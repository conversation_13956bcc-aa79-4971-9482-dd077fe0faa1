{"permissions": {"allow": ["Bash(./mvnw:*)", "<PERSON><PERSON>(mv:*)", "<PERSON><PERSON>(curl:*)", "<PERSON><PERSON>(time curl:*)", "Bash(for:*)", "Ba<PERSON>(do:*)", "Bash(done)", "<PERSON><PERSON>(mkdir:*)", "<PERSON><PERSON>(timeout:*)", "<PERSON><PERSON>(true:*)", "Bash(rm:*)", "Bash(lsof:*)", "WebSearch", "Bash(npm run dev:*)", "Bash(find:*)", "Bash(jar:*)", "Bash(npm run format)", "<PERSON><PERSON>(pkill:*)", "<PERSON><PERSON>(gtimeout:*)", "mcp__ide__getDiagnostics", "Bash(grep:*)", "<PERSON><PERSON>(echo:*)", "Bash(kill:*)"], "deny": [], "ask": [], "defaultMode": "acceptEdits", "additionalDirectories": ["/Users/<USER>/.m2/repository/com/baomidou/dynamic-datasource-spring-boot-starter"]}, "outputStyle": "default"}