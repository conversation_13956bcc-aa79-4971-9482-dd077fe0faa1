package com.xinjian.generator.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xinjian.generator.domain.GenTable;
import com.xinjian.generator.domain.query.GenTableQuery;
import java.util.List;
import org.apache.ibatis.annotations.Param;

/** 业务 数据层 */
public interface GenTableMapper {
  /**
   * 查询业务列表
   *
   * @param genTable 业务信息
   * @return 业务集合
   */
  public List<GenTable> selectGenTableList(GenTable genTable);

  /**
   * 查询业务列表（使用查询类）
   *
   * @param query 业务查询条件
   * @return 业务集合
   */
  public List<GenTable> selectGenTableListByQuery(GenTableQuery query);

  /**
   * 查询业务列表
   *
   * @param page 分页对象
   * @param query 查询参数对象
   * @return 当页的数据列表。分页的总数等信息，由分页插件填充到传入的 page 对象中。
   */
  public List<GenTable> selectGenTableListByPage(
      @Param("page") IPage<GenTable> page, @Param("query") GenTableQuery query);

  /**
   * 查询据库列表
   *
   * @param genTable 业务信息
   * @return 数据库表集合
   */
  public List<GenTable> selectDbTableList(GenTable genTable);

  /**
   * 查询据库列表（使用查询类）
   *
   * @param query 业务查询条件
   * @return 数据库表集合
   */
  public List<GenTable> selectDbTableListByQuery(GenTableQuery query);

  /**
   * 查询数据库列表
   *
   * @param page 分页对象
   * @param query 查询参数对象
   * @return 当页的数据列表。分页的总数等信息，由分页插件填充到传入的 page 对象中。
   */
  public List<GenTable> selectDbTableListByPage(
      @Param("page") IPage<GenTable> page, @Param("query") GenTableQuery query);

  /**
   * 查询据库列表
   *
   * @param tableNames 表名称组
   * @return 数据库表集合
   */
  public List<GenTable> selectDbTableListByNames(String[] tableNames);

  /**
   * 查询所有表信息
   *
   * @return 表信息集合
   */
  public List<GenTable> selectGenTableAll();

  /**
   * 查询表 ID 业务信息
   *
   * @param id 业务 ID
   * @return 业务信息
   */
  public GenTable selectGenTableById(Long id);

  /**
   * 查询表名称业务信息
   *
   * @param tableName 表名称
   * @return 业务信息
   */
  public GenTable selectGenTableByName(String tableName);

  /**
   * 新增业务
   *
   * @param genTable 业务信息
   * @return 结果
   */
  public int insertGenTable(GenTable genTable);

  /**
   * 修改业务
   *
   * @param genTable 业务信息
   * @return 结果
   */
  public int updateGenTable(GenTable genTable);

  /**
   * 批量删除业务
   *
   * @param ids 需要删除的数据 ID
   * @return 结果
   */
  public int deleteGenTableByIds(Long[] ids);

  /**
   * 创建表
   *
   * @param sql 表结构
   * @return 结果
   */
  public int createTable(String sql);
}
