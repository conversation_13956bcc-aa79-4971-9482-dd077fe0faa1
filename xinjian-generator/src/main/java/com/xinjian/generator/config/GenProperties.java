package com.xinjian.generator.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.PropertySource;
import org.springframework.stereotype.Component;

/** 读取代码生成相关配置 */
@Data
@Component
@ConfigurationProperties(prefix = "gen")
@PropertySource(value = {"classpath:generator.yml"})
public class GenProperties {
  /** 作者 */
  private String author;

  /** 生成包路径 */
  private String packageName;

  /** 自动去除表前缀，默认是 false */
  private boolean autoRemovePre;

  /** 表前缀 (类名不会包含表前缀) */
  private String tablePrefix;
}
