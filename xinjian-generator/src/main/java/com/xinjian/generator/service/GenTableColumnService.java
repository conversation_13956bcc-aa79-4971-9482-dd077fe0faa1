package com.xinjian.generator.service;

import com.xinjian.generator.domain.GenTableColumn;
import com.xinjian.generator.mapper.GenTableColumnMapper;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/** 业务字段 服务层实现 */
@Service
public class GenTableColumnService {
  @Autowired private GenTableColumnMapper genTableColumnMapper;

  /**
   * 根据表 ID 查询字段信息
   *
   * @param tableId 表 ID
   * @return 字段集合
   */
  public List<GenTableColumn> selectGenTableColumnListByTableId(Long tableId) {
    return genTableColumnMapper.selectGenTableColumnListByTableId(tableId);
  }
}
