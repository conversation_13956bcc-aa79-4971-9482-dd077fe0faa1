package com.xinjian.generator.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.fasterxml.jackson.core.type.TypeReference;
import com.xinjian.common.constant.GenConstants;
import com.xinjian.common.exception.status400.BadRequestException;
import com.xinjian.common.properties.PaginationProperties;
import com.xinjian.common.utils.jackson.JacksonUtil;
import com.xinjian.generator.config.GenProperties;
import com.xinjian.generator.domain.GenTable;
import com.xinjian.generator.domain.GenTableColumn;
import com.xinjian.generator.domain.query.GenTableQuery;
import com.xinjian.generator.mapper.GenTableColumnMapper;
import com.xinjian.generator.mapper.GenTableMapper;
import com.xinjian.generator.util.GenUtils;
import com.xinjian.generator.util.VelocityInitializer;
import com.xinjian.generator.util.VelocityUtils;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.IOException;
import java.io.StringWriter;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;
import javax.annotation.PostConstruct;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.velocity.Template;
import org.apache.velocity.VelocityContext;
import org.apache.velocity.app.Velocity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

/** 业务 服务层实现 */
@Service
@Slf4j
@RequiredArgsConstructor
public class GenTableService {

  private final GenTableMapper genTableMapper;
  private final GenTableColumnMapper genTableColumnMapper;
  private final PaginationProperties paginationProperties;
  private final GenProperties genConfig;

  /** 初始化代码生成器配置 */
  @PostConstruct
  public void initGenConfig() {
    GenUtils.setGenConfig(genConfig);
  }

  /**
   * 查询业务信息
   *
   * @param id 业务 ID
   * @return 业务信息
   */
  public GenTable selectGenTableById(Long id) {
    GenTable genTable = genTableMapper.selectGenTableById(id);
    setTableFromOptions(genTable);
    return genTable;
  }

  /**
   * 查询业务列表
   *
   * @param genTable 业务信息
   * @return 业务集合
   */
  public List<GenTable> selectGenTableList(GenTable genTable) {
    return genTableMapper.selectGenTableList(genTable);
  }

  /**
   * 查询业务列表（使用查询类）
   *
   * @param query 业务查询条件
   * @return 业务集合
   */
  public List<GenTable> selectGenTableListByQuery(GenTableQuery query) {
    return genTableMapper.selectGenTableListByQuery(query);
  }

  /**
   * 查询业务列表
   *
   * <p>使用统一分页方案处理分页和排序逻辑
   *
   * @param query 查询参数对象
   * @return 业务分页集合
   */
  public IPage<GenTable> selectGenTableListByPage(GenTableQuery query) {
    // 处理分页和排序逻辑
    IPage<GenTable> page = query.buildPage(paginationProperties);

    // 调用 Mapper 方法查询数据
    List<GenTable> records = genTableMapper.selectGenTableListByPage(page, query);
    page.setRecords(records);

    return page;
  }

  /**
   * 查询据库列表
   *
   * @param genTable 业务信息
   * @return 数据库表集合
   */
  public List<GenTable> selectDbTableList(GenTable genTable) {
    return genTableMapper.selectDbTableList(genTable);
  }

  /**
   * 查询据库列表（使用查询类）
   *
   * @param query 业务查询条件
   * @return 数据库表集合
   */
  public List<GenTable> selectDbTableListByQuery(GenTableQuery query) {
    return genTableMapper.selectDbTableListByQuery(query);
  }

  /**
   * 查询数据库列表
   *
   * <p>使用统一分页方案处理分页和排序逻辑
   *
   * @param query 查询参数对象
   * @return 数据库表分页集合
   */
  public IPage<GenTable> selectDbTableListByPage(GenTableQuery query) {
    // 处理分页和排序逻辑
    IPage<GenTable> page = query.buildPage(paginationProperties);

    // 调用 Mapper 方法查询数据
    List<GenTable> records = genTableMapper.selectDbTableListByPage(page, query);
    page.setRecords(records);

    return page;
  }

  /**
   * 查询据库列表
   *
   * @param tableNames 表名称组
   * @return 数据库表集合
   */
  public List<GenTable> selectDbTableListByNames(String[] tableNames) {
    return genTableMapper.selectDbTableListByNames(tableNames);
  }

  /**
   * 查询所有表信息
   *
   * @return 表信息集合
   */
  public List<GenTable> selectGenTableAll() {
    return genTableMapper.selectGenTableAll();
  }

  /**
   * 修改业务
   *
   * @param genTable 业务信息
   * @return 结果
   */
  @Transactional
  public void updateGenTable(GenTable genTable) {
    // 直接使用实体类的字段，不再通过 params 中转
    Map<String, String> params = new HashMap<>();
    if (StringUtils.isNotBlank(genTable.getTreeCode())) {
      params.put(GenConstants.TREE_CODE, genTable.getTreeCode());
    }
    if (StringUtils.isNotBlank(genTable.getTreeParentCode())) {
      params.put(GenConstants.TREE_PARENT_CODE, genTable.getTreeParentCode());
    }
    if (StringUtils.isNotBlank(genTable.getTreeName())) {
      params.put(GenConstants.TREE_NAME, genTable.getTreeName());
    }
    if (StringUtils.isNotBlank(genTable.getParentMenuId())) {
      params.put(GenConstants.PARENT_MENU_ID, genTable.getParentMenuId());
    }
    if (StringUtils.isNotBlank(genTable.getParentMenuName())) {
      params.put(GenConstants.PARENT_MENU_NAME, genTable.getParentMenuName());
    }
    genTable.setOptions(JacksonUtil.toJSONString(params));
    int row = genTableMapper.updateGenTable(genTable);
    if (row > 0) {
      for (GenTableColumn cenTableColumn : genTable.getColumns()) {
        genTableColumnMapper.updateGenTableColumn(cenTableColumn);
      }
    }
  }

  /**
   * 删除业务对象
   *
   * @param tableIds 需要删除的数据 ID
   * @return 结果
   */
  @Transactional
  public void deleteGenTableByIds(Long[] tableIds) {
    genTableMapper.deleteGenTableByIds(tableIds);
    genTableColumnMapper.deleteGenTableColumnByIds(tableIds);
  }

  /**
   * 创建表
   *
   * @param sql 创建表语句
   * @return 结果
   */
  public boolean createTable(String sql) {
    return genTableMapper.createTable(sql) == 0;
  }

  /**
   * 导入表结构
   *
   * @param tableList 导入表列表
   */
  @Transactional
  public void importGenTable(List<GenTable> tableList, String operName) {
    try {
      for (GenTable table : tableList) {
        String tableName = table.getTableName();
        GenUtils.initTable(table, operName);
        int row = genTableMapper.insertGenTable(table);
        if (row > 0) {
          // 保存列信息
          List<GenTableColumn> genTableColumns =
              genTableColumnMapper.selectDbTableColumnsByName(tableName);
          for (GenTableColumn column : genTableColumns) {
            GenUtils.initColumnField(column, table);
            genTableColumnMapper.insertGenTableColumn(column);
          }
        }
      }
    } catch (Exception e) {
      throw new BadRequestException("导入表结构失败：" + e.getMessage());
    }
  }

  /**
   * 预览代码
   *
   * @param tableId 表编号
   * @return 预览数据列表
   */
  public Map<String, String> previewCode(Long tableId) {
    Map<String, String> dataMap = new LinkedHashMap<>();
    // 查询表信息
    GenTable table = genTableMapper.selectGenTableById(tableId);
    // 设置主子表信息
    setSubTable(table);
    // 设置主键列信息
    setPkColumn(table);
    VelocityInitializer.initVelocity();

    VelocityContext context = VelocityUtils.prepareContext(table);

    // 获取模板列表
    List<String> templates =
        VelocityUtils.getTemplateList(table.getTplCategory(), table.getTplWebType());
    for (String template : templates) {
      // 渲染模板
      StringWriter sw = new StringWriter();
      Template tpl = Velocity.getTemplate(template, StandardCharsets.UTF_8.name());
      tpl.merge(context, sw);
      dataMap.put(template, sw.toString());
    }
    return dataMap;
  }

  /**
   * 生成代码（下载方式）
   *
   * @param tableName 表名称
   * @return 数据
   */
  public byte[] downloadCode(String tableName) {
    ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
    ZipOutputStream zip = new ZipOutputStream(outputStream);
    generatorCode(tableName, zip);
    IOUtils.closeQuietly(zip);
    return outputStream.toByteArray();
  }

  /**
   * 生成代码（自定义路径）
   *
   * @param tableName 表名称
   */
  public void generatorCode(String tableName) {
    // 查询表信息
    GenTable table = genTableMapper.selectGenTableByName(tableName);
    // 设置主子表信息
    setSubTable(table);
    // 设置主键列信息
    setPkColumn(table);

    VelocityInitializer.initVelocity();

    VelocityContext context = VelocityUtils.prepareContext(table);

    // 获取模板列表
    List<String> templates =
        VelocityUtils.getTemplateList(table.getTplCategory(), table.getTplWebType());
    for (String template : templates) {
      if (!StringUtils.containsAny(
          template, "sql.vm", "api.js.vm", "index.vue.vm", "index-tree.vue.vm")) {
        // 渲染模板
        StringWriter sw = new StringWriter();
        Template tpl = Velocity.getTemplate(template, StandardCharsets.UTF_8.name());
        tpl.merge(context, sw);
        try {
          String path = getGenPath(table, template);
          FileUtils.writeStringToFile(new File(path), sw.toString(), StandardCharsets.UTF_8);
        } catch (IOException e) {
          throw new BadRequestException("渲染模板失败，表名：" + table.getTableName());
        }
      }
    }
  }

  /**
   * 同步数据库
   *
   * @param tableName 表名称
   */
  @Transactional
  public void synchDb(String tableName) {
    GenTable table = genTableMapper.selectGenTableByName(tableName);
    List<GenTableColumn> tableColumns = table.getColumns();
    Map<String, GenTableColumn> tableColumnMap =
        tableColumns.stream()
            .collect(Collectors.toMap(GenTableColumn::getColumnName, Function.identity()));

    List<GenTableColumn> dbTableColumns =
        genTableColumnMapper.selectDbTableColumnsByName(tableName);
    if (CollectionUtils.isEmpty(dbTableColumns)) {
      throw new BadRequestException("同步数据失败，原表结构不存在");
    }
    List<String> dbTableColumnNames =
        dbTableColumns.stream().map(GenTableColumn::getColumnName).collect(Collectors.toList());

    dbTableColumns.forEach(
        column -> {
          GenUtils.initColumnField(column, table);
          if (tableColumnMap.containsKey(column.getColumnName())) {
            GenTableColumn prevColumn = tableColumnMap.get(column.getColumnName());
            column.setColumnId(prevColumn.getColumnId());
            if (column.isList()) {
              // 如果是列表，继续保留查询方式/字典类型选项
              column.setDictType(prevColumn.getDictType());
              column.setQueryType(prevColumn.getQueryType());
            }
            if (StringUtils.isNotBlank(prevColumn.getIsRequired())
                && !column.isPk()
                && (column.isInsert() || column.isEdit())
                && ((column.isUsableColumn()) || (!column.isSuperColumn()))) {
              // 如果是 (新增/修改&非主键/非忽略及父属性)，继续保留必填/显示类型选项
              column.setIsRequired(prevColumn.getIsRequired());
              column.setHtmlType(prevColumn.getHtmlType());
            }
            genTableColumnMapper.updateGenTableColumn(column);
          } else {
            genTableColumnMapper.insertGenTableColumn(column);
          }
        });

    List<GenTableColumn> delColumns =
        tableColumns.stream()
            .filter(column -> !dbTableColumnNames.contains(column.getColumnName()))
            .collect(Collectors.toList());
    if (!CollectionUtils.isEmpty(delColumns)) {
      genTableColumnMapper.deleteGenTableColumns(delColumns);
    }
  }

  /**
   * 批量生成代码（下载方式）
   *
   * @param tableNames 表数组
   * @return 数据
   */
  public byte[] downloadCode(String[] tableNames) {
    ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
    ZipOutputStream zip = new ZipOutputStream(outputStream);
    for (String tableName : tableNames) {
      generatorCode(tableName, zip);
    }
    IOUtils.closeQuietly(zip);
    return outputStream.toByteArray();
  }

  /** 查询表信息并生成代码 */
  private void generatorCode(String tableName, ZipOutputStream zip) {
    // 查询表信息
    GenTable table = genTableMapper.selectGenTableByName(tableName);
    // 设置主子表信息
    setSubTable(table);
    // 设置主键列信息
    setPkColumn(table);

    VelocityInitializer.initVelocity();

    VelocityContext context = VelocityUtils.prepareContext(table);

    // 获取模板列表
    List<String> templates =
        VelocityUtils.getTemplateList(table.getTplCategory(), table.getTplWebType());
    for (String template : templates) {
      // 渲染模板
      StringWriter sw = new StringWriter();
      Template tpl = Velocity.getTemplate(template, StandardCharsets.UTF_8.name());
      tpl.merge(context, sw);
      try {
        // 添加到 zip
        zip.putNextEntry(new ZipEntry(VelocityUtils.getFileName(template, table)));
        IOUtils.write(sw.toString(), zip, StandardCharsets.UTF_8.name());
        IOUtils.closeQuietly(sw);
        zip.flush();
        zip.closeEntry();
      } catch (IOException e) {
        log.error("渲染模板失败，表名：" + table.getTableName(), e);
      }
    }
  }

  /**
   * 修改保存参数校验
   *
   * @param genTable 业务信息
   */
  public void validateEdit(GenTable genTable) {
    if (GenConstants.TPL_TREE.equals(genTable.getTplCategory())) {
      // 直接使用实体类的字段进行验证
      if (StringUtils.isBlank(genTable.getTreeCode())) {
        throw new BadRequestException("树编码字段不能为空");
      } else if (StringUtils.isBlank(genTable.getTreeParentCode())) {
        throw new BadRequestException("树父编码字段不能为空");
      } else if (StringUtils.isBlank(genTable.getTreeName())) {
        throw new BadRequestException("树名称字段不能为空");
      }
    } else if (GenConstants.TPL_SUB.equals(genTable.getTplCategory())) {
      if (StringUtils.isBlank(genTable.getSubTableName())) {
        throw new BadRequestException("关联子表的表名不能为空");
      } else if (StringUtils.isBlank(genTable.getSubTableFkName())) {
        throw new BadRequestException("子表关联的外键名不能为空");
      }
    }
  }

  /**
   * 设置主键列信息
   *
   * @param table 业务表信息
   */
  public void setPkColumn(GenTable table) {
    for (GenTableColumn column : table.getColumns()) {
      if (column.isPk()) {
        table.setPkColumn(column);
        break;
      }
    }
    if (Objects.isNull(table.getPkColumn())) {
      table.setPkColumn(table.getColumns().get(0));
    }
    if (GenConstants.TPL_SUB.equals(table.getTplCategory())) {
      for (GenTableColumn column : table.getSubTable().getColumns()) {
        if (column.isPk()) {
          table.getSubTable().setPkColumn(column);
          break;
        }
      }
      if (Objects.isNull(table.getSubTable().getPkColumn())) {
        table.getSubTable().setPkColumn(table.getSubTable().getColumns().get(0));
      }
    }
  }

  /**
   * 设置主子表信息
   *
   * @param table 业务表信息
   */
  public void setSubTable(GenTable table) {
    String subTableName = table.getSubTableName();
    if (StringUtils.isNotBlank(subTableName)) {
      table.setSubTable(genTableMapper.selectGenTableByName(subTableName));
    }
  }

  /**
   * 设置代码生成其他选项值
   *
   * @param genTable 设置后的生成对象
   */
  public void setTableFromOptions(GenTable genTable) {
    String options = genTable.getOptions();
    if (StringUtils.isNotEmpty(options)) {
      Map<String, String> params =
          JacksonUtil.parseObject(options, new TypeReference<Map<String, String>>() {});
      // 从 params 中设置字段值，保持向后兼容
      String treeCode = params.get(GenConstants.TREE_CODE);
      String treeParentCode = params.get(GenConstants.TREE_PARENT_CODE);
      String treeName = params.get(GenConstants.TREE_NAME);
      String parentMenuId = params.get(GenConstants.PARENT_MENU_ID);
      String parentMenuName = params.get(GenConstants.PARENT_MENU_NAME);

      genTable.setTreeCode(treeCode);
      genTable.setTreeParentCode(treeParentCode);
      genTable.setTreeName(treeName);
      genTable.setParentMenuId(parentMenuId);
      genTable.setParentMenuName(parentMenuName);
    }
  }

  /**
   * 获取代码生成地址
   *
   * @param table 业务表信息
   * @param template 模板文件路径
   * @return 生成地址
   */
  public static String getGenPath(GenTable table, String template) {
    String genPath = table.getGenPath();
    if (StringUtils.equals(genPath, "/")) {
      return System.getProperty("user.dir")
          + File.separator
          + "src"
          + File.separator
          + VelocityUtils.getFileName(template, table);
    }
    return genPath + File.separator + VelocityUtils.getFileName(template, table);
  }
}
