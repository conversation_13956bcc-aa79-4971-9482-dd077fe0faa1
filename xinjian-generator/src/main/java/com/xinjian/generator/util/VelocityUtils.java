package com.xinjian.generator.util;

import com.fasterxml.jackson.core.type.TypeReference;
import com.xinjian.common.constant.GenConstants;
import com.xinjian.common.utils.TimeUtils;
import com.xinjian.common.utils.jackson.JacksonUtil;
import com.xinjian.generator.domain.GenTable;
import com.xinjian.generator.domain.GenTableColumn;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import org.apache.commons.lang3.StringUtils;
import org.apache.velocity.VelocityContext;

/** 模板处理工具类 */
public class VelocityUtils {
  /** 项目空间路径 */
  private static final String PROJECT_PATH = "main/java";

  /** mybatis 空间路径 */
  private static final String MYBATIS_PATH = "main/resources/mapper";

  /** 默认上级菜单，系统工具 */
  private static final String DEFAULT_PARENT_MENU_ID = "3";

  /**
   * 设置模板变量信息
   *
   * @return 模板列表
   */
  public static VelocityContext prepareContext(GenTable genTable) {
    String moduleName = genTable.getModuleName();
    String businessName = genTable.getBusinessName();
    String packageName = genTable.getPackageName();
    String tplCategory = genTable.getTplCategory();
    String functionName = genTable.getFunctionName();

    VelocityContext velocityContext = new VelocityContext();
    velocityContext.put("tplCategory", genTable.getTplCategory());
    velocityContext.put("tableName", genTable.getTableName());
    velocityContext.put(
        "functionName", StringUtils.isNotBlank(functionName) ? functionName : "【请填写功能名称】");
    velocityContext.put("ClassName", genTable.getClassName());
    velocityContext.put("className", StringUtils.uncapitalize(genTable.getClassName()));
    velocityContext.put("moduleName", genTable.getModuleName());
    velocityContext.put("BusinessName", StringUtils.capitalize(genTable.getBusinessName()));
    velocityContext.put("businessName", genTable.getBusinessName());
    velocityContext.put("basePackage", getPackagePrefix(packageName));
    velocityContext.put("packageName", packageName);
    velocityContext.put("author", genTable.getFunctionAuthor());
    velocityContext.put("datetime", TimeUtils.getCurrentDateStr());
    velocityContext.put("pkColumn", genTable.getPkColumn());
    velocityContext.put("importList", getImportList(genTable));
    velocityContext.put("permissionPrefix", getPermissionPrefix(moduleName, businessName));
    velocityContext.put("columns", genTable.getColumns());
    velocityContext.put("table", genTable);
    velocityContext.put("dicts", getDicts(genTable));
    setMenuVelocityContext(velocityContext, genTable);
    if (GenConstants.TPL_TREE.equals(tplCategory)) {
      setTreeVelocityContext(velocityContext, genTable);
    }
    if (GenConstants.TPL_SUB.equals(tplCategory)) {
      setSubVelocityContext(velocityContext, genTable);
    }
    return velocityContext;
  }

  public static void setMenuVelocityContext(VelocityContext context, GenTable genTable) {
    String options = genTable.getOptions();
    String parentMenuId = getParentMenuId(options);
    context.put("parentMenuId", parentMenuId);
  }

  public static void setTreeVelocityContext(VelocityContext context, GenTable genTable) {
    String options = genTable.getOptions();
    String treeCode = getTreecode(options);
    String treeParentCode = getTreeParentCode(options);
    String treeName = getTreeName(options);

    context.put("treeCode", treeCode);
    context.put("treeParentCode", treeParentCode);
    context.put("treeName", treeName);
    context.put("expandColumn", getExpandColumn(genTable));
    if (StringUtils.isNotBlank(treeParentCode)) {
      context.put("tree_parent_code", getOptionsMap(options).get(GenConstants.TREE_PARENT_CODE));
    }
    if (StringUtils.isNotBlank(treeName)) {
      context.put("tree_name", getOptionsMap(options).get(GenConstants.TREE_NAME));
    }
  }

  public static void setSubVelocityContext(VelocityContext context, GenTable genTable) {
    GenTable subTable = genTable.getSubTable();
    String subTableName = genTable.getSubTableName();
    String subTableFkName = genTable.getSubTableFkName();
    String subClassName = genTable.getSubTable().getClassName();
    String subTableFkClassName = toCamelCase(subTableFkName);

    context.put("subTable", subTable);
    context.put("subTableName", subTableName);
    context.put("subTableFkName", subTableFkName);
    context.put("subTableFkClassName", subTableFkClassName);
    context.put("subTableFkclassName", StringUtils.uncapitalize(subTableFkClassName));
    context.put("subClassName", subClassName);
    context.put("subclassName", StringUtils.uncapitalize(subClassName));
    context.put("subImportList", getImportList(genTable.getSubTable()));
  }

  /**
   * 获取模板信息
   *
   * @param tplCategory 生成的模板
   * @param tplWebType 前端类型
   * @return 模板列表
   */
  public static List<String> getTemplateList(String tplCategory, String tplWebType) {
    String useWebType = "vm/vue";
    if ("element-plus".equals(tplWebType)) {
      useWebType = "vm/vue/v3";
    }
    List<String> templates = new ArrayList<String>();
    templates.add("vm/java/domain.java.vm");
    templates.add("vm/java/mapper.java.vm");
    templates.add("vm/java/service.java.vm");
    templates.add("vm/java/controller.java.vm");
    templates.add("vm/xml/mapper.xml.vm");
    templates.add("vm/sql/sql.vm");
    templates.add("vm/js/api.js.vm");
    if (GenConstants.TPL_CRUD.equals(tplCategory)) {
      templates.add(useWebType + "/index.vue.vm");
    } else if (GenConstants.TPL_TREE.equals(tplCategory)) {
      templates.add(useWebType + "/index-tree.vue.vm");
    } else if (GenConstants.TPL_SUB.equals(tplCategory)) {
      templates.add(useWebType + "/index.vue.vm");
      templates.add("vm/java/sub-domain.java.vm");
    }
    return templates;
  }

  /** 获取文件名 */
  public static String getFileName(String template, GenTable genTable) {
    // 文件名称
    String fileName = "";
    // 包路径
    String packageName = genTable.getPackageName();
    // 模块名
    String moduleName = genTable.getModuleName();
    // 大写类名
    String className = genTable.getClassName();
    // 业务名称
    String businessName = genTable.getBusinessName();

    String javaPath = PROJECT_PATH + "/" + StringUtils.replace(packageName, ".", "/");
    String mybatisPath = MYBATIS_PATH + "/" + moduleName;
    String vuePath = "vue";

    if (template.contains("domain.java.vm")) {
      fileName = String.format("%s/domain/%s.java", javaPath, className);
    }
    if (template.contains("sub-domain.java.vm")
        && StringUtils.equals(GenConstants.TPL_SUB, genTable.getTplCategory())) {
      fileName =
          String.format("%s/domain/%s.java", javaPath, genTable.getSubTable().getClassName());
    } else if (template.contains("mapper.java.vm")) {
      fileName = String.format("%s/mapper/%sMapper.java", javaPath, className);
    } else if (template.contains("service.java.vm")) {
      fileName = String.format("%s/service/I%sService.java", javaPath, className);
    } else if (template.contains("serviceImpl.java.vm")) {
      fileName = String.format("%s/service/impl/%sServiceImpl.java", javaPath, className);
    } else if (template.contains("controller.java.vm")) {
      fileName = String.format("%s/controller/%sController.java", javaPath, className);
    } else if (template.contains("mapper.xml.vm")) {
      fileName = String.format("%s/%sMapper.xml", mybatisPath, className);
    } else if (template.contains("sql.vm")) {
      fileName = businessName + "Menu.sql";
    } else if (template.contains("api.js.vm")) {
      fileName = String.format("%s/api/%s/%s.js", vuePath, moduleName, businessName);
    } else if (template.contains("index.vue.vm")) {
      fileName = String.format("%s/views/%s/%s/index.vue", vuePath, moduleName, businessName);
    } else if (template.contains("index-tree.vue.vm")) {
      fileName = String.format("%s/views/%s/%s/index.vue", vuePath, moduleName, businessName);
    }
    return fileName;
  }

  /**
   * 获取包前缀
   *
   * @param packageName 包名称
   * @return 包前缀名称
   */
  public static String getPackagePrefix(String packageName) {
    int lastIndex = packageName.lastIndexOf(".");
    return StringUtils.substring(packageName, 0, lastIndex);
  }

  /**
   * 根据列类型获取导入包
   *
   * @param genTable 业务表对象
   * @return 返回需要导入的包列表
   */
  public static HashSet<String> getImportList(GenTable genTable) {
    List<GenTableColumn> columns = genTable.getColumns();
    GenTable subGenTable = genTable.getSubTable();
    HashSet<String> importList = new HashSet<String>();
    if (Objects.nonNull(subGenTable)) {
      importList.add("java.util.List");
    }
    for (GenTableColumn column : columns) {
      if (!column.isSuperColumn() && GenConstants.TYPE_DATE.equals(column.getJavaType())) {
        importList.add("java.time.LocalDateTime");
        importList.add("com.fasterxml.jackson.annotation.JsonFormat");
      } else if (!column.isSuperColumn()
          && GenConstants.TYPE_BIGDECIMAL.equals(column.getJavaType())) {
        importList.add("java.math.BigDecimal");
      }
    }
    return importList;
  }

  /**
   * 根据列类型获取字典组
   *
   * @param genTable 业务表对象
   * @return 返回字典组
   */
  public static String getDicts(GenTable genTable) {
    List<GenTableColumn> columns = genTable.getColumns();
    Set<String> dicts = new HashSet<String>();
    addDicts(dicts, columns);
    if (Objects.nonNull(genTable.getSubTable())) {
      List<GenTableColumn> subColumns = genTable.getSubTable().getColumns();
      addDicts(dicts, subColumns);
    }
    return StringUtils.join(dicts, ", ");
  }

  /**
   * 添加字典列表
   *
   * @param dicts 字典列表
   * @param columns 列集合
   */
  public static void addDicts(Set<String> dicts, List<GenTableColumn> columns) {
    for (GenTableColumn column : columns) {
      if (!column.isSuperColumn()
          && StringUtils.isNotBlank(column.getDictType())
          && StringUtils.equalsAny(
              column.getHtmlType(),
              new String[] {
                GenConstants.HTML_SELECT, GenConstants.HTML_RADIO, GenConstants.HTML_CHECKBOX
              })) {
        dicts.add("'" + column.getDictType() + "'");
      }
    }
  }

  /**
   * 获取权限前缀
   *
   * @param moduleName 模块名称
   * @param businessName 业务名称
   * @return 返回权限前缀
   */
  public static String getPermissionPrefix(String moduleName, String businessName) {
    return String.format("%s:%s", moduleName, businessName);
  }

  /**
   * 获取上级菜单 ID 字段
   *
   * @param paramsObj 生成其他选项
   * @return 上级菜单 ID 字段
   */
  public static String getParentMenuId(String options) {
    if (StringUtils.isBlank(options)) {
      return DEFAULT_PARENT_MENU_ID;
    }
    String parentMenuId = getOptionsMap(options).get(GenConstants.PARENT_MENU_ID);
    if (StringUtils.isNotBlank(parentMenuId)) {
      return parentMenuId;
    }
    return DEFAULT_PARENT_MENU_ID;
  }

  /**
   * 获取树编码
   *
   * @param options 生成其他选项
   * @return 树编码
   */
  public static String getTreecode(String options) {
    String treeCode = getOptionsMap(options).get(GenConstants.TREE_CODE);
    if (StringUtils.isNotBlank(treeCode)) {
      return toCamelCase(treeCode);
    }
    return StringUtils.EMPTY;
  }

  /**
   * 获取树父编码
   *
   * @param options 生成其他选项
   * @return 树父编码
   */
  public static String getTreeParentCode(String options) {
    String treeParentCode = getOptionsMap(options).get(GenConstants.TREE_PARENT_CODE);
    if (StringUtils.isNotBlank(treeParentCode)) {
      return toCamelCase(treeParentCode);
    }
    return StringUtils.EMPTY;
  }

  /**
   * 获取树名称
   *
   * @param options 生成其他选项
   * @return 树名称
   */
  public static String getTreeName(String options) {
    String treeName = getOptionsMap(options).get(GenConstants.TREE_NAME);
    if (StringUtils.isNotBlank(treeName)) {
      return toCamelCase(treeName);
    }
    return StringUtils.EMPTY;
  }

  /**
   * 获取需要在哪一列上面显示展开按钮
   *
   * @param genTable 业务表对象
   * @return 展开按钮列序号
   */
  public static int getExpandColumn(GenTable genTable) {
    String options = genTable.getOptions();
    String treeName = getOptionsMap(options).get(GenConstants.TREE_NAME);
    int num = 0;
    for (GenTableColumn column : genTable.getColumns()) {
      if (column.isList()) {
        num++;
        String columnName = column.getColumnName();
        if (columnName.equals(treeName)) {
          break;
        }
      }
    }
    return num;
  }

  /**
   * 驼峰命名法
   *
   * @param s a {@link java.lang.String} object.
   * @return a {@link java.lang.String} object.
   */
  public static String toCamelCase(String s) {
    if (s == null) {
      return null;
    }
    s = s.toLowerCase();
    StringBuilder sb = new StringBuilder(s.length());
    boolean upperCase = false;
    for (int i = 0; i < s.length(); i++) {
      char c = s.charAt(i);

      if (c == '_') {
        upperCase = true;
      } else if (upperCase) {
        sb.append(Character.toUpperCase(c));
        upperCase = false;
      } else {
        sb.append(c);
      }
    }
    return sb.toString();
  }

  private static Map<String, String> getOptionsMap(String options) {
    if (StringUtils.isBlank(options)) {
      return new java.util.HashMap<>();
    }
    return JacksonUtil.parseObject(options, new TypeReference<Map<String, String>>() {});
  }
}
