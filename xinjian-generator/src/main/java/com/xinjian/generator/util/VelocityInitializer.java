package com.xinjian.generator.util;

import com.xinjian.common.exception.status500.ServiceException;
import java.nio.charset.StandardCharsets;
import java.util.Properties;
import org.apache.velocity.app.Velocity;

/** VelocityEngine 工厂 */
public class VelocityInitializer {
  /** 初始化 vm 方法 */
  public static void initVelocity() {
    Properties p = new Properties();
    try {
      // 加载 classpath 目录下的 vm 文件
      p.setProperty(
          "resource.loader.file.class",
          "org.apache.velocity.runtime.resource.loader.ClasspathResourceLoader");
      // 定义字符集
      p.setProperty(Velocity.INPUT_ENCODING, StandardCharsets.UTF_8.name());
      // 初始化 Velocity 引擎，指定配置 Properties
      Velocity.init(p);
    } catch (Exception e) {
      throw new ServiceException("初始化 Velocity 引擎失败，请检查配置文件是否正确，错误信息：" + e.getMessage());
    }
  }
}
