package com.xinjian.generator.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.xinjian.common.core.domain.BaseEntity;
import javax.validation.constraints.NotBlank;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.StringUtils;

@Data
@EqualsAndHashCode(callSuper = true)
/** 代码生成业务字段表 gen_table_column */
public class GenTableColumn extends BaseEntity {
  private static final long serialVersionUID = 1L;

  /** 编号 */
  @TableId(type = IdType.AUTO)
  private Long columnId;

  /** 归属表编号 */
  private Long tableId;

  /** 列名称 */
  private String columnName;

  /** 列描述 */
  private String columnComment;

  /** 列类型 */
  private String columnType;

  /** JAVA 类型 */
  private String javaType;

  /** JAVA 字段名 */
  @NotBlank(message = "Java 属性不能为空")
  private String javaField;

  /** 是否主键（1 是） */
  private String isPk;

  /** 是否自增（1 是） */
  private String isIncrement;

  /** 是否必填（1 是） */
  private String isRequired;

  /** 是否为插入字段（1 是） */
  private String isInsert;

  /** 是否编辑字段（1 是） */
  private String isEdit;

  /** 是否列表字段（1 是） */
  private String isList;

  /** 是否查询字段（1 是） */
  private String isQuery;

  /** 查询方式（EQ 等于、NE 不等于、GT 大于、LT 小于、LIKE 模糊、BETWEEN 范围） */
  private String queryType;

  /**
   * 显示类型（input 文本框、textarea 文本域、select 下拉框、checkbox 复选框、radio 单选框、datetime 日期控件、image 图片上传控件、upload
   * 文件上传控件、editor 富文本控件）
   */
  private String htmlType;

  /** 字典类型 */
  private String dictType;

  /** 排序 */
  private Integer sort;

  public static boolean isSuperColumn(String javaField) {
    return StringUtils.equalsAnyIgnoreCase(
        javaField,
        // BaseEntity
        "createBy",
        "createTime",
        "updateBy",
        "updateTime",
        "remark",
        // TreeEntity
        "parentName",
        "parentId",
        "orderNum",
        "ancestors");
  }

  public static boolean isUsableColumn(String javaField) {
    // isSuperColumn() 中的名单用于避免生成多余 Domain 属性，若某些属性在生成页面时需要用到不能忽略，则放在此处白名单
    return StringUtils.equalsAnyIgnoreCase(javaField, "parentId", "orderNum", "remark");
  }

  public String getCapJavaField() {
    return StringUtils.capitalize(javaField);
  }

  public boolean isPk() {
    return isPk(this.isPk);
  }

  public static boolean isPk(String isPk) {
    return isPk != null && isPk.equals("1");
  }

  public boolean isInsert() {
    return isInsert(this.isInsert);
  }

  public static boolean isInsert(String isInsert) {
    return isInsert != null && isInsert.equals("1");
  }

  public boolean isEdit() {
    return isEdit(this.isEdit);
  }

  public static boolean isEdit(String isEdit) {
    return isEdit != null && isEdit.equals("1");
  }

  public boolean isList() {
    return isList(this.isList);
  }

  public static boolean isList(String isList) {
    return isList != null && isList.equals("1");
  }

  public boolean isQuery() {
    return isQuery(this.isQuery);
  }

  public static boolean isQuery(String isQuery) {
    return isQuery != null && isQuery.equals("1");
  }

  public boolean isRequired() {
    return isRequired(this.isRequired);
  }

  public static boolean isRequired(String isRequired) {
    return isRequired != null && isRequired.equals("1");
  }

  public boolean isSuperColumn() {
    return isSuperColumn(this.javaField);
  }

  public boolean isUsableColumn() {
    return isUsableColumn(this.javaField);
  }

  public String readConverterExp() {
    String remarks = StringUtils.substringBetween(this.columnComment, "（", "）");
    StringBuffer sb = new StringBuffer();
    if (StringUtils.isNotEmpty(remarks)) {
      for (String value : remarks.split(" ")) {
        if (StringUtils.isNotEmpty(value)) {
          Object startStr = value.subSequence(0, 1);
          String endStr = value.substring(1);
          sb.append("").append(startStr).append("=").append(endStr).append(",");
        }
      }
      return sb.deleteCharAt(sb.length() - 1).toString();
    } else {
      return this.columnComment;
    }
  }
}
