package com.xinjian.generator.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.xinjian.common.constant.GenConstants;
import com.xinjian.common.core.domain.BaseEntity;
import java.util.List;
import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;

@Data
@EqualsAndHashCode(callSuper = true)
/** 业务表 gen_table */
public class GenTable extends BaseEntity {
  private static final long serialVersionUID = 1L;

  /** 编号 */
  @TableId(type = IdType.AUTO)
  private Long tableId;

  /** 表名称 */
  @NotBlank(message = "表名称不能为空")
  private String tableName;

  /** 表描述 */
  @NotBlank(message = "表描述不能为空")
  private String tableComment;

  /** 关联父表的表名 */
  private String subTableName;

  /** 本表关联父表的外键名 */
  private String subTableFkName;

  /** 实体类名称 (首字母大写) */
  @NotBlank(message = "实体类名称不能为空")
  private String className;

  /** 使用的模板（crud 单表操作 tree 树表操作 sub 主子表操作） */
  private String tplCategory;

  /** 前端类型（element-ui 模版 element-plus 模版） */
  private String tplWebType;

  /** 生成包路径 */
  @NotBlank(message = "生成包路径不能为空")
  private String packageName;

  /** 生成模块名 */
  @NotBlank(message = "生成模块名不能为空")
  private String moduleName;

  /** 生成业务名 */
  @NotBlank(message = "生成业务名不能为空")
  private String businessName;

  /** 生成功能名 */
  @NotBlank(message = "生成功能名不能为空")
  private String functionName;

  /** 生成作者 */
  @NotBlank(message = "作者不能为空")
  private String functionAuthor;

  /** 生成代码方式（0zip 压缩包 1 自定义路径） */
  private String genType;

  /** 生成路径（不填默认项目路径） */
  private String genPath;

  /** 主键信息 */
  private GenTableColumn pkColumn;

  /** 子表信息 */
  private GenTable subTable;

  /** 表列信息 */
  @Valid private List<GenTableColumn> columns;

  /** 其它生成选项 */
  private String options;

  /** 树编码字段 */
  private String treeCode;

  /** 树父编码字段 */
  private String treeParentCode;

  /** 树名称字段 */
  private String treeName;

  /** 上级菜单 ID 字段 */
  private String parentMenuId;

  /** 上级菜单名称字段 */
  private String parentMenuName;

  public boolean isSub() {
    return isSub(this.tplCategory);
  }

  public static boolean isSub(String tplCategory) {
    return tplCategory != null && StringUtils.equals(GenConstants.TPL_SUB, tplCategory);
  }

  public boolean isTree() {
    return isTree(this.tplCategory);
  }

  public static boolean isTree(String tplCategory) {
    return tplCategory != null && StringUtils.equals(GenConstants.TPL_TREE, tplCategory);
  }

  public boolean isCrud() {
    return isCrud(this.tplCategory);
  }

  public static boolean isCrud(String tplCategory) {
    return tplCategory != null && StringUtils.equals(GenConstants.TPL_CRUD, tplCategory);
  }

  public boolean isSuperColumn(String javaField) {
    return isSuperColumn(this.tplCategory, javaField);
  }

  public static boolean isSuperColumn(String tplCategory, String javaField) {
    if (isTree(tplCategory)) {
      return StringUtils.equalsAnyIgnoreCase(
          javaField, ArrayUtils.addAll(GenConstants.TREE_ENTITY, GenConstants.BASE_ENTITY));
    }
    return StringUtils.equalsAnyIgnoreCase(javaField, GenConstants.BASE_ENTITY);
  }
}
