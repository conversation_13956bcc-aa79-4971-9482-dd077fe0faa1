package com.xinjian.generator.domain.query;

import com.xinjian.common.core.domain.BaseQuery;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class GenTableQuery extends BaseQuery<GenTableQuery> {

  private Long tableId;
  private String tableName;
  private String tableComment;

  @Override
  public GenTableQuery getQueryParams() {
    return this;
  }
}
