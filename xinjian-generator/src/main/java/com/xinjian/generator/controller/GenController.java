package com.xinjian.generator.controller;

import com.alibaba.druid.DbType;
import com.alibaba.druid.sql.SQLUtils;
import com.alibaba.druid.sql.ast.SQLStatement;
import com.alibaba.druid.sql.dialect.mysql.ast.statement.MySqlCreateTableStatement;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xinjian.common.annotation.Log;
import com.xinjian.common.core.domain.dto.LoginUser;
import com.xinjian.common.core.page.TableDataInfo;
import com.xinjian.common.core.text.Convert;
import com.xinjian.common.enums.BusinessType;
import com.xinjian.common.exception.status500.ServiceException;
import com.xinjian.common.utils.sql.SqlUtil;
import com.xinjian.generator.domain.GenTable;
import com.xinjian.generator.domain.GenTableColumn;
import com.xinjian.generator.domain.GenTableInfoResponse;
import com.xinjian.generator.domain.query.GenTableQuery;
import com.xinjian.generator.service.GenTableColumnService;
import com.xinjian.generator.service.GenTableService;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import javax.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;

/** 代码生成 操作处理 */
@Slf4j
@RestController
@RequestMapping("/tool/gen")
public class GenController {
  @Autowired private GenTableService genTableService;

  @Autowired private GenTableColumnService genTableColumnService;

  /** 查询代码生成列表 */
  @PreAuthorize("hasAnyAuthority('tool:gen:list')")
  @GetMapping("/list")
  public TableDataInfo<GenTable> genList(GenTableQuery query) {
    IPage<GenTable> page = genTableService.selectGenTableListByPage(query);
    return new TableDataInfo<>(page);
  }

  /** 修改代码生成业务 */
  @PreAuthorize("hasAnyAuthority('tool:gen:query')")
  @GetMapping(value = "/{tableId}")
  public GenTableInfoResponse getInfo(@PathVariable Long tableId) {
    GenTable table = genTableService.selectGenTableById(tableId);
    List<GenTable> tables = genTableService.selectGenTableAll();
    List<GenTableColumn> list = genTableColumnService.selectGenTableColumnListByTableId(tableId);

    GenTableInfoResponse dto = new GenTableInfoResponse();
    dto.setInfo(table);
    dto.setRows(list);
    dto.setTables(tables);
    return dto;
  }

  /** 查询数据库列表 */
  @PreAuthorize("hasAnyAuthority('tool:gen:list')")
  @GetMapping("/db/list")
  public TableDataInfo<GenTable> dataList(GenTableQuery query) {
    IPage<GenTable> page = genTableService.selectDbTableListByPage(query);
    return new TableDataInfo<>(page);
  }

  /** 查询数据表字段列表 */
  @PreAuthorize("hasAnyAuthority('tool:gen:list')")
  @GetMapping(value = "/column/{tableId}")
  public TableDataInfo<GenTableColumn> columnList(Long tableId) {
    List<GenTableColumn> list = genTableColumnService.selectGenTableColumnListByTableId(tableId);
    return TableDataInfo.of(list);
  }

  /** 导入表结构（保存） */
  @PreAuthorize("hasAnyAuthority('tool:gen:import')")
  @Log(title = "代码生成", businessType = BusinessType.IMPORT)
  @PostMapping("/importTable")
  public void importTableSave(String tables, @AuthenticationPrincipal LoginUser loginUser) {
    String[] tableNames = Convert.toStrArray(tables);
    // 查询表信息
    List<GenTable> tableList = genTableService.selectDbTableListByNames(tableNames);
    genTableService.importGenTable(tableList, loginUser.getUsername());
  }

  /** 创建表结构（保存） */
  @PreAuthorize("hasRole('ADMIN')")
  @Log(title = "创建表", businessType = BusinessType.OTHER)
  @PostMapping("/createTable")
  @ResponseStatus(HttpStatus.CREATED)
  public void createTableSave(String sql, @AuthenticationPrincipal LoginUser loginUser) {
    try {
      SqlUtil.filterKeyword(sql);
      List<SQLStatement> sqlStatements = SQLUtils.parseStatements(sql, DbType.mysql);
      List<String> tableNames = new ArrayList<>();
      for (SQLStatement sqlStatement : sqlStatements) {
        if (sqlStatement instanceof MySqlCreateTableStatement) {
          MySqlCreateTableStatement createTableStatement = (MySqlCreateTableStatement) sqlStatement;
          if (genTableService.createTable(createTableStatement.toString())) {
            String tableName = createTableStatement.getTableName().replaceAll("`", "");
            tableNames.add(tableName);
          }
        }
      }
      List<GenTable> tableList =
          genTableService.selectDbTableListByNames(
              tableNames.toArray(new String[tableNames.size()]));
      String operName = loginUser.getUsername();
      genTableService.importGenTable(tableList, operName);
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      throw new ServiceException("创建表结构异常");
    }
  }

  /** 修改保存代码生成业务 */
  @PreAuthorize("hasAnyAuthority('tool:gen:edit')")
  @Log(title = "代码生成", businessType = BusinessType.UPDATE)
  @PutMapping
  public GenTable editSave(@Validated @RequestBody GenTable genTable) {
    genTableService.validateEdit(genTable);
    genTableService.updateGenTable(genTable);
    return genTable;
  }

  /** 删除代码生成 */
  @PreAuthorize("hasAnyAuthority('tool:gen:remove')")
  @Log(title = "代码生成", businessType = BusinessType.DELETE)
  @DeleteMapping("/{tableIds}")
  @ResponseStatus(HttpStatus.NO_CONTENT)
  public void remove(@PathVariable Long[] tableIds) {
    genTableService.deleteGenTableByIds(tableIds);
  }

  /** 预览代码 */
  @PreAuthorize("hasAnyAuthority('tool:gen:preview')")
  @GetMapping("/preview/{tableId}")
  public Map<String, String> preview(@PathVariable("tableId") Long tableId) throws IOException {
    return genTableService.previewCode(tableId);
  }

  /** 生成代码（下载方式） */
  @PreAuthorize("hasAnyAuthority('tool:gen:code')")
  @Log(title = "代码生成", businessType = BusinessType.GENCODE)
  @GetMapping("/download/{tableName}")
  public void download(HttpServletResponse response, @PathVariable("tableName") String tableName)
      throws IOException {
    byte[] data = genTableService.downloadCode(tableName);
    genCode(response, data);
  }

  /** 生成代码（自定义路径） */
  @PreAuthorize("hasAnyAuthority('tool:gen:code')")
  @Log(title = "代码生成", businessType = BusinessType.GENCODE)
  @GetMapping("/genCode/{tableName}")
  public void genCode(@PathVariable("tableName") String tableName) {
    genTableService.generatorCode(tableName);
  }

  /** 同步数据库 */
  @PreAuthorize("hasAnyAuthority('tool:gen:edit')")
  @Log(title = "代码生成", businessType = BusinessType.UPDATE)
  @GetMapping("/synchDb/{tableName}")
  public void synchDb(@PathVariable("tableName") String tableName) {
    genTableService.synchDb(tableName);
  }

  /** 批量生成代码 */
  @PreAuthorize("hasAnyAuthority('tool:gen:code')")
  @Log(title = "代码生成", businessType = BusinessType.GENCODE)
  @GetMapping("/batchGenCode")
  public void batchGenCode(HttpServletResponse response, String tables) throws IOException {
    String[] tableNames = Convert.toStrArray(tables);
    byte[] data = genTableService.downloadCode(tableNames);
    genCode(response, data);
  }

  /** 生成 zip 文件 */
  private void genCode(HttpServletResponse response, byte[] data) throws IOException {
    response.reset();
    response.addHeader("Access-Control-Allow-Origin", "*");
    response.addHeader("Access-Control-Expose-Headers", "Content-Disposition");
    response.setHeader("Content-Disposition", "attachment; filename=\"xinjian.zip\"");
    response.addHeader("Content-Length", "" + data.length);
    response.setContentType("application/octet-stream; charset=UTF-8");
    IOUtils.write(data, response.getOutputStream());
  }
}
