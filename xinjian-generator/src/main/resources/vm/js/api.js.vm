// 查询${functionName}列表
export function useApiGet${BusinessName}List(query) {
  return useMyFetch({
    url: '/${moduleName}/${businessName}/list',
    method: 'get',
    params: query
  })
}

// 查询${functionName}详细
export function useApiGet${BusinessName}(${pkColumn.javaField}) {
  return useMyFetch({
    url: '/${moduleName}/${businessName}/' + ${pkColumn.javaField},
    method: 'get'
  })
}

// 新增${functionName}
export function useApiAdd${BusinessName}(data) {
  return useMyFetch({
    url: '/${moduleName}/${businessName}',
    method: 'post',
    data: data
  })
}

// 修改${functionName}
export function useApiUpdate${BusinessName}(data) {
  return useMyFetch({
    url: '/${moduleName}/${businessName}',
    method: 'put',
    data: data
  })
}

// 删除${functionName}
export function useApiDel${BusinessName}(${pkColumn.javaField}) {
  return useMyFetch({
    url: '/${moduleName}/${businessName}/' + ${pkColumn.javaField},
    method: 'delete'
  })
}
