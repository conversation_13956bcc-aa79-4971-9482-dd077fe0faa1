package ${packageName}.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.xinjian.common.annotation.Log;
import com.xinjian.common.core.controller.BaseController;
import org.springframework.http.HttpStatus;
import org.springframework.validation.annotation.Validated;
import com.xinjian.common.enums.BusinessType;
import ${packageName}.domain.${ClassName};
import ${packageName}.service.${ClassName}Service;
import com.xinjian.common.utils.poi.ExcelUtil;
#if($table.crud || $table.sub)
import com.xinjian.common.core.page.TableDataInfo;
#elseif($table.tree)
#end

/**
 * ${functionName}Controller
 *
 * <AUTHOR>
 * @date ${datetime}
 */
@RestController
@RequestMapping("/${moduleName}/${businessName}")
public class ${ClassName}Controller {
    @Autowired
    private ${ClassName}Service ${className}Service;

/**
 * 查询${functionName}列表
 */
@PreAuthorize("hasAnyAuthority('${permissionPrefix}:list')")
@GetMapping("/list")
    #if($table.crud || $table.sub)
    public TableDataInfo<${ClassName}> list(${ClassName} ${className}) {
        startPage();
        List<${ClassName}> list = ${className}Service.select${ClassName}List(${className});
        return new TableDataInfo<>(list);
    }
    #elseif($table.tree)
        public List<${ClassName}> list(${ClassName} ${className}) {
            return ${className}Service.select${ClassName}List(${className});
        }
    #end

    /**
     * 导出${functionName}列表
     */
    @PreAuthorize("hasAnyAuthority('${permissionPrefix}:export')")
    @Log(title = "${functionName}", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ${ClassName} ${className}) {
        List<${ClassName}> list = ${className}Service.select${ClassName}List(${className});
        ExcelUtil<${ClassName}> util = new ExcelUtil<${ClassName}>(${ClassName}. class);
        util.exportExcel(response, list, "${functionName}数据");
    }

    /**
     * 获取${functionName}详细信息
     */
    @PreAuthorize("hasAnyAuthority('${permissionPrefix}:query')")
    @GetMapping(value = "/{${pkColumn.javaField}}")
    public ${ClassName} getInfo(@PathVariable("${pkColumn.javaField}") ${pkColumn.javaType} ${pkColumn.javaField}) {
        return ${className}Service.select${ClassName}By${pkColumn.capJavaField}(${pkColumn.javaField});
    }

    /**
     * 新增${functionName}
     */
    @PreAuthorize("hasAnyAuthority('${permissionPrefix}:add')")
    @Log(title = "${functionName}", businessType = BusinessType.INSERT)
    @PostMapping
    @ResponseStatus(HttpStatus.CREATED)
    public ${ClassName} add(@Validated @RequestBody ${ClassName} ${className}) {
        ${className}.setCreateBy(getUsername());
        if (${className}Service.insert${ClassName}(${className}) > 0) {
            return ${className}Service.select${ClassName}By${pkColumn.capJavaField}(${className}.get${pkColumn.capJavaField}());
        }
        throw new ServiceException("新增${functionName}失败");
    }

    /**
     * 修改${functionName}
     */
    @PreAuthorize("hasAnyAuthority('${permissionPrefix}:edit')")
    @Log(title = "${functionName}", businessType = BusinessType.UPDATE)
    @PutMapping
    public ${ClassName} edit(@Validated @RequestBody ${ClassName} ${className}) {
        ${className}.setUpdateBy(getUsername());
        if (${className}Service.update${ClassName}(${className}) > 0) {
            return ${className}Service.select${ClassName}By${pkColumn.capJavaField}(${className}.get${pkColumn.capJavaField}());
        }
        throw new ServiceException("修改${functionName}失败");
    }

    /**
     * 删除${functionName}
     */
    @PreAuthorize("hasAnyAuthority('${permissionPrefix}:remove')")
    @Log(title = "${functionName}", businessType = BusinessType.DELETE)
    @DeleteMapping("/{${pkColumn.javaField}s}")
    @ResponseStatus(HttpStatus.NO_CONTENT)
    public void remove(@PathVariable ${pkColumn.javaType}[] ${pkColumn.javaField}s) {
        ${className}Service.delete${ClassName}By${pkColumn.capJavaField}s(${pkColumn.javaField}s);
    }
}
