<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="Encoding" native2AsciiForPropertiesFiles="true"
    defaultCharsetForPropertiesFiles="UTF-8">
    <file url="file://$PROJECT_DIR$/xinjian-admin/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/xinjian-admin/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/xinjian-common/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/xinjian-common/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/xinjian-framework/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/xinjian-framework/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/xinjian-generator/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/xinjian-generator/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/xinjian-quartz/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/xinjian-quartz/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/xinjian-system/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/xinjian-system/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/src/main/resources" charset="UTF-8" />
    <file url="PROJECT" charset="UTF-8" />
  </component>
</project>
