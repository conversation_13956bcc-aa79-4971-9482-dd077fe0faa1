<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="CompilerConfiguration">
    <annotationProcessing>
      <profile default="true" name="Default" enabled="true" />
      <profile name="Maven default annotation processors profile" enabled="true">
        <sourceOutputDir name="target/generated-sources/annotations" />
        <sourceTestOutputDir name="target/generated-test-sources/test-annotations" />
        <outputRelativeToContentRoot value="true" />
        <module name="xinjian-common" />
        <module name="xinjian-framework" />
        <module name="xinjian-generator" />
        <module name="xinjian-quartz" />
        <module name="xinjian-admin" />
      </profile>
    </annotationProcessing>
  </component>
  <component name="JavacSettings">
    <option name="ADDITIONAL_OPTIONS_OVERRIDE">
      <module name="xinjian" options="" />
      <module name="xinjian-admin" options="" />
      <module name="xinjian-common" options="" />
      <module name="xinjian-framework" options="" />
      <module name="xinjian-generator" options="" />
      <module name="xinjian-quartz" options="" />
    </option>
  </component>
</project>
