# XinJian-Service-API 项目

## 1. 项目简介

这是一个基于 XinJian 框架构建的后端服务 API 项目。项目采用了模块化的设计思想，将不同的功能解耦到独立的模块中，提高了代码的可维护性和可扩展性。

该项目旨在提供一个通用的后台管理系统解决方案，涵盖了系统管理、监控、代码生成等核心功能。

## 2. 技术栈

- **核心框架:** Spring Boot
- **安全框架:** Spring Security
- **任务调度:** Quartz
- **持久层框架:** MyBatis
- **数据库连接池:** Druid
- **构建工具:** Maven

## 3. 模块说明

本项目包含以下主要模块：

- `xinjian-admin`: 管理后台和项目启动入口。
- `xinjian-common`: 通用工具模块，存放常量、异常、过滤器等。
- `xinjian-framework`: 核心框架模块，集成安全、拦截器等功能。
- `xinjian-system`: 系统核心业务模块，处理用户、角色、菜单等逻辑。
- `xinjian-generator`: 代码生成器模块。
- `xinjian-quartz`: 定时任务模块。
- `xinjian-ui`: (前端) Vue.js 实现的管理界面。（注意：前后分离项目不在此处使用，单独作为一个项目维护）。
- `sql`: 数据库初始化脚本，依次导入 `basic.sql` 和 `quartz.sql`。

## 4. 开发规范

为了确保团队协作开发中的代码风格一致性，本项目引入了 `.editorconfig` 文件。该文件定义了统一的编码规范，如缩进、换行符和字符集等。

大部分现代编辑器和 IDE 会自动识别并应用 `.editorconfig` 的配置。请确保您的开发环境已启用此功能，以保持代码整洁。

## 5. 如何启动

1.  配置 `xinjian-admin/src/main/resources/application.yml` 中的数据库连接信息。
2.  在项目根目录下执行 Maven 构建命令：`mvn clean install`。
3.  运行 `xinjian-admin/src/main/java/com/xinjian/Application.java` 的 `main` 方法启动项目。
4.  API 文档地址：`http://localhost:8123/swagger-ui/index.html`
