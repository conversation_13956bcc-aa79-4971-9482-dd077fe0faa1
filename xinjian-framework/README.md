# `xinjian-framework` 模块

## 1. 模块简介

`xinjian-framework` 是项目的核心框架模块，主要负责集成和配置基础框架功能，如 Spring Security、拦截器和异步任务管理器。

## 2. 核心功能

- **安全控制 (`security`):** 集成了 Spring Security，实现了 JWT (JSON Web Token) 认证过滤器 `JwtAuthenticationTokenFilter`，并定义了认证入口点和登出成功处理器。
- **拦截器 (`interceptor`):** 实现了重复提交拦截器 `RepeatSubmitInterceptor`，防止接口被恶意重复调用。
- **异步任务 (`manager`):** 提供了 `AsyncManager`，用于执行异步任务，并管理线程池的优雅关闭。

## 3. 使用方式

此模块作为底层框架，被 `xinjian-admin` 等上层模块依赖，提供了应用运行所需的基础设施。
