package com.xinjian.framework.aspectj;

import com.xinjian.common.annotation.DataScope;
import com.xinjian.framework.context.DataScopeContextHolder;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.stereotype.Component;

/**
 * 数据权限切面
 *
 * <p>职责：拦截 @DataScope 注解，将其内容存入 ThreadLocal，供 Mybatis-Plus 数据权限插件使用
 */
@Aspect
@Component
public class DataScopeAspect {
  @Around("@annotation(controllerDataScope)")
  public Object around(ProceedingJoinPoint point, DataScope controllerDataScope) throws Throwable {
    try {
      // 将注解存入 ThreadLocal
      DataScopeContextHolder.set(controllerDataScope);
      // 执行原始方法
      return point.proceed();
    } finally {
      // 方法执行完毕后，清理 ThreadLocal，防止内存泄漏
      DataScopeContextHolder.clear();
    }
  }
}
