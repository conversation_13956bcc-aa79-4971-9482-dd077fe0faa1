package com.xinjian.framework.web.exception;

import java.net.URI;
import java.util.stream.Collectors;
import javax.annotation.Nonnull;
import javax.annotation.Nullable;
import javax.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.context.request.NativeWebRequest;
import org.zalando.problem.AbstractThrowableProblem;
import org.zalando.problem.DefaultProblem;
import org.zalando.problem.Problem;
import org.zalando.problem.ProblemBuilder;
import org.zalando.problem.Status;
import org.zalando.problem.spring.web.advice.ProblemHandling;
import org.zalando.problem.spring.web.advice.security.SecurityAdviceTrait;
import org.zalando.problem.violations.ConstraintViolationProblem;
import org.zalando.problem.violations.Violation;

/** 全局异常处理器 */
@Slf4j
@RestControllerAdvice
public class GlobalExceptionHandler implements ProblemHandling, SecurityAdviceTrait {

  @Override
  public ResponseEntity<Problem> process(
      @Nullable ResponseEntity<Problem> entity, NativeWebRequest request) {
    if (entity == null) {
      return null;
    }
    Problem problem = entity.getBody();
    if (!(problem instanceof ConstraintViolationProblem || problem instanceof DefaultProblem)) {
      return entity;
    }

    // 安全地获取请求 URI
    String requestUri = "/";
    try {
      HttpServletRequest httpRequest = request.getNativeRequest(HttpServletRequest.class);
      if (httpRequest != null && httpRequest.getRequestURI() != null) {
        requestUri = httpRequest.getRequestURI();
      }
    } catch (Exception e) {
      log.debug("无法获取请求 URI，使用默认值", e);
    }

    URI problemType = problem.getType();
    if (problemType == null || Problem.DEFAULT_TYPE.equals(problemType)) {
      if (problem.getStatus() != null) {
        String reasonPhrase = problem.getStatus().getReasonPhrase().toLowerCase();
        problemType = URI.create("urn:error:" + reasonPhrase.replace(" ", "-"));
      } else {
        problemType = Problem.DEFAULT_TYPE;
      }
    }

    ProblemBuilder builder =
        Problem.builder()
            .withType(problemType)
            .withTitle(problem.getTitle())
            .withStatus(problem.getStatus())
            .withDetail(problem.getDetail())
            .withInstance(URI.create(requestUri));

    if (problem instanceof ConstraintViolationProblem) {
      builder.with("errors", ((ConstraintViolationProblem) problem).getViolations());
    }
    return new ResponseEntity<>(builder.build(), entity.getHeaders(), entity.getStatusCode());
  }

  @Override
  public ResponseEntity<Problem> handleMethodArgumentNotValid(
      MethodArgumentNotValidException ex, @Nonnull NativeWebRequest request) {
    BindingResult result = ex.getBindingResult();

    Problem problem =
        new ConstraintViolationProblem(
            Status.UNPROCESSABLE_ENTITY,
            result.getFieldErrors().stream()
                .map(
                    fieldError ->
                        new Violation(fieldError.getField(), fieldError.getDefaultMessage()))
                .collect(Collectors.toList()));

    return create(ex, problem, request);
  }

  /** 统一处理所有继承自 AbstractThrowableProblem 的自定义异常 */
  @ExceptionHandler
  public ResponseEntity<Problem> handleAbstractThrowableProblem(
      AbstractThrowableProblem ex, NativeWebRequest request) {

    if (log.isDebugEnabled()) {
      log.debug("处理自定义异常：{} - {}", ex.getClass().getSimpleName(), ex.getDetail());
    }

    Problem problem =
        Problem.builder()
            .withStatus(ex.getStatus())
            .withTitle(ex.getTitle())
            .withDetail(ex.getDetail())
            .build();

    return create(ex, problem, request);
  }

  /**
   * 兜底异常处理器 - 处理所有未被其他处理器捕获的异常 注意：ProblemHandling 接口已经处理了大部分常见的异常（包括很多会产生 500 的异常）
   * 这个方法主要处理一些边缘情况和第三方库的异常
   */
  // @ExceptionHandler(Exception.class)
  // public ResponseEntity<Problem> handleGenericException(Exception ex, NativeWebRequest request) {

  //   log.error("未被其他处理器捕获的异常：{}", ex.getClass().getSimpleName(), ex);

  //   Problem problem =
  //       Problem.builder()
  //           .withTitle("Internal Server Error")
  //           .withStatus(Status.INTERNAL_SERVER_ERROR)
  //           .withDetail("系统内部错误，请稍后重试")
  //           .build();

  //   return create(ex, problem, request);
  // }
}
