package com.xinjian.framework.web.service;

import com.xinjian.common.core.domain.entity.SysUser;
import com.xinjian.common.service.ISysMenuService;
import com.xinjian.common.service.ISysRoleService;
import com.xinjian.common.utils.SecurityUtils;
import java.util.HashSet;
import java.util.Set;
import java.util.stream.Collectors;
import javax.validation.constraints.NotNull;
import org.springframework.stereotype.Component;
import org.springframework.validation.annotation.Validated;

/** 用户权限服务，用于获取用户的角色和菜单权限信息 */
@Validated
@Component
public class SysPermissionService {

  private final ISysRoleService roleService;
  private final ISysMenuService menuService;

  public SysPermissionService(ISysRoleService roleService, ISysMenuService menuService) {
    this.roleService = roleService;
    this.menuService = menuService;
  }

  /**
   * 获取用户的角色权限标识集合
   *
   * @param user 用户信息，不能为 null
   * @return 角色权限标识集合，带 ROLE_ 前缀
   */
  public Set<String> getRolePermission(@NotNull(message = "用户信息不能为空") SysUser user) {
    Set<String> roles = new HashSet<>();
    if (SecurityUtils.isAdminUser(user.getUserId())) {
      roles.add("ROLE_ADMIN");
    } else {
      // 为角色添加 ROLE_ 前缀，符合 Spring Security 约定
      roles.addAll(
          roleService.selectRolePermissionByUserId(user.getUserId()).stream()
              .map(role -> "ROLE_" + role)
              .collect(Collectors.toSet()));
    }
    return roles;
  }

  /**
   * 获取用户的菜单权限标识集合
   *
   * @param user 用户信息，不能为 null
   * @return 菜单权限标识集合
   */
  public Set<String> getMenuPermission(@NotNull(message = "用户信息不能为空") SysUser user) {
    Set<String> perms = new HashSet<>();
    if (SecurityUtils.isAdminUser(user.getUserId())) {
      // 管理员拥有所有实际存在的权限
      // perms.add("*:*:*"); // 原有的通配符权限 Spring Security 不支持
      perms.addAll(menuService.selectAllMenuPerms()); // 添加所有实际存在的权限 = 通配符权限
    } else {
      // 直接根据用户 ID 查询菜单权限
      perms.addAll(menuService.selectMenuPermsByUserId(user.getUserId()));
    }
    return perms;
  }

  /**
   * 获取用户的所有权限（角色 + 菜单权限）
   *
   * @param user 用户信息，不能为 null
   * @return 所有权限集合
   */
  public Set<String> getAllPermissions(@NotNull(message = "用户信息不能为空") SysUser user) {
    Set<String> allPermissions = new HashSet<>();

    // 添加角色权限
    allPermissions.addAll(getRolePermission(user));

    // 添加菜单权限
    allPermissions.addAll(getMenuPermission(user));

    return allPermissions;
  }
}
