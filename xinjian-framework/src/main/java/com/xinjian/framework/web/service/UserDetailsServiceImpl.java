package com.xinjian.framework.web.service;

import com.xinjian.common.core.domain.dto.LoginUser;
import com.xinjian.common.core.domain.entity.SysUser;
import com.xinjian.common.enums.UserStatus;
import com.xinjian.common.exception.status403.ForbiddenException;
import com.xinjian.common.exception.status500.ServiceException;
import com.xinjian.common.service.ISysUserService;
import java.util.Objects;
import java.util.Set;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

/**
 * Spring Security 用户验证处理服务
 *
 * <p>在用户认证过程中加载用户特定数据，根据用户名从数据库中检索用户，检查其状态，并创建一个包含完整权限信息的 UserDetails 对象
 */
@Slf4j
@Service
public class UserDetailsServiceImpl implements UserDetailsService {

  private final ISysUserService userService;
  private final SysPermissionService permissionService;

  public UserDetailsServiceImpl(
      ISysUserService userService, SysPermissionService permissionService) {
    this.userService = userService;
    this.permissionService = permissionService;
  }

  /**
   * 当用户尝试登录时，通过用户名获取用户的详细信息 1.根据用户名查找用户 2.验证用户状态 3.如果用户有效，则为其构建一个包含完整授权信息的 {@link LoginUser} 对象
   *
   * @param username 待认证的用户名
   * @return 一个包含用户核心信息和权限的 {@link UserDetails} 对象
   * @throws UsernameNotFoundException 如果用户名为空或用户在数据库中不存在
   * @throws ForbiddenException 如果用户账户状态异常（如被删除或停用）
   */
  @Override
  public UserDetails loadUserByUsername(String username) throws UsernameNotFoundException {
    try {
      if (!StringUtils.hasText(username)) {
        log.warn("用户名为空，认证失败");
        throw new UsernameNotFoundException("用户名或密码错误");
      }

      log.debug("开始加载用户详情，用户名：{}", username);
      // 步骤 1: 根据用户名查找用户
      SysUser user = findUserByUsername(username);
      // 步骤 2: 验证用户状态
      validateUserStatus(user);
      // 步骤 3: 构建并返回包含用户详细信息（包括权限）的 LoginUser 对象
      UserDetails userDetails = buildLoginUser(user);
      log.info("成功加载用户详情，用户名：{}, 用户 ID: {}", username, user.getUserId());

      return userDetails;
    } catch (UsernameNotFoundException | ForbiddenException e) {
      // 重新抛出已知的异常
      throw e;
    } catch (Exception e) {
      log.error("加载用户详情时发生未知错误，用户名：{}, 错误：{}", username, e.getMessage(), e);
      // 记录异常，抛出通用的认证失败异常，不要向客户端明确提示错误细节
      throw new UsernameNotFoundException("用户名或密码错误");
    }
  }

  /**
   * 根据用户名查找用户
   *
   * @param username 待查找的用户名
   * @return 查找到的 {@link SysUser} 实体
   * @throws UsernameNotFoundException 如果用户名为空或用户不存在
   */
  private SysUser findUserByUsername(String username) throws UsernameNotFoundException {
    try {
      log.debug("正在根据用户名查找用户：{}", username);

      SysUser user = userService.selectUserByUserName(username);
      if (Objects.isNull(user)) {
        log.info("登录认证失败：用户 '{}' 不存在", username);
        // 记录异常，抛出通用的认证失败异常，不要向客户端明确提示错误细节
        throw new UsernameNotFoundException("用户名或密码错误");
      }

      log.debug("成功找到用户：{}, 用户 ID: {}", username, user.getUserId());
      return user;
    } catch (Exception e) {
      if (e instanceof UsernameNotFoundException) {
        throw e;
      }
      log.error("查找用户时发生错误，用户名：{}, 错误：{}", username, e.getMessage(), e);
      // 记录异常，抛出通用的认证失败异常，不要向客户端明确提示错误细节
      throw new UsernameNotFoundException("用户名或密码错误");
    }
  }

  /**
   * 验证用户状态
   *
   * @param user 系统用户实体
   * @throws ForbiddenException 如果账户状态异常
   */
  private void validateUserStatus(SysUser user) throws ForbiddenException {
    try {
      log.debug("正在验证用户状态，用户名：{}", user.getUserName());

      if (user.getIsDeleted()) {
        log.warn("用户 '{}' 的账号已被删除，认证失败", user.getUserName());
        throw new ForbiddenException("您的账号已被删除，请联系管理员");
      }

      if (UserStatus.DISABLE.getCode().equals(user.getStatus())) {
        log.warn("用户 '{}' 的账号已被停用，认证失败", user.getUserName());
        throw new ForbiddenException("您的账号已被停用，请联系管理员");
      }

      log.debug("用户状态验证通过，用户名：{}", user.getUserName());
    } catch (ForbiddenException e) {
      throw e;
    } catch (Exception e) {
      log.error("验证用户状态时发生错误，用户名：{}, 错误：{}", user.getUserName(), e.getMessage(), e);
      throw new ForbiddenException("账号状态验证失败，请联系管理员");
    }
  }

  /**
   * 构建登录用户
   *
   * @param user 系统用户实体
   * @return 包含用户核心信息和权限的 LoginUser 对象
   * @throws RuntimeException 如果构建登录用户失败
   */
  private UserDetails buildLoginUser(SysUser user) throws RuntimeException {
    try {
      log.debug("正在构建登录用户，用户名：{}", user.getUserName());

      // 获取用户所有权限（角色 + 菜单权限）
      Set<String> permissions = permissionService.getAllPermissions(user);
      log.debug("成功获取用户权限，用户名：{}, 权限数量：{}", user.getUserName(), permissions.size());

      // 构建登录用户对象，只包含认证和授权所需的核心信息
      LoginUser loginUser =
          new LoginUser(
              user.getUserId(),
              user.getDeptId(),
              user.getUserName(),
              user.getPassword(),
              !UserStatus.DISABLE.getCode().equals(user.getStatus()),
              permissions);

      log.debug("成功构建登录用户，用户名：{}", user.getUserName());
      return loginUser;
    } catch (Exception e) {
      log.error("构建登录用户时发生错误，用户名：{}, 错误：{}", user.getUserName(), e.getMessage(), e);
      throw new ServiceException("构建用户信息失败，请联系管理员");
    }
  }
}
