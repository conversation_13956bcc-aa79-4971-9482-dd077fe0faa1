package com.xinjian.framework.web.filter;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import javax.servlet.*;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.apache.commons.lang3.StringUtils;

/** 防止 XSS 攻击的过滤器 */
public class XssFilter implements Filter {
  /** 排除链接 */
  public List<String> excludes = new ArrayList<>();

  @Override
  public void init(FilterConfig filterConfig) throws ServletException {
    String tempExcludes = filterConfig.getInitParameter("excludes");
    if (StringUtils.isNotBlank(tempExcludes)) {
      String[] urls = tempExcludes.split(",");
      for (String url : urls) {
        excludes.add(url);
      }
    }
  }

  @Override
  public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain)
      throws IOException, ServletException {
    HttpServletRequest req = (HttpServletRequest) request;
    HttpServletResponse resp = (HttpServletResponse) response;
    if (handleExcludeURL(req, resp)) {
      chain.doFilter(request, response);
      return;
    }
    XssHttpServletRequestWrapper xssRequest =
        new XssHttpServletRequestWrapper((HttpServletRequest) request);
    chain.doFilter(xssRequest, response);
  }

  private boolean handleExcludeURL(HttpServletRequest request, HttpServletResponse response) {
    String url = request.getServletPath();
    String method = request.getMethod();
    // GET DELETE 不过滤
    if (method == null || "GET".equals(method) || "DELETE".equals(method)) {
      return true;
    }
    return matches(url, excludes);
  }

  /**
   * 查找指定字符串是否匹配指定字符串列表中的任意一个字符串
   *
   * @param url 指定字符串
   * @param patterns 需要检查的字符串数组
   * @return 是否匹配
   */
  private boolean matches(String url, List<String> patterns) {
    if (org.apache.commons.lang3.StringUtils.isEmpty(url)
        || patterns == null
        || patterns.isEmpty()) {
      return false;
    }
    org.springframework.util.AntPathMatcher matcher = new org.springframework.util.AntPathMatcher();
    for (String pattern : patterns) {
      if (matcher.match(pattern, url)) {
        return true;
      }
    }
    return false;
  }

  @Override
  public void destroy() {}
}
