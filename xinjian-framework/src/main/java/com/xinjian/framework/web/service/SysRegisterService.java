package com.xinjian.framework.web.service;

import com.xinjian.captcha.service.ICaptchaService;
import com.xinjian.common.core.domain.dto.RegisterRequest;
import com.xinjian.common.core.domain.entity.SysUser;
import com.xinjian.common.exception.status400.InvalidInputDataException;
import com.xinjian.common.exception.status409.ConflictException;
import com.xinjian.common.exception.status500.ServiceException;
import com.xinjian.common.service.ISysUserService;
import com.xinjian.framework.manager.AsyncManager;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Component;
import org.springframework.validation.annotation.Validated;

/** 提供用户注册服务：输入验证、验证码校验和用户创建 */
@Component
@RequiredArgsConstructor
public class SysRegisterService {

  private final ISysUserService userService;
  private final ICaptchaService captchaService;
  private final AsyncManager asyncManager;
  private final PasswordEncoder passwordEncoder;

  /**
   * 处理用户注册请求
   *
   * @param registerBody 包含注册信息的请求体，不能为空
   * @throws InvalidInputDataException 如果输入数据（如用户名、密码、验证码）不合法
   * @throws ConflictException 如果用户名已被占用
   * @throws RuntimeException 如果发生未预期的注册失败
   */
  public void register(@Validated RegisterRequest registerBody)
      throws ConflictException, RuntimeException {
    // 1. 基本输入验证
    String[] validatedInputs = validateRegistrationInput(registerBody);
    String username = validatedInputs[0];
    String password = validatedInputs[1];

    // 2. 验证码校验
    captchaService.validate(registerBody.getUuid(), registerBody.getCode());

    // 3. 用户名和密码验证（通过注解验证，无需手动验证）

    // 4. 检查用户名可用性
    checkUsernameAvailability(username);

    // 5. 创建用户实体
    SysUser sysUser = createUserEntity(username, password);

    // 6. 保存用户
    saveUser(sysUser);

    // 7. 记录日志
    asyncManager.recordLogininfor(username, "Register", "用户注册成功");
  }

  /**
   * 验证注册输入
   *
   * @param registerBody 注册信息
   * @return 包含用户名和密码的数组
   */
  private String[] validateRegistrationInput(RegisterRequest registerBody) {
    String username = registerBody.getUsername();
    String password = registerBody.getPassword();
    String code = registerBody.getCode();
    String uuid = registerBody.getUuid();

    return new String[] {username, password};
  }

  /**
   * 检查用户名可用性
   *
   * @param username 用户名
   * @throws ConflictException 如果用户名已被占用
   * @throws InvalidInputDataException 如果用户名为空
   */
  private void checkUsernameAvailability(String username)
      throws ConflictException, InvalidInputDataException {
    if (StringUtils.isBlank(username)) {
      throw new InvalidInputDataException("用户名不能为空");
    }

    SysUser sysUser = new SysUser();
    sysUser.setUserName(username);
    if (!userService.checkUserNameUnique(sysUser)) {
      throw new ConflictException("注册账号已存在");
    }
  }

  /**
   * 创建用户实体
   *
   * @param username 用户名
   * @param password 密码
   * @return 用户实体
   * @throws InvalidInputDataException 如果用户名或密码为空
   */
  private SysUser createUserEntity(String username, String password)
      throws InvalidInputDataException {
    if (StringUtils.isBlank(username)) {
      throw new InvalidInputDataException("用户名不能为空");
    }
    if (StringUtils.isBlank(password)) {
      throw new InvalidInputDataException("密码不能为空");
    }

    SysUser sysUser = new SysUser();
    sysUser.setUserName(username);
    sysUser.setNickName(username);
    sysUser.setPassword(passwordEncoder.encode(password));
    return sysUser;
  }

  /**
   * 保存用户
   *
   * @param sysUser 用户实体
   * @throws RuntimeException 如果注册失败
   * @throws InvalidInputDataException 如果用户实体为空
   */
  private void saveUser(SysUser sysUser) throws RuntimeException, InvalidInputDataException {
    if (sysUser == null) {
      throw new InvalidInputDataException("用户信息不能为空");
    }

    boolean regFlag = userService.registerUser(sysUser);
    if (!regFlag) {
      throw new ServiceException("注册失败，请联系系统管理员");
    }
  }
}
