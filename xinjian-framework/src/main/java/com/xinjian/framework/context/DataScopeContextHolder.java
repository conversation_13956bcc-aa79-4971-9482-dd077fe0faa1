package com.xinjian.framework.context;

import com.xinjian.common.annotation.DataScope;

/**
 * 数据权限上下文持有器
 *
 * <p>使用 ThreadLocal 在 AOP 切面和 MyBatis-Plus 数据权限处理器之间传递注解信息
 */
public final class DataScopeContextHolder {

  private static final ThreadLocal<DataScope> CONTEXT_HOLDER = new ThreadLocal<>();

  /**
   * 设置数据权限注解
   *
   * @param dataScope 数据权限注解实例
   */
  public static void set(DataScope dataScope) {
    CONTEXT_HOLDER.set(dataScope);
  }

  /**
   * 获取数据权限注解
   *
   * @return DataScope 实例，可能为 null
   */
  public static DataScope get() {
    return CONTEXT_HOLDER.get();
  }

  /** 清理上下文 */
  public static void clear() {
    CONTEXT_HOLDER.remove();
  }
}
