package com.xinjian.framework.config;

import java.nio.charset.StandardCharsets;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.converter.StringHttpMessageConverter;
import org.springframework.web.client.RestTemplate;

/** RestTemplate 配置 */
@Configuration
public class RestTemplateConfiguration {

  /** 创建 RestTemplate Bean */
  @Bean
  public RestTemplate restTemplate() {
    RestTemplate restTemplate = new RestTemplate();
    // 设置默认的字符串编码为 UTF-8
    restTemplate
        .getMessageConverters()
        .set(1, new StringHttpMessageConverter(StandardCharsets.UTF_8));
    return restTemplate;
  }
}
