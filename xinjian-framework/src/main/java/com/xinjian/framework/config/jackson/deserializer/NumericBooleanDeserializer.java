package com.xinjian.framework.config.jackson.deserializer;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.JsonToken;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import java.io.IOException;

/** 自定义 Boolean 反序列化器 兼容将数字 1/0 或字符串 "1"/"0" 转换为 Boolean 类型的 true/false。 */
public class NumericBooleanDeserializer extends JsonDeserializer<Boolean> {

  @Override
  public Boolean deserialize(JsonParser p, DeserializationContext ctxt) throws IOException {
    if (p.hasToken(JsonToken.VALUE_TRUE)) {
      return true;
    }
    if (p.hasToken(JsonToken.VALUE_FALSE)) {
      return false;
    }
    if (p.hasToken(JsonToken.VALUE_NUMBER_INT)) {
      return p.getIntValue() != 0;
    }
    if (p.hasToken(JsonToken.VALUE_STRING)) {
      String text = p.getText().trim();
      if ("1".equals(text) || "true".equalsIgnoreCase(text)) {
        return true;
      }
      if ("0".equals(text) || "false".equalsIgnoreCase(text)) {
        return false;
      }
    }
    return null;
  }
}
