package com.xinjian.framework.config;

import com.xinjian.common.properties.ApiProperties;
import com.xinjian.common.properties.StorageProperties;
import com.xinjian.framework.security.filter.JwtAuthenticationTokenFilter;
import com.xinjian.framework.security.handle.LogoutSuccessHandlerImpl;
import java.util.stream.Stream;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.http.HttpMethod;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.config.annotation.authentication.configuration.AuthenticationConfiguration;
import org.springframework.security.config.annotation.method.configuration.EnableMethodSecurity;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.annotation.web.configurers.AbstractHttpConfigurer;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;
import org.springframework.security.web.util.matcher.AntPathRequestMatcher;
import org.springframework.security.web.util.matcher.RequestMatcher;
import org.springframework.web.filter.CorsFilter;
import org.zalando.problem.spring.web.advice.security.SecurityProblemSupport;

/**
 * Spring Security 配置
 *
 * <AUTHOR>
 */
@Configuration
@EnableWebSecurity
@EnableMethodSecurity(prePostEnabled = true, securedEnabled = true)
@Import(SecurityProblemSupport.class)
@RequiredArgsConstructor
public class SecurityConfiguration {

  /** 静态资源白名单，这些资源将完全绕过 Spring Security 过滤器链 */
  private static final String[] STATIC_RESOURCES_TO_IGNORE = {
    "/v3/api-docs/**", "/swagger-ui/**", "/swagger-ui.html", "/druid/**",
  };

  /** 公开 API 端点白名单，这些路径将在过滤器链内被放行，无需认证 */
  private static final String[] PUBLIC_API_ENDPOINTS = {"/login", "/register", "/captcha"};

  /** 登出成功处理器 */
  private final LogoutSuccessHandlerImpl logoutSuccessHandler;

  /** JWT 认证过滤器 */
  private final JwtAuthenticationTokenFilter authenticationTokenFilter;

  /** 跨域过滤器 */
  private final CorsFilter corsFilter;

  private final SecurityProblemSupport problemSupport;

  /** 配置密码加密器，使用 BCrypt 强哈希算法 */
  @Bean
  public BCryptPasswordEncoder bCryptPasswordEncoder() {
    return new BCryptPasswordEncoder();
  }

  /** 身份认证管理器，用于处理认证请求 */
  @Bean
  public AuthenticationManager authenticationManager(
      AuthenticationConfiguration authenticationConfiguration) throws Exception {
    return authenticationConfiguration.getAuthenticationManager();
  }

  /** 配置核心安全过滤器链 */
  @Bean
  public SecurityFilterChain filterChain(
      HttpSecurity http, ApiProperties apiProperties, StorageProperties storageProperties)
      throws Exception {
    // 根据配置构建 API 前缀
    String apiPrefix = apiProperties.getBaseUrl();
    String[] prefixedPublicApis =
        Stream.of(PUBLIC_API_ENDPOINTS).map(url -> apiPrefix + url).toArray(String[]::new);

    // 将字符串路径数组转换为 RequestMatcher 对象数组，以供 requestMatchers 方法使用
    RequestMatcher[] publicMatchers =
        Stream.of(prefixedPublicApis)
            .map(AntPathRequestMatcher::new)
            .toArray(RequestMatcher[]::new);

    http
        // 禁用 CSRF（跨站请求伪造）保护，因为我们使用 JWT，不需要 Session
        .csrf(AbstractHttpConfigurer::disable)
        // 配置会话管理策略为无状态（STATELESS），服务器不维护会话
        .sessionManagement(
            session -> session.sessionCreationPolicy(SessionCreationPolicy.STATELESS))
        // 配置请求授权规则
        .authorizeHttpRequests(
            auth ->
                auth.requestMatchers(publicMatchers)
                    .permitAll() // 对公开 API 端点允许匿名访问
                    .requestMatchers(
                        new AntPathRequestMatcher(storageProperties.getEndpoint() + "/**"))
                    .permitAll() // 对上传的静态资源允许匿名访问
                    .requestMatchers(new AntPathRequestMatcher("/actuator/**"))
                    .permitAll() // 放行 Spring Boot Actuator 监控端点
                    .requestMatchers(new AntPathRequestMatcher("/**", HttpMethod.OPTIONS.name()))
                    .permitAll() // 对所有 OPTIONS 预检请求允许匿名访问
                    .anyRequest()
                    .authenticated()) // 其他所有请求都需要身份验证
        // 配置异常处理，使用 problem-spring-web 提供的 SecurityProblemSupport
        // authenticationEntryPoint 用于处理认证失败（401 Unauthorized）
        // accessDeniedHandler 用于处理授权失败（403 Forbidden）
        .exceptionHandling(
            exception ->
                exception
                    .authenticationEntryPoint(problemSupport)
                    .accessDeniedHandler(problemSupport))
        // 配置登出逻辑
        .logout(
            logout ->
                logout.logoutUrl(apiPrefix + "/logout").logoutSuccessHandler(logoutSuccessHandler))
        // 将 CORS 过滤器和 JWT 过滤器都添加到 UsernamePasswordAuthenticationFilter 之前
        // Spring Security 会按照添加顺序执行，所以 CORS 过滤器会先于 JWT 过滤器执行
        .addFilterBefore(corsFilter, UsernamePasswordAuthenticationFilter.class)
        .addFilterBefore(authenticationTokenFilter, UsernamePasswordAuthenticationFilter.class);

    return http.build();
  }

  /** Web 安全性自定义，配置静态资源，使其不经过 Spring Security 的过滤器链 */
  @Bean
  public org.springframework.security.config.annotation.web.configuration.WebSecurityCustomizer
      webSecurityCustomizer() {
    // 将字符串路径数组转换为 RequestMatcher 对象数组
    RequestMatcher[] staticMatchers =
        Stream.of(STATIC_RESOURCES_TO_IGNORE)
            .map(AntPathRequestMatcher::new)
            .toArray(RequestMatcher[]::new);

    return web -> web.ignoring().requestMatchers(staticMatchers);
  }
}
