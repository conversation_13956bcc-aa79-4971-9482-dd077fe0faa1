package com.xinjian.framework.config;

import com.xinjian.common.properties.XssProperties;
import com.xinjian.framework.web.filter.RepeatableFilter;
import com.xinjian.framework.web.filter.XssFilter;
import java.util.HashMap;
import java.util.Map;
import javax.servlet.DispatcherType;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/** Filter 配置 */
@Configuration
public class FilterConfiguration {

  @Autowired private XssProperties xssProperties;

  @SuppressWarnings({"rawtypes", "unchecked"})
  @Bean
  @ConditionalOnProperty(value = "app.xss.enabled", havingValue = "true")
  public FilterRegistrationBean xssFilterRegistration() {
    FilterRegistrationBean registration = new FilterRegistrationBean();
    registration.setDispatcherTypes(DispatcherType.REQUEST);
    registration.setFilter(new XssFilter());
    registration.addUrlPatterns(StringUtils.split(xssProperties.getUrlPatterns(), ","));
    registration.setName("xssFilter");
    registration.setOrder(FilterRegistrationBean.HIGHEST_PRECEDENCE);
    Map<String, String> initParameters = new HashMap<String, String>();
    initParameters.put("excludes", xssProperties.getExcludes());
    registration.setInitParameters(initParameters);
    return registration;
  }

  @SuppressWarnings({"rawtypes", "unchecked"})
  @Bean
  public FilterRegistrationBean someFilterRegistration() {
    FilterRegistrationBean registration = new FilterRegistrationBean();
    registration.setFilter(new RepeatableFilter());
    registration.addUrlPatterns("/*");
    registration.setName("repeatableFilter");
    registration.setOrder(FilterRegistrationBean.LOWEST_PRECEDENCE);
    return registration;
  }
}
