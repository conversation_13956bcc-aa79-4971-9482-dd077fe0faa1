package com.xinjian.framework.config;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.module.SimpleModule;
import com.xinjian.framework.config.jackson.deserializer.NumericBooleanDeserializer;
import java.util.TimeZone;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.http.converter.json.Jackson2ObjectMapperBuilder;
import org.zalando.problem.jackson.ProblemModule;
import org.zalando.problem.violations.ConstraintViolationProblemModule;

@Configuration
public class JacksonConfiguration {

  /**
   * 定义一个唯一的、权威的、完全定制化的 ObjectMapper Bean。
   *
   * @param builder Spring Boot 自动配置好的 Jackson2ObjectMapperBuilder，它已经包含了所有基础配置。
   * @return 完全配置好的 ObjectMapper @Primary 注解确保了 Spring 容器中所有的依赖注入都会优先使用这个我们自定义的 Bean。
   */
  @Bean
  @Primary
  public ObjectMapper objectMapper(Jackson2ObjectMapperBuilder builder) {

    // 1. 从 Spring Boot 提供的 builder 开始，而不是手动 new ObjectMapper()
    // 这样可以继承 Spring Boot 的所有默认优秀配置（如日期格式化、对 Optional 的支持等）
    ObjectMapper objectMapper = builder.createXmlMapper(false).build();

    // 2. 为 ObjectMapper 注册 Zalando Problem (RFC 7807) 支持模块
    // withStackTraces() 可以根据需要开启或关闭
    objectMapper.registerModule(new ProblemModule());
    objectMapper.registerModule(new ConstraintViolationProblemModule());

    // 3. 为 ObjectMapper 注册我们自定义的布尔值反序列化模块
    SimpleModule customBooleanModule = new SimpleModule();
    customBooleanModule.addDeserializer(Boolean.class, new NumericBooleanDeserializer());
    customBooleanModule.addDeserializer(boolean.class, new NumericBooleanDeserializer());
    objectMapper.registerModule(customBooleanModule);

    // 4. 设置默认时区
    objectMapper.setTimeZone(TimeZone.getDefault());

    return objectMapper;
  }
}
