package com.xinjian.framework.auditor;

import com.xinjian.common.core.domain.dto.LoginUser;
import com.xinjian.common.utils.SecurityUtils;
import com.xinjian.starter.mybatis.auditor.UserAuditor;
import org.springframework.stereotype.Component;

/** 基于 Spring Security 的 UserAuditor 实现 (适配器) */
@Component
public class SecurityUserAuditorImpl implements UserAuditor {

  @Override
  public Long getCurrentUserId() {
    try {
      LoginUser loginUser = SecurityUtils.getLoginUser();
      if (loginUser != null) {
        return loginUser.getUserId();
      }
    } catch (Exception e) {
      // 异常情况下，可以返回一个默认值或 null
      return 0L;
    }
    return 0L;
  }
}
