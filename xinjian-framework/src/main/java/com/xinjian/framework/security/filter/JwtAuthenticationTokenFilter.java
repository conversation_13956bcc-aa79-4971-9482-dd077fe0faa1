package com.xinjian.framework.security.filter;

import com.xinjian.common.core.domain.dto.LoginUser;
import com.xinjian.common.utils.SecurityUtils;
import com.xinjian.framework.web.service.TokenService;
import java.io.IOException;
import java.util.Objects;
import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.web.authentication.WebAuthenticationDetailsSource;
import org.springframework.stereotype.Component;
import org.springframework.web.filter.OncePerRequestFilter;

/** token 过滤器 验证 token 有效性 */
@Component
public class JwtAuthenticationTokenFilter extends OncePerRequestFilter {
  @Autowired private TokenService tokenService;

  @Override
  protected void doFilterInternal(
      HttpServletRequest request, HttpServletResponse response, FilterChain chain)
      throws ServletException, IOException {
    LoginUser loginUser = null;
    try {
      loginUser = tokenService.getLoginUser(request);
    } catch (RuntimeException e) {
      // 对于 token 无效或已过期的情况，忽略异常，让请求继续作为匿名访问处理，无需记录日志

      // 该日志只在调试时使用
      // logger.debug("无法解析 JWT 令牌，请求将作为匿名处理：{}", e.getMessage());
    }
    if (Objects.nonNull(loginUser) && Objects.isNull(SecurityUtils.getAuthentication())) {
      tokenService.verifyToken(loginUser);
      UsernamePasswordAuthenticationToken authenticationToken =
          new UsernamePasswordAuthenticationToken(loginUser, null, loginUser.getAuthorities());
      authenticationToken.setDetails(new WebAuthenticationDetailsSource().buildDetails(request));
      SecurityContextHolder.getContext().setAuthentication(authenticationToken);
    }
    chain.doFilter(request, response);
  }
}
