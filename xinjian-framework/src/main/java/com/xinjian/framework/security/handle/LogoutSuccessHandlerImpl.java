package com.xinjian.framework.security.handle;

import com.xinjian.common.core.domain.dto.LoginUser;
import com.xinjian.common.core.domain.dto.UserSessionInfo;
import com.xinjian.framework.manager.AsyncManager;
import com.xinjian.framework.web.service.TokenService;
import java.io.IOException;
import java.util.Objects;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.core.Authentication;
import org.springframework.security.web.authentication.logout.LogoutSuccessHandler;

/** 自定义退出处理类 返回成功 */
@Configuration
@RequiredArgsConstructor
public class LogoutSuccessHandlerImpl implements LogoutSuccessHandler {

  private final TokenService tokenService;
  private final AsyncManager asyncManager;

  /**
   * 退出处理
   *
   * @return
   */
  @Override
  public void onLogoutSuccess(
      HttpServletRequest request, HttpServletResponse response, Authentication authentication)
      throws IOException, ServletException {
    LoginUser loginUser = tokenService.getLoginUser(request);
    if (Objects.nonNull(loginUser)) {
      String userName = loginUser.getUsername();

      // 获取会话信息以获取 token
      UserSessionInfo sessionInfo = tokenService.getUserSessionInfo(request);
      if (Objects.nonNull(sessionInfo)) {
        // 删除用户缓存记录
        tokenService.delLoginUser(sessionInfo.getToken());
      }

      // 记录用户退出日志
      asyncManager.recordLogininfor(userName, "Logout", "用户退出登录成功");
    }

    // 设置响应状态码为 204 表示成功但没有返回内容
    response.setStatus(HttpServletResponse.SC_NO_CONTENT);
  }
}
