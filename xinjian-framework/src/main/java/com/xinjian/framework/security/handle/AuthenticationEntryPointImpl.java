package com.xinjian.framework.security.handle;

import com.xinjian.common.exception.status403.ForbiddenException;
import com.xinjian.common.utils.ServletUtils;
import com.xinjian.common.utils.jackson.JacksonUtil;
import java.io.IOException;
import java.io.Serializable;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.web.AuthenticationEntryPoint;
import org.springframework.stereotype.Component;

/** 认证失败处理类 返回未授权 */
@Component
public class AuthenticationEntryPointImpl implements AuthenticationEntryPoint, Serializable {
  private static final long serialVersionUID = -8970718410437077606L;

  @Override
  public void commence(
      HttpServletRequest request, HttpServletResponse response, AuthenticationException e)
      throws IOException {
    // 创建符合 RFC 7807 标准的错误响应
    String msg = String.format("请求访问：%s，认证失败，无法访问系统资源", request.getRequestURI());
    ForbiddenException exception = new ForbiddenException(msg);

    ServletUtils.renderString(response, JacksonUtil.toJSONString(exception));
  }
}
