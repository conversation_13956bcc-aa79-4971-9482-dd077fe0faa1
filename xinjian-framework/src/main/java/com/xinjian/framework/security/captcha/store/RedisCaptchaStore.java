package com.xinjian.framework.security.captcha.store;

import com.xinjian.captcha.service.ICaptchaStore;
import com.xinjian.starter.redis.core.RedisClient;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * Redis 验证码存储实现
 *
 * <p>使用 Redis 存储验证码，支持分布式环境下的验证码管理
 */
@Slf4j
@RequiredArgsConstructor
public class RedisCaptchaStore implements ICaptchaStore {

  private final RedisClient redisClient;

  /** 验证码 Redis 键前缀 */
  private static final String CAPTCHA_KEY_PREFIX = "captcha:";

  @Override
  public void save(String key, String code, long expireTimeInMinutes) {
    try {
      String redisKey = CAPTCHA_KEY_PREFIX + key;
      redisClient.set(redisKey, code, expireTimeInMinutes, TimeUnit.MINUTES);
      log.debug("验证码已保存到 Redis，key: {}，过期时间: {} 分钟", redisKey, expireTimeInMinutes);
    } catch (Exception e) {
      log.error("保存验证码到 Redis 失败，key: {}", key, e);
      throw new RuntimeException("保存验证码失败", e);
    }
  }

  @Override
  public Optional<String> get(String key) {
    try {
      String redisKey = CAPTCHA_KEY_PREFIX + key;
      Optional<String> code = redisClient.get(redisKey, String.class);
      log.debug("从 Redis 获取验证码，key: {}，结果: {}", redisKey, code.isPresent() ? "存在" : "不存在");
      return code;
    } catch (Exception e) {
      log.error("从 Redis 获取验证码失败，key: {}", key, e);
      return Optional.empty();
    }
  }

  @Override
  public void delete(String key) {
    try {
      String redisKey = CAPTCHA_KEY_PREFIX + key;
      redisClient.delete(redisKey);
      log.debug("从 Redis 删除验证码，key: {}", redisKey);
    } catch (Exception e) {
      log.error("从 Redis 删除验证码失败，key: {}", key, e);
    }
  }
}
