package com.xinjian.framework.manager.factory;

import com.xinjian.common.core.domain.entity.SysLogininfor;
import com.xinjian.common.core.domain.entity.SysOperLog;
import com.xinjian.common.service.AddressService;
import com.xinjian.common.service.ISysLogininforService;
import com.xinjian.common.service.ISysOperLogService;
import com.xinjian.common.utils.LogUtils;
import com.xinjian.common.utils.ServletUtils;
import com.xinjian.common.utils.ip.IpUtils;
import eu.bitwalker.useragentutils.UserAgent;
import java.time.LocalDateTime;
import java.util.TimerTask;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

/** 异步工厂（产生任务用） */
@Slf4j
@Component
@RequiredArgsConstructor
public class AsyncFactory {
  private static final Logger sys_user_logger = LoggerFactory.getLogger("sys-user");

  private final ISysLogininforService logininforService;
  private final ISysOperLogService operLogService;
  private final AddressService addressService;

  /**
   * 记录登录信息
   *
   * @param username 用户名
   * @param status 状态
   * @param message 消息
   * @param args 列表
   * @return 任务 task
   */
  public TimerTask recordLogininfor(
      final String username, final String status, final String message, final Object... args) {
    final UserAgent userAgent =
        UserAgent.parseUserAgentString(ServletUtils.getRequest().getHeader("User-Agent"));
    final String ip = IpUtils.getIpAddr();
    return new TimerTask() {
      @Override
      public void run() {
        String address = addressService.getRealAddressByIP(ip);
        StringBuilder s = new StringBuilder();
        s.append(LogUtils.getBlock(ip));
        s.append(address);
        s.append(LogUtils.getBlock(username));
        s.append(LogUtils.getBlock(status));
        s.append(LogUtils.getBlock(message));
        // 打印信息到日志
        log.info(s.toString(), args);
        // 获取客户端操作系统
        String os = userAgent.getOperatingSystem().getName();
        // 获取客户端浏览器
        String browser = userAgent.getBrowser().getName();
        // 封装对象
        SysLogininfor logininfor = new SysLogininfor();
        logininfor.setUserName(username);
        logininfor.setIpaddr(ip);
        logininfor.setLoginLocation(address);
        logininfor.setBrowser(browser);
        logininfor.setOs(os);
        logininfor.setMsg(message);
        logininfor.setLoginTime(LocalDateTime.now());
        // 日志状态
        if (StringUtils.equalsAny(status, "Success", "Logout", "Register")) {
          logininfor.setStatus(true);
        } else if ("Error".equals(status)) {
          logininfor.setStatus(false);
        }
        // 插入数据
        logininforService.insertLogininfor(logininfor);
      }
    };
  }

  /**
   * 操作日志记录
   *
   * @param operLog 操作日志信息
   * @return 任务 task
   */
  public TimerTask recordOper(final SysOperLog operLog) {
    return new TimerTask() {
      @Override
      public void run() {
        // 远程查询操作地点
        operLog.setOperLocation(addressService.getRealAddressByIP(operLog.getOperIp()));
        operLogService.insertOperlog(operLog);
      }
    };
  }
}
