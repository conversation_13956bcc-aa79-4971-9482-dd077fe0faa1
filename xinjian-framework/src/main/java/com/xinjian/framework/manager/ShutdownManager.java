package com.xinjian.framework.manager;

import javax.annotation.PreDestroy;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/** 确保应用退出时能关闭后台线程 */
@Component
@Slf4j
@RequiredArgsConstructor
public class ShutdownManager {

  private final AsyncManager asyncManager;

  @PreDestroy
  public void destroy() {
    shutdownAsyncManager();
  }

  /** 停止异步执行任务 */
  private void shutdownAsyncManager() {
    try {
      log.info("====关闭后台任务任务线程池====");
      asyncManager.shutdown();
    } catch (Exception e) {
      log.error(e.getMessage(), e);
    }
  }
}
