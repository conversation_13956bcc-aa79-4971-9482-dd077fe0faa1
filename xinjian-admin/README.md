# `xinjian-admin` 模块

## 1. 模块简介

`xinjian-admin` 是项目的核心启动模块和管理后台。它包含了应用的入口类 `Application.java`，并集成了 Swagger API 文档。

## 2. 主要功能

- **应用启动:** 作为 Spring Boot 应用的启动入口。
- **Web 控制器:** 包含了系统监控、通用接口等 Web Controller。
- **配置文件:** 管理不同环境（开发、生产、测试）的 application.yml 配置文件。
- **API 文档:** 通过 `OpenApiConfig` 配置并暴露 Swagger API 接口。

## 3. 如何运行

直接运行 `src/main/java/com/xinjian/Application.java` 的 `main` 方法即可启动整个后端服务。
