<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xinjian.admin.web.monitor.mapper.SysLogininforMapper">

  <resultMap type="SysLogininfor" id="SysLogininforResult">
    <id property="infoId" column="info_id" />
    <result property="userName" column="user_name" />
    <result property="status" column="status" />
    <result property="ipaddr" column="ipaddr" />
    <result property="loginLocation" column="login_location" />
    <result property="browser" column="browser" />
    <result property="os" column="os" />
    <result property="msg" column="msg" />
    <result property="loginTime" column="login_time" />
  </resultMap>

  <insert id="insertLogininfor" parameterType="SysLogininfor"> insert into sys_logininfor
    (user_name, status, ipaddr, login_location, browser, os, msg, login_time) values (#{userName},
    #{status}, #{ipaddr}, #{loginLocation}, #{browser}, #{os}, #{msg}, #{loginTime}) </insert>

  <select id="selectLogininforList" parameterType="SysLogininforQuery"
    resultMap="SysLogininforResult"> select info_id, user_name, ipaddr, login_location, browser, os,
    status, msg, login_time from sys_logininfor <where>
      <if test="ipaddr != null and ipaddr != ''"> AND ipaddr like concat('%', #{ipaddr}, '%') </if>
            <if
        test="status != null"> AND status = #{status} </if>
            <if
        test="userName != null and userName != ''"> AND user_name like concat('%', #{userName}, '%') </if>
            <if
        test="beginTime != null and beginTime != ''"><!--
                开始时间检索 --> AND login_time &gt;= #{beginTime} </if>
            <if
        test="endTime != null and endTime != ''">AND login_time &lt;= #{endTime} </if>
    </where>
    order by info_id desc </select>

  <select id="selectLogininforListByPage" resultMap="SysLogininforResult"> select info_id,
    user_name, ipaddr, login_location, browser, os, status, msg, login_time from sys_logininfor <where>
      <if test="query.ipaddr != null and query.ipaddr != ''"> AND ipaddr like concat('%',
    #{query.ipaddr}, '%') </if>
            <if
        test="query.status != null"> AND status = #{query.status} </if>
            <if
        test="query.userName != null and query.userName != ''"> AND user_name like concat('%',
    #{query.userName}, '%') </if>
            <if
        test="query.beginTime != null"><!--
                开始时间检索 --> AND login_time &gt;= #{query.beginTime} </if>
            <if
        test="query.endTime != null">AND login_time &lt;= #{query.endTime} </if>
    </where> order
    by info_id desc </select>

  <delete id="deleteLogininforByIds" parameterType="Long"> delete from sys_logininfor where info_id
    in <foreach collection="array" item="infoId" open="(" separator="," close=")"> #{infoId} </foreach>
  </delete>

  <update id="cleanLogininfor"> truncate table sys_logininfor </update>

</mapper>
