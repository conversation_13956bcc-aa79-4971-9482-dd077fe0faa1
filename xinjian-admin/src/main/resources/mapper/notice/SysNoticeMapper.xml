<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xinjian.admin.web.notice.mapper.SysNoticeMapper">

    <sql id="selectNoticeVo">
        SELECT
            notice_id,
            notice_title,
            notice_type,
            notice_content,
            status,
            create_by,
            create_time,
            update_by,
            update_time,
            remark
        FROM sys_notice
    </sql>

    <select id="selectNoticeListPage" resultType="SysNotice">
        <include refid="selectNoticeVo"/>
        <where>
            <if test="query.noticeTitle != null and query.noticeTitle != ''">
                AND notice_title like concat('%', #{query.noticeTitle}, '%')
            </if>
            <if test="query.noticeType != null and query.noticeType != ''">
                AND notice_type = #{query.noticeType}
            </if>
            <if test="query.createBy != null and query.createBy != ''">
                AND create_by like concat('%', #{query.createBy}, '%')
            </if>
        </where>
    </select>

</mapper>
