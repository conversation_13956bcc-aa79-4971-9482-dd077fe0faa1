<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xinjian.admin.web.system.mapper.SysUserMapper">
  <resultMap type="SysUser" id="SysUserResult">
    <id property="userId" column="user_id" />
    <result property="deptId" column="dept_id" />
    <result property="userName" column="user_name" />
    <result property="nickName" column="nick_name" />
    <result property="email" column="email" />
    <result property="mobile" column="mobile" />
    <result property="sex" column="sex" />
    <result property="avatar" column="avatar" />
    <result property="password" column="password" />
    <result property="status" column="status" />
    <result property="isDeleted" column="is_deleted" />
    <result property="loginIp" column="login_ip" />
    <result property="loginTime" column="login_time" />
    <result property="createBy" column="create_by" />
    <result property="createTime" column="create_time" />
    <result property="updateBy" column="update_by" />
    <result property="updateTime" column="update_time" />
    <result property="remark" column="remark" />
  </resultMap>
  <resultMap id="deptResult" type="SysDept">
    <id property="deptId" column="dept_id" />
    <result property="parentId" column="parent_id" />
    <result property="deptName" column="dept_name" />
    <result property="ancestors" column="ancestors" />
    <result property="orderNum" column="order_num" />
    <result property="leader" column="leader" />
    <result property="status" column="dept_status" />
  </resultMap>
  <resultMap id="RoleResult" type="SysRole">
    <id property="roleId" column="role_id" />
    <result property="roleName" column="role_name" />
    <result property="roleKey" column="role_key" />
    <result property="roleSort" column="role_sort" />
    <result property="dataScope" column="data_scope" />
    <result property="status" column="role_status" />
  </resultMap>
  <sql id="selectUserVo"> select u.user_id, u.dept_id, u.user_name, u.nick_name, u.email, u.avatar,
    u.mobile, u.password, u.sex, u.status, u.is_deleted, u.login_ip, u.login_time, u.create_by,
    u.create_time, u.remark, d.dept_id, d.parent_id, d.ancestors, d.dept_name, d.order_num,
    d.leader, d.status as dept_status, r.role_id, r.role_name, r.role_key, r.role_sort,
    r.data_scope, r.status as role_status from sys_user u left join sys_dept d on u.dept_id =
    d.dept_id left join sys_user_role ur on u.user_id = ur.user_id left join sys_role r on r.role_id
    = ur.role_id </sql>
  <select id="selectUserList" parameterType="SysUserQuery" resultMap="SysUserResult"> select
    u.user_id, u.dept_id, u.nick_name, u.user_name, u.email, u.avatar, u.mobile, u.sex, u.status,
    u.is_deleted, u.login_ip, u.login_time, u.create_by, u.create_time, u.remark, d.dept_name,
    d.leader from sys_user u left join sys_dept d on u.dept_id = d.dept_id where u.is_deleted = 0 <if
      test="userName != null and userName != ''"> AND u.user_name like concat('%', #{userName}, '%') </if>
    <if
      test="status != null"> AND u.status = #{status} </if>
    <if
      test="mobile != null and mobile != ''"> AND u.mobile like concat('%', #{mobile}, '%') </if>
    <if
      test="beginTime != null and beginTime != ''"> AND u.create_time &gt;= #{beginTime}</if>
    <if
      test="endTime != null and endTime != ''"> AND u.create_time &lt;= #{endTime}</if>
    <if
      test="deptId != null and deptId != 0"> AND (u.dept_id = #{deptId} OR u.dept_id IN ( SELECT
    t.dept_id FROM sys_dept t WHERE find_in_set(#{deptId}, ancestors) )) </if>
  </select>

  <select id="selectUserListPage" resultMap="SysUserResult"> select u.user_id, u.dept_id,
    u.nick_name, u.user_name, u.email, u.avatar, u.mobile, u.sex, u.status, u.is_deleted,
    u.login_ip, u.login_time, u.create_by, u.create_time, u.remark, d.dept_name, d.leader from
    sys_user u left join sys_dept d on u.dept_id = d.dept_id where u.is_deleted = 0 <if
      test="query.userName != null and query.userName != ''"> AND u.user_name like concat('%',
    #{query.userName}, '%') </if>
    <if test="query.status != null"> AND u.status = #{query.status} </if>
    <if
      test="query.mobile != null and query.mobile != ''"> AND u.mobile like concat('%',
    #{query.mobile}, '%') </if>
    <if test="query.beginTime != null"> AND u.create_time &gt;=
    #{query.beginTime}</if>
    <if test="query.endTime != null"> AND u.create_time &lt;=
    #{query.endTime}</if>
    <if
      test="query.deptId != null and query.deptId != 0"> AND (u.dept_id = #{query.deptId} OR
    u.dept_id IN ( SELECT t.dept_id FROM sys_dept t WHERE find_in_set(#{query.deptId}, ancestors) )) </if>
  </select>
  <select id="selectAllocatedList" parameterType="SysUser" resultMap="SysUserResult"> select
    distinct u.user_id, u.dept_id, u.user_name, u.nick_name, u.email, u.mobile, u.status,
    u.create_time from sys_user u left join sys_dept d on u.dept_id = d.dept_id left join
    sys_user_role ur on u.user_id = ur.user_id left join sys_role r on r.role_id = ur.role_id where
    u.is_deleted = 0 and r.role_id = #{roleId} <if
      test="userName != null and userName != ''"> AND u.user_name like concat('%', #{userName}, '%') </if>
    <if
      test="mobile != null and mobile != ''"> AND u.mobile like concat('%', #{mobile}, '%') </if>
  </select>

  <select id="selectAllocatedListPage" parameterType="SysUserQuery" resultMap="SysUserResult">
    select distinct u.user_id, u.dept_id, u.user_name, u.nick_name, u.email, u.mobile, u.status,
    u.create_time from sys_user u left join sys_dept d on u.dept_id = d.dept_id left join
    sys_user_role ur on u.user_id = ur.user_id left join sys_role r on r.role_id = ur.role_id where
    u.is_deleted = 0 and r.role_id = #{query.roleId} <if
      test="query.userName != null and query.userName != ''"> AND u.user_name like concat('%',
    #{query.userName}, '%') </if>
    <if
      test="query.mobile != null and query.mobile != ''"> AND u.mobile like concat('%',
    #{query.mobile}, '%') </if>
  </select>
  <select id="selectUnallocatedList" parameterType="SysUser" resultMap="SysUserResult"> select
    distinct u.user_id, u.dept_id, u.user_name, u.nick_name, u.email, u.mobile, u.status,
    u.create_time from sys_user u left join sys_dept d on u.dept_id = d.dept_id left join
    sys_user_role ur on u.user_id = ur.user_id left join sys_role r on r.role_id = ur.role_id where
    u.is_deleted = 0 and (r.role_id != #{roleId} or r.role_id IS NULL) and u.user_id not in (select
    u.user_id from sys_user u inner join sys_user_role ur on u.user_id = ur.user_id and ur.role_id =
    #{roleId}) <if test="userName != null and userName != ''"> AND u.user_name like concat('%',
    #{userName}, '%') </if>
    <if test="mobile != null and mobile != ''"> AND u.mobile like concat('%',
    #{mobile}, '%') </if>
  </select>

  <select id="selectUnallocatedListPage" parameterType="SysUserQuery" resultMap="SysUserResult">
    select distinct u.user_id, u.dept_id, u.user_name, u.nick_name, u.email, u.mobile, u.status,
    u.create_time from sys_user u left join sys_dept d on u.dept_id = d.dept_id left join
    sys_user_role ur on u.user_id = ur.user_id left join sys_role r on r.role_id = ur.role_id where
    u.is_deleted = 0 and (r.role_id != #{query.roleId} or r.role_id IS NULL) and u.user_id not in
    (select u.user_id from sys_user u inner join sys_user_role ur on u.user_id = ur.user_id and
    ur.role_id = #{query.roleId}) <if test="query.userName != null and query.userName != ''"> AND
    u.user_name like concat('%', #{query.userName}, '%') </if>
    <if
      test="query.mobile != null and query.mobile != ''"> AND u.mobile like concat('%',
    #{query.mobile}, '%') </if>
  </select>
  <select id="selectUserByUserName" parameterType="String" resultMap="SysUserResult">
    <include refid="selectUserVo" /> where u.user_name = #{userName} and u.is_deleted = 0 </select>
  <select id="selectUserById" parameterType="Long" resultMap="SysUserResult">
    <include refid="selectUserVo" /> where u.user_id = #{userId} </select>
  <select id="checkUserNameUnique" parameterType="String" resultMap="SysUserResult"> select user_id,
    user_name from sys_user where user_name = #{userName} and is_deleted = 0 limit 1 </select>
  <select id="checkMobileUnique" parameterType="String" resultMap="SysUserResult"> select user_id,
    mobile from sys_user where mobile = #{mobile} and is_deleted = 0 limit 1 </select>
  <select id="checkEmailUnique" parameterType="String" resultMap="SysUserResult"> select user_id,
    email from sys_user where email = #{email} and is_deleted = 0 limit 1 </select>
  <insert id="insertUser" parameterType="SysUser" useGeneratedKeys="true" keyProperty="userId">
    insert into sys_user( <if test="userId != null and userId != 0">user_id,</if>
    <if
      test="deptId != null and deptId != 0">dept_id,</if>
    <if
      test="userName != null and userName != ''">user_name,</if>
    <if
      test="nickName != null and nickName != ''">nick_name,</if>
    <if
      test="email != null and email != ''">email,</if>
    <if
      test="avatar != null and avatar != ''">avatar,</if>
    <if
      test="mobile != null and mobile != ''">mobile,</if>
    <if
      test="sex != null and sex != ''">sex,</if>
    <if test="password != null and password != ''">
    password,</if>
    <if test="status != null">status,</if>
    <if test="createBy != null">create_by,</if>
    <if
      test="remark != null and remark != ''">remark,</if> create_time )values( <if
      test="userId != null and userId != ''">#{userId},</if>
    <if
      test="deptId != null and deptId != ''">#{deptId},</if>
    <if
      test="userName != null and userName != ''">#{userName},</if>
    <if
      test="nickName != null and nickName != ''">#{nickName},</if>
    <if
      test="email != null and email != ''">#{email},</if>
    <if
      test="avatar != null and avatar != ''">#{avatar},</if>
    <if
      test="mobile != null and mobile != ''">#{mobile},</if>
    <if
      test="sex != null and sex != ''">#{sex},</if>
    <if
      test="password != null and password != ''">#{password},</if>
    <if test="status != null">
    #{status},</if>
    <if test="createBy != null">#{createBy},</if>
    <if
      test="remark != null and remark != ''">#{remark},</if> #{createTime} ) </insert>
  <update id="updateUser" parameterType="SysUser"> update sys_user <set>
      <if test="deptId != null and deptId != 0">dept_id = #{deptId},</if>
      <if
        test="userName != null and userName != ''">user_name = #{userName},</if>
      <if
        test="nickName != null and nickName != ''">nick_name = #{nickName},</if>
      <if
        test="email != null ">email = #{email},</if>
      <if test="mobile != null ">mobile = #{mobile},</if>
      <if
        test="sex != null and sex != ''">sex = #{sex},</if>
      <if
        test="avatar != null and avatar != ''">avatar = #{avatar},</if>
      <if
        test="password != null and password != ''">password = #{password},</if>
      <if
        test="status != null">status = #{status},</if>
      <if
        test="loginIp != null and loginIp != ''">login_ip = #{loginIp},</if>
      <if
        test="loginTime != null">login_time = #{loginTime},</if>
      <if
        test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
      <if
        test="remark != null">remark = #{remark},</if>
      <if
        test="updateTime != null">update_time = #{updateTime},</if>
      </set> where
    user_id = #{userId} </update>
  <update id="updateUserStatus" parameterType="SysUser"> update sys_user set status = #{status}
    where user_id = #{userId} </update>
  <update id="updateUserAvatar" parameterType="SysUser"> update sys_user set avatar = #{avatar}
    where user_name = #{userName} </update>
  <update id="resetUserPwd" parameterType="SysUser"> update sys_user set password = #{password}
    where user_name = #{userName} </update>
  <delete id="deleteUserById" parameterType="Long"> update sys_user set is_deleted = 1 where user_id
    = #{userId} </delete>
  <delete id="deleteUserByIds" parameterType="Long"> update sys_user set is_deleted = 1 where
    user_id in <foreach collection="array" item="userId" open="(" separator="," close=")"> #{userId} </foreach>
  </delete>
</mapper>
