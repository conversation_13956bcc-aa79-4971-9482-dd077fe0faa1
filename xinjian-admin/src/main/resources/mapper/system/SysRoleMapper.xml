<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xinjian.admin.web.system.mapper.SysRoleMapper">

  <resultMap type="SysRole" id="SysRoleResult">
    <id property="roleId" column="role_id" />
    <result property="roleName" column="role_name" />
    <result property="roleKey" column="role_key" />
    <result property="roleSort" column="role_sort" />
    <result property="dataScope" column="data_scope" />
    <result property="menuCheckStrictly" column="menu_check_strictly" />
    <result property="deptCheckStrictly" column="dept_check_strictly" />
    <result property="status" column="status" />
    <result property="isDeleted" column="is_deleted" />
    <result property="createBy" column="create_by" />
    <result property="createTime" column="create_time" />
    <result property="updateBy" column="update_by" />
    <result property="updateTime" column="update_time" />
    <result property="remark" column="remark" />
  </resultMap>

  <sql id="selectRoleVo"> select distinct r.role_id, r.role_name, r.role_key, r.role_sort,
    r.data_scope, r.menu_check_strictly, r.dept_check_strictly, r.status, r.is_deleted,
    r.create_time, r.remark from sys_role r left join sys_user_role ur on ur.role_id = r.role_id
    left join sys_user u on u.user_id = ur.user_id left join sys_dept d on u.dept_id = d.dept_id </sql>

  <select id="selectRoleList" parameterType="SysRoleQuery" resultMap="SysRoleResult">
    <include refid="selectRoleVo" /> where r.is_deleted = 0 <if
      test="roleId != null and roleId != 0"> AND r.role_id = #{roleId} </if>
            <if
      test="roleName != null and roleName != ''"> AND r.role_name like concat('%', #{roleName}, '%') </if>
        <if
      test="status != null"> AND r.status = #{status} </if>
        <if
      test="roleKey != null and roleKey != ''"> AND r.role_key like concat('%', #{roleKey}, '%') </if>
        <if
      test="beginTime != null and beginTime != ''">AND r.create_time &gt;= #{beginTime}</if>
        <if
      test="endTime != null and endTime != ''">AND r.create_time &lt;= #{endTime}</if>
    order by r.role_sort </select>

  <select id="selectRoleListPage" resultMap="SysRoleResult">
    <include refid="selectRoleVo" /> where r.is_deleted = 0 <if
      test="query.roleId != null and query.roleId != 0"> AND r.role_id = #{query.roleId} </if>
        <if
      test="query.roleName != null and query.roleName != ''"> AND r.role_name like concat('%',
    #{query.roleName}, '%') </if>
        <if
      test="query.status != null"> AND r.status = #{query.status} </if>
        <if
      test="query.roleKey != null and query.roleKey != ''"> AND r.role_key like concat('%',
    #{query.roleKey}, '%') </if>
        <if test="query.beginTime != null">AND r.create_time &gt;=
    #{query.beginTime}</if>
        <if test="query.endTime != null">AND r.create_time &lt;= #{query.endTime}</if>
    <!-- 数据范围过滤 -->
    ${query.dataScopeSqlFilter} order by r.role_sort </select>

  <select id="selectRolePermissionByUserId" parameterType="Long" resultMap="SysRoleResult">
    <include refid="selectRoleVo" /> WHERE r.is_deleted = 0 and ur.user_id = #{userId} </select>

  <select id="selectRoleAll" resultMap="SysRoleResult">
    <include refid="selectRoleVo" />
  </select>

  <select id="selectRoleListByUserId" parameterType="Long" resultType="Long"> select r.role_id from
    sys_role r left join sys_user_role ur on ur.role_id = r.role_id left join sys_user u on
    u.user_id = ur.user_id where u.user_id = #{userId} </select>

  <select id="selectRoleById" parameterType="Long" resultMap="SysRoleResult">
    <include refid="selectRoleVo" /> where r.role_id = #{roleId} </select>

  <select id="selectRolesByUserName" parameterType="String" resultMap="SysRoleResult">
    <include refid="selectRoleVo" /> WHERE r.is_deleted = 0 and u.user_name = #{userName} </select>

  <select id="checkRoleNameUnique" parameterType="String" resultMap="SysRoleResult">
    <include refid="selectRoleVo" /> where r.role_name=#{roleName} and r.is_deleted = 0 limit 1 </select>

  <select id="checkRoleKeyUnique" parameterType="String" resultMap="SysRoleResult">
    <include refid="selectRoleVo" /> where r.role_key=#{roleKey} and r.is_deleted = 0 limit 1 </select>

  <insert id="insertRole" parameterType="SysRole" useGeneratedKeys="true" keyProperty="roleId">
    insert into sys_role( <if test="roleId != null and roleId != 0">role_id,</if>
        <if
      test="roleName != null and roleName != ''">role_name,</if>
        <if
      test="roleKey != null and roleKey != ''">role_key,</if>
        <if test="roleSort != null"> role_sort,</if>
        <if
      test="dataScope != null and dataScope != ''">data_scope,</if>
        <if
      test="menuCheckStrictly != null">menu_check_strictly,</if>
        <if
      test="deptCheckStrictly != null">dept_check_strictly,</if>
        <if test="status != null">status,</if>
        <if
      test="remark != null and remark != ''">remark,</if>
        <if test="createBy != null">create_by,</if> create_time )values( <if
      test="roleId != null and roleId != 0">#{roleId},</if>
        <if
      test="roleName != null and roleName != ''">#{roleName},</if>
        <if
      test="roleKey != null and roleKey != ''">#{roleKey},</if>
        <if test="roleSort != null">
    #{roleSort},</if>
        <if test="dataScope != null and dataScope != ''">#{dataScope},</if>
        <if
      test="menuCheckStrictly != null">#{menuCheckStrictly},</if>
        <if
      test="deptCheckStrictly != null">#{deptCheckStrictly},</if>
        <if test="status != null">
    #{status},</if>
        <if
      test="remark != null and remark != ''">#{remark},</if>
        <if test="createBy != null">#{createBy},</if> #{createTime} ) </insert>

  <update id="updateRole" parameterType="SysRole"> update sys_role <set>
      <if test="roleName != null and roleName != ''">role_name = #{roleName},</if>
            <if
        test="roleKey != null and roleKey != ''">role_key = #{roleKey},</if>
            <if
        test="roleSort != null">role_sort = #{roleSort},</if>
            <if
        test="dataScope != null and dataScope != ''">data_scope = #{dataScope},</if>
            <if
        test="menuCheckStrictly != null">menu_check_strictly = #{menuCheckStrictly},</if>
            <if
        test="deptCheckStrictly != null">dept_check_strictly = #{deptCheckStrictly},</if>
            <if
        test="status != null">status = #{status},</if>
            <if
        test="remark != null">remark = #{remark},</if>
            <if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if> update_time =
    #{updateTime} </set> where role_id = #{roleId} </update>

  <delete id="deleteRoleById" parameterType="Long"> update sys_role set is_deleted = 1 where role_id
    = #{roleId} </delete>

  <delete id="deleteRoleByIds" parameterType="Long"> update sys_role set is_deleted = 1 where
    role_id in <foreach collection="array" item="roleId" open="(" separator="," close=")"> #{roleId} </foreach>
  </delete>

</mapper>
