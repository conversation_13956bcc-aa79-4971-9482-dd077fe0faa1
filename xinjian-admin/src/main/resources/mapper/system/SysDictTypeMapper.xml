<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xinjian.admin.web.system.mapper.SysDictTypeMapper">

  <resultMap type="SysDictType" id="SysDictTypeResult">
    <id property="dictId" column="dict_id" />
    <result property="dictName" column="dict_name" />
    <result property="dictType" column="dict_type" />
    <result property="status" column="status" />
    <result property="createBy" column="create_by" />
    <result property="createTime" column="create_time" />
    <result property="updateBy" column="update_by" />
    <result property="updateTime" column="update_time" />
  </resultMap>

  <sql id="selectDictTypeVo"> select dict_id, dict_name, dict_type, status, create_by, create_time,
    remark from sys_dict_type </sql>

  <select id="selectDictTypeList" parameterType="SysDictType" resultMap="SysDictTypeResult">
    <include refid="selectDictTypeVo" />
        <where>
      <if test="dictName != null and dictName != ''"> AND dict_name like concat('%', #{dictName},
    '%') </if>
            <if test="status != null"> AND status = #{status} </if>
            <if
        test="dictType != null and dictType != ''"> AND dict_type like concat('%', #{dictType}, '%') </if>
            <if
        test="beginTime != null and beginTime != ''">AND create_time &gt;= #{beginTime}</if>
            <if
        test="endTime != null and endTime != ''">AND create_time &lt;= #{endTime}</if>
    </where>
  </select>

  <select id="selectDictTypeListByQuery" parameterType="SysDictTypeQuery"
    resultMap="SysDictTypeResult">
    <include refid="selectDictTypeVo" />
        <where>
      <if test="dictName != null and dictName != ''"> AND dict_name like concat('%', #{dictName},
    '%') </if>
            <if test="status != null"> AND status = #{status} </if>
            <if
        test="dictType != null and dictType != ''"> AND dict_type like concat('%', #{dictType}, '%') </if>
            <if
        test="beginTime != null and beginTime != ''">AND create_time &gt;= #{beginTime}</if>
            <if
        test="endTime != null and endTime != ''">AND create_time &lt;= #{endTime}</if>
    </where>
  </select>

  <select id="selectDictTypeListPage" parameterType="SysDictTypeQuery" resultMap="SysDictTypeResult">
    <include refid="selectDictTypeVo" />
        <where>
      <if test="query.dictName != null and query.dictName != ''"> AND dict_name like concat('%',
    #{query.dictName}, '%') </if>
            <if test="query.status != null"> AND status = #{query.status} </if>
            <if
        test="query.dictType != null and query.dictType != ''"> AND dict_type like concat('%',
    #{query.dictType}, '%') </if>
            <if test="query.beginTime != null">AND create_time &gt;=
    #{query.beginTime}</if>
            <if test="query.endTime != null">AND create_time &lt;= #{query.endTime}</if>
    </where>
  </select>

  <select id="selectDictTypeAll" resultMap="SysDictTypeResult">
    <include refid="selectDictTypeVo" />
  </select>

  <select id="selectDictTypeById" parameterType="Long" resultMap="SysDictTypeResult">
    <include refid="selectDictTypeVo" /> where dict_id = #{dictId} </select>

  <select id="selectDictTypeByType" parameterType="String" resultMap="SysDictTypeResult">
    <include refid="selectDictTypeVo" /> where dict_type = #{dictType} </select>

  <select id="checkDictTypeUnique" parameterType="String" resultMap="SysDictTypeResult">
    <include refid="selectDictTypeVo" /> where dict_type = #{dictType} limit 1 </select>

  <delete id="deleteDictTypeById" parameterType="Long"> delete from sys_dict_type where dict_id =
    #{dictId} </delete>

  <delete id="deleteDictTypeByIds" parameterType="Long"> delete from sys_dict_type where dict_id in <foreach
      collection="array" item="dictId" open="(" separator="," close=")"> #{dictId} </foreach>
  </delete>

  <update id="updateDictType" parameterType="SysDictType"> update sys_dict_type <set>
      <if test="dictName != null and dictName != ''">dict_name = #{dictName},</if>
            <if
        test="dictType != null and dictType != ''">dict_type = #{dictType},</if>
            <if
        test="status != null">status = #{status},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if> update_time =
    #{updateTime} </set> where dict_id = #{dictId} </update>

  <insert id="insertDictType" parameterType="SysDictType"> insert into sys_dict_type( <if
      test="dictName != null and dictName != ''">dict_name,</if>
        <if
      test="dictType != null and dictType != ''">dict_type,</if>
        <if test="status != null"> status,</if>
        <if
      test="remark != null and remark != ''">remark,</if>
        <if test="createBy != null">create_by,</if> create_time )values( <if
      test="dictName != null and dictName != ''">#{dictName},</if>
        <if
      test="dictType != null and dictType != ''">#{dictType},</if>
        <if test="status != null">
    #{status},</if>
        <if test="remark != null and remark != ''">#{remark},</if>
        <if test="createBy != null">#{createBy},</if> #{createTime} ) </insert>

</mapper>
