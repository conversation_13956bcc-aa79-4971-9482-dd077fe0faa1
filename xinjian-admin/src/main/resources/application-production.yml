app:
  storage:
    path: /xdata/nas/xinjian-service

spring:
  datasource:
    dynamic:
      datasource:
        master:
          url: **********************************************************************************************************************************************************************************************
          username: gxxj
          password: Mgp^fMWTK7oRG&Q*
  redis:
    database: 0
    password: PlqQFwjrM3i2VBgduNcmRKkOHTx
    sentinel:
      master: mymaster
      nodes:
        - 192.168.35.85:26379
        - 192.168.35.86:26379
        - 192.168.35.87:26379

springdoc:
  api-docs:
    enabled: false
  swagger-ui:
    enabled: false
