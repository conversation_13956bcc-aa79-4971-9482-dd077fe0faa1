package com.xinjian.admin;

import java.io.IOException;
import java.net.*;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.Enumeration;
import javax.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.core.env.Environment;

@SpringBootApplication(exclude = {DataSourceAutoConfiguration.class})
@EnableCaching
@MapperScan("com.xinjian.**.mapper")
@ComponentScan(basePackages = {"com.xinjian"})
@Slf4j
public class Application {

  public static void main(String[] args) {
    SpringApplication app = new SpringApplication(Application.class);
    Environment env = app.run(args).getEnvironment();
    loggingAppStartupInfo(env);
  }

  private static void loggingAppStartupInfo(Environment env) {
    String protocol = env.getProperty("server.ssl.key-store") != null ? "https" : "http";
    String serverPort = env.getProperty("server.port");
    String contextPath = env.getProperty("server.servlet.context-path");

    if (StringUtils.isBlank(contextPath)) {
      contextPath = "/";
    }

    String hostAddress = getCurrentRunningIp();

    log.info(
        "\n------------------------------------------------------------------\n\t"
            + "Application '{}' is running!\n\t"
            + "- Local:    \t{}://localhost:{}{}\n\t"
            + "- Network:  \t{}://{}:{}{}\n\t"
            + "- Swagger:  \t{}://{}:{}{}{}\n\t"
            + "- Profile:  \t{}\n"
            + "------------------------------------------------------------------",
        env.getProperty("spring.application.name"),
        protocol,
        serverPort,
        contextPath,
        protocol,
        hostAddress,
        serverPort,
        contextPath,
        protocol,
        hostAddress,
        serverPort,
        contextPath,
        env.getProperty("springdoc.swagger-ui.path").replace(contextPath, ""),
        env.getActiveProfiles());
  }

  public static String getCurrentRunningIp() {
    try {
      Enumeration<NetworkInterface> networkInterfaces = NetworkInterface.getNetworkInterfaces();
      while (networkInterfaces.hasMoreElements()) {
        NetworkInterface ni = networkInterfaces.nextElement();
        Enumeration<InetAddress> nias = ni.getInetAddresses();
        while (nias.hasMoreElements()) {
          InetAddress ia = nias.nextElement();
          if (!ia.isLinkLocalAddress() && !ia.isLoopbackAddress() && ia instanceof Inet4Address) {
            return ia.getHostAddress();
          }
        }
      }
    } catch (SocketException e) {
      log.error("Unable to get local IP: " + e.getMessage(), e);
      try {
        return InetAddress.getLocalHost().getHostAddress();
      } catch (UnknownHostException u) {
        log.error("Unable to determine host name: " + u.getMessage(), u);
      }
    }
    return "127.0.0.1";
  }

  @PostConstruct
  public void initializeFolders() {
    final String LOGS_DIRECTORY = System.getProperty("user.dir") + "/logs";
    final String UPLOADS_DIRECTORY = System.getProperty("user.dir") + "/files";
    try {
      Files.createDirectories(Paths.get(LOGS_DIRECTORY));
      Files.createDirectories(Paths.get(UPLOADS_DIRECTORY));
    } catch (IOException e) {
      log.error("Failed to create folder: " + e.getMessage(), e);
    }
  }
}
