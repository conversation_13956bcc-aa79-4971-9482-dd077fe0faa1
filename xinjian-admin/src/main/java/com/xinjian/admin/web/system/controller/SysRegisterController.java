package com.xinjian.admin.web.system.controller;

import com.xinjian.common.core.domain.dto.RegisterRequest;
import com.xinjian.common.exception.status403.ForbiddenException;
import com.xinjian.common.service.ISysConfigService;
import com.xinjian.framework.web.service.SysRegisterService;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;

/**
 * 用户注册
 *
 * <p>提供用户注册接口
 */
@RestController
@RequestMapping("")
public class SysRegisterController {

  private final SysRegisterService registerService;

  private final ISysConfigService configService;

  public SysRegisterController(
      SysRegisterService registerService, ISysConfigService configService) {
    this.registerService = registerService;
    this.configService = configService;
  }

  /**
   * 用户注册
   *
   * @param user 用户注册信息
   */
  @PostMapping("/register")
  @ResponseStatus(HttpStatus.CREATED)
  public void register(@RequestBody RegisterRequest user) {
    if (!("true".equals(configService.selectConfigByKey("sys.account.registerUser")))) {
      throw new ForbiddenException("当前系统没有开启注册功能");
    }

    registerService.register(user);
  }
}
