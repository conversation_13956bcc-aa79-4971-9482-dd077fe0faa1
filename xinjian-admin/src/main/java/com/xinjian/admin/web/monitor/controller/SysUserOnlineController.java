package com.xinjian.admin.web.monitor.controller;

import com.xinjian.admin.web.system.service.SysUserOnlineService;
import com.xinjian.common.annotation.Log;
import com.xinjian.common.constant.CacheConstants;
import com.xinjian.common.core.domain.dto.LoginUser;
import com.xinjian.common.core.domain.dto.UserSessionInfo;
import com.xinjian.common.core.domain.entity.SysUserOnline;
import com.xinjian.common.core.page.TableDataInfo;
import com.xinjian.common.enums.BusinessType;
import com.xinjian.starter.redis.core.RedisClient;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;

/** 在线用户监控 */
@RestController
@RequestMapping("/monitor/online")
public class SysUserOnlineController {
  @Autowired private SysUserOnlineService userOnlineService;

  @Autowired private RedisClient redisClient;

  @PreAuthorize("hasAnyAuthority('monitor:online:list')")
  @GetMapping("/list")
  public TableDataInfo<SysUserOnline> list(String ipaddr, String userName) {
    Collection<String> keys = redisClient.keys(CacheConstants.LOGIN_TOKEN_KEY + "*");
    List<SysUserOnline> userOnlineList = new ArrayList<SysUserOnline>();
    for (String key : keys) {
      // 跳过会话信息的 key
      if (key.contains(":session:")) {
        continue;
      }

      LoginUser user = redisClient.get(key, LoginUser.class).orElse(null);
      if (user == null) {
        continue;
      }

      // 获取对应的会话信息
      String tokenUUID = key.substring(key.lastIndexOf(':') + 1);
      String sessionKey = CacheConstants.LOGIN_TOKEN_KEY + ":session:" + tokenUUID;
      UserSessionInfo sessionInfo = redisClient.get(sessionKey, UserSessionInfo.class).orElse(null);

      if (StringUtils.isNotEmpty(ipaddr) && StringUtils.isNotEmpty(userName)) {
        userOnlineList.add(
            userOnlineService.selectOnlineByInfo(ipaddr, userName, user, sessionInfo));
      } else if (StringUtils.isNotEmpty(ipaddr)) {
        userOnlineList.add(userOnlineService.selectOnlineByIpaddr(ipaddr, user, sessionInfo));
      } else if (StringUtils.isNotEmpty(userName)) {
        userOnlineList.add(userOnlineService.selectOnlineByUserName(userName, user, sessionInfo));
      } else {
        userOnlineList.add(userOnlineService.loginUserToUserOnline(user, sessionInfo));
      }
    }
    Collections.reverse(userOnlineList);
    userOnlineList.removeAll(Collections.singleton(null));
    return TableDataInfo.of(userOnlineList);
  }

  /** 强退用户 */
  @PreAuthorize("hasAnyAuthority('monitor:online:forceLogout')")
  @Log(title = "在线用户", businessType = BusinessType.FORCE)
  @DeleteMapping("/{tokenId}")
  @ResponseStatus(HttpStatus.NO_CONTENT)
  public void forceLogout(@PathVariable String tokenId) {
    redisClient.delete(CacheConstants.LOGIN_TOKEN_KEY + tokenId);
  }
}
