package com.xinjian.admin.web.notice;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xinjian.admin.web.notice.dto.SysNoticeDTO;
import com.xinjian.admin.web.notice.dto.SysNoticeQuery;
import com.xinjian.admin.web.notice.dto.SysNoticeRequest;

public interface ISysNoticeService {

  SysNoticeDTO selectNoticeById(Long noticeId);

  IPage<SysNoticeDTO> selectNoticeListByPage(SysNoticeQuery query);

  void createNotice(SysNoticeRequest request);

  void updateNotice(Long noticeId, SysNoticeRequest request);

  void deleteNoticeById(Long noticeId);
}
