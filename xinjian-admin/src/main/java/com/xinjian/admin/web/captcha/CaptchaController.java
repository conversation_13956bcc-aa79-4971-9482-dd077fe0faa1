package com.xinjian.admin.web.captcha;

import com.xinjian.captcha.domain.CaptchaGenerationResult;
import com.xinjian.captcha.service.ICaptchaService;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/** 验证码操作处理控制器 */
@RestController
@RequestMapping("/captcha")
public class CaptchaController {

  private final ICaptchaService captchaService;

  public CaptchaController(final ICaptchaService captchaService) {
    this.captchaService = captchaService;
  }

  /** 生成验证码 */
  @GetMapping()
  public CaptchaGenerationResult getCode() {
    return captchaService.generate();
  }
}
