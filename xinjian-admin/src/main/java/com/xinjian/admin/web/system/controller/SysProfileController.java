package com.xinjian.admin.web.system.controller;

import com.xinjian.admin.web.system.dto.AvatarResponse;
import com.xinjian.admin.web.system.dto.UserProfileResponse;
import com.xinjian.common.annotation.Log;
import com.xinjian.common.core.domain.dto.LoginUser;
import com.xinjian.common.core.domain.entity.SysUser;
import com.xinjian.common.enums.BusinessType;
import com.xinjian.common.exception.status409.ConflictException;
import com.xinjian.common.exception.status422.BusinessRuleViolationException;
import com.xinjian.common.exception.status422.ValidationFailedException;
import com.xinjian.common.exception.status500.ServiceException;
import com.xinjian.common.service.FileStorageService;
import com.xinjian.common.service.ISysUserService;
import com.xinjian.framework.web.service.TokenService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

/** 个人信息 业务处理 */
@RestController
@RequestMapping("/system/user/profile")
public class SysProfileController {
  @Autowired private ISysUserService userService;

  @Autowired private TokenService tokenService;

  @Autowired private FileStorageService fileStorageService;

  @Autowired private PasswordEncoder passwordEncoder;

  /** 个人信息 */
  @GetMapping
  public UserProfileResponse profile(@AuthenticationPrincipal LoginUser loginUser) {
    SysUser user = userService.selectUserByUserName(loginUser.getUsername());
    return new UserProfileResponse(
        user,
        userService.selectUserRoleGroup(loginUser.getUsername()),
        userService.selectUserPostGroup(loginUser.getUsername()));
  }

  /** 修改用户 */
  @Log(title = "个人信息", businessType = BusinessType.UPDATE)
  @PutMapping
  @ResponseStatus(HttpStatus.NO_CONTENT)
  public void updateProfile(
      @RequestBody SysUser user, @AuthenticationPrincipal LoginUser loginUser) {
    SysUser currentUser = userService.selectUserByUserName(loginUser.getUsername());
    currentUser.setNickName(user.getNickName());
    currentUser.setEmail(user.getEmail());
    currentUser.setMobile(user.getMobile());
    currentUser.setSex(user.getSex());
    if (StringUtils.isNotEmpty(user.getMobile()) && !userService.checkMobileUnique(currentUser)) {
      throw new ConflictException("修改用户'" + loginUser.getUsername() + "'失败，手机号码已存在");
    }
    if (StringUtils.isNotEmpty(user.getEmail()) && !userService.checkEmailUnique(currentUser)) {
      throw new ConflictException("修改用户'" + loginUser.getUsername() + "'失败，邮箱账号已存在");
    }
    if (userService.updateUserProfile(currentUser) > 0) {
      // 更新缓存用户信息
      tokenService.setLoginUser(loginUser);
    } else {
      throw new ServiceException("修改个人信息异常，请联系管理员");
    }
  }

  /** 重置密码 */
  @Log(title = "个人信息", businessType = BusinessType.UPDATE)
  @PutMapping("/updatePwd")
  @ResponseStatus(HttpStatus.NO_CONTENT)
  public void updatePwd(
      String oldPassword, String newPassword, @AuthenticationPrincipal LoginUser loginUser) {
    String userName = loginUser.getUsername();
    String password = loginUser.getPassword();
    if (!passwordEncoder.matches(oldPassword, password)) {
      throw new ValidationFailedException("修改密码失败，旧密码错误");
    }
    if (passwordEncoder.matches(newPassword, password)) {
      throw new BusinessRuleViolationException("新密码不能与旧密码相同");
    }
    newPassword = passwordEncoder.encode(newPassword);
    if (userService.resetUserPwd(userName, newPassword) > 0) {
      // 更新缓存用户密码 - 重新创建 LoginUser 对象
      SysUser user = userService.selectUserByUserName(userName);
      LoginUser updatedLoginUser =
          new LoginUser(
              user.getUserId(),
              user.getDeptId(),
              user.getUserName(),
              newPassword,
              loginUser.isEnabled(),
              loginUser.getAuthoritiesSet());
      tokenService.setLoginUser(updatedLoginUser);
    } else {
      throw new ServiceException("修改密码异常，请联系管理员");
    }
  }

  /** 头像上传 */
  @Log(title = "用户头像", businessType = BusinessType.UPDATE)
  @PostMapping("/avatar")
  public AvatarResponse avatar(
      @RequestParam("avatarfile") MultipartFile file, @AuthenticationPrincipal LoginUser loginUser)
      throws Exception {
    if (!file.isEmpty()) {
      String avatar =
          fileStorageService.upload(
              fileStorageService.getAvatarPath(),
              file,
              fileStorageService.getStorageProperties().getImageExtensions());
      if (userService.updateUserAvatar(loginUser.getUsername(), avatar)) {
        // 更新缓存用户头像 - 重新创建 LoginUser 对象
        SysUser user = userService.selectUserByUserName(loginUser.getUsername());
        LoginUser updatedLoginUser =
            new LoginUser(
                user.getUserId(),
                user.getDeptId(),
                user.getUserName(),
                loginUser.getPassword(),
                loginUser.isEnabled(),
                loginUser.getAuthoritiesSet());
        tokenService.setLoginUser(updatedLoginUser);
        return new AvatarResponse(avatar);
      }
    }
    throw new ServiceException("上传图片异常，请联系管理员");
  }
}
