package com.xinjian.admin.web.notice.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xinjian.admin.web.notice.dto.SysNoticeQuery;
import com.xinjian.admin.web.notice.entity.SysNotice;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface SysNoticeMapper extends BaseMapper<SysNotice> {
  /** 自定义分页查询通知公告列表 */
  List<SysNotice> selectNoticeListPage(
      @Param("page") IPage<SysNotice> page, @Param("query") SysNoticeQuery query);
}
