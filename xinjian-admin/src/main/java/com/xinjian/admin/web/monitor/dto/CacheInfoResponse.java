package com.xinjian.admin.web.monitor.dto;

import java.util.List;
import java.util.Properties;

/** 缓存信息响应对象 */
public class CacheInfoResponse {

  private Properties info;
  private Object dbSize;
  private List<CommandStatResponse> commandStats;

  public CacheInfoResponse() {}

  public CacheInfoResponse(Properties info, Object dbSize, List<CommandStatResponse> commandStats) {
    this.info = info;
    this.dbSize = dbSize;
    this.commandStats = commandStats;
  }

  public Properties getInfo() {
    return info;
  }

  public void setInfo(Properties info) {
    this.info = info;
  }

  public Object getDbSize() {
    return dbSize;
  }

  public void setDbSize(Object dbSize) {
    this.dbSize = dbSize;
  }

  public List<CommandStatResponse> getCommandStats() {
    return commandStats;
  }

  public void setCommandStats(List<CommandStatResponse> commandStats) {
    this.commandStats = commandStats;
  }
}
