package com.xinjian.admin.web.system.dto;

import com.xinjian.common.core.domain.entity.SysUser;
import java.util.Set;

/** 用户信息响应 DTO */
public class UserInfoResponse {

  private SysUser user;

  private Set<String> roles;

  private Set<String> permissions;

  public UserInfoResponse() {}

  public UserInfoResponse(SysUser user, Set<String> roles, Set<String> permissions) {
    this.user = user;
    this.roles = roles;
    this.permissions = permissions;
  }

  public SysUser getUser() {
    return user;
  }

  public void setUser(SysUser user) {
    this.user = user;
  }

  public Set<String> getRoles() {
    return roles;
  }

  public void setRoles(Set<String> roles) {
    this.roles = roles;
  }

  public Set<String> getPermissions() {
    return permissions;
  }

  public void setPermissions(Set<String> permissions) {
    this.permissions = permissions;
  }
}
