package com.xinjian.admin.web.notice;

import com.xinjian.admin.web.notice.dto.SysNoticeDTO;
import com.xinjian.admin.web.notice.dto.SysNoticeRequest;
import com.xinjian.admin.web.notice.entity.SysNotice;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

@Mapper(componentModel = "spring")
public interface SysNoticeConverter {

  SysNoticeDTO toDto(SysNotice entity);

  @Mapping(target = "noticeId", ignore = true)
  SysNotice toEntity(SysNoticeRequest request);
}
