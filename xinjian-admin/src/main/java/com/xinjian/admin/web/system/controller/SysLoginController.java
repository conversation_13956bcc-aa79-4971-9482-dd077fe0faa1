package com.xinjian.admin.web.system.controller;

import com.xinjian.admin.web.system.dto.LoginResponse;
import com.xinjian.admin.web.system.dto.UserInfoResponse;
import com.xinjian.common.core.domain.dto.LoginRequest;
import com.xinjian.common.core.domain.entity.SysMenu;
import com.xinjian.common.core.domain.entity.SysUser;
import com.xinjian.common.service.ISysMenuService;
import com.xinjian.common.service.ISysUserService;
import com.xinjian.common.utils.SecurityUtils;
import com.xinjian.framework.web.service.SysLoginService;
import com.xinjian.framework.web.service.SysPermissionService;
import java.util.List;
import java.util.Set;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/** 登录验证 */
@RestController
@RequestMapping("")
public class SysLoginController {
  @Autowired private SysLoginService loginService;

  @Autowired private ISysMenuService menuService;

  @Autowired private SysPermissionService permissionService;

  @Autowired private ISysUserService userService;

  /**
   * 登录方法
   *
   * @param loginBody 登录信息
   * @return 结果
   */
  @PostMapping("/login")
  public LoginResponse login(@RequestBody LoginRequest loginBody) {
    // 生成令牌
    String token =
        loginService.login(
            loginBody.getUsername(),
            loginBody.getPassword(),
            loginBody.getCode(),
            loginBody.getUuid());
    return new LoginResponse(token);
  }

  /**
   * 获取用户信息
   *
   * @return 用户信息
   */
  @GetMapping("getInfo")
  public UserInfoResponse getInfo() {
    Long userId = SecurityUtils.getUserId();
    // 根据用户 ID 查询完整的用户信息
    SysUser user = userService.selectUserById(userId);
    // 角色集合
    Set<String> roles = permissionService.getRolePermission(user);
    // 权限集合
    Set<String> permissions = permissionService.getMenuPermission(user);
    return new UserInfoResponse(user, roles, permissions);
  }

  /**
   * 获取路由信息
   *
   * @return 路由信息
   */
  @GetMapping("getRouters")
  public Object getRouters() {
    Long userId = SecurityUtils.getUserId();
    List<SysMenu> menus = menuService.selectMenuTreeByUserId(userId);
    return menuService.buildMenus(menus, "");
  }
}
