package com.xinjian.admin.web.system.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xinjian.common.core.domain.entity.SysRole;
import com.xinjian.common.core.domain.query.SysRoleQuery;
import java.util.List;
import org.apache.ibatis.annotations.Param;

/** 角色表 数据层 */
public interface SysRoleMapper {
  /**
   * 根据条件分页查询角色数据
   *
   * @param query 查询参数对象
   * @return 角色数据集合信息
   */
  public List<SysRole> selectRoleList(SysRoleQuery query);

  /**
   * 根据条件分页查询角色数据
   *
   * @param page 分页对象
   * @param query 查询参数对象
   * @return 当页的数据列表。分页的总数等信息，由分页插件填充到传入的 page 对象中。
   */
  public List<SysRole> selectRoleListPage(
      @Param("page") IPage<SysRole> page, @Param("query") SysRoleQuery query);

  /**
   * 根据用户 ID 查询角色
   *
   * @param userId 用户 ID
   * @return 角色列表
   */
  public List<SysRole> selectRolePermissionByUserId(Long userId);

  /**
   * 查询所有角色
   *
   * @return 角色列表
   */
  public List<SysRole> selectRoleAll();

  /**
   * 根据用户 ID 获取角色选择框列表
   *
   * @param userId 用户 ID
   * @return 选中角色 ID 列表
   */
  public List<Long> selectRoleListByUserId(Long userId);

  /**
   * 通过角色 ID 查询角色
   *
   * @param roleId 角色 ID
   * @return 角色对象信息
   */
  public SysRole selectRoleById(Long roleId);

  /**
   * 根据用户 ID 查询角色
   *
   * @param userName 用户名
   * @return 角色列表
   */
  public List<SysRole> selectRolesByUserName(String userName);

  /**
   * 校验角色名称是否唯一
   *
   * @param roleName 角色名称
   * @return 角色信息
   */
  public SysRole checkRoleNameUnique(String roleName);

  /**
   * 校验角色权限是否唯一
   *
   * @param roleKey 角色权限
   * @return 角色信息
   */
  public SysRole checkRoleKeyUnique(String roleKey);

  /**
   * 修改角色信息
   *
   * @param role 角色信息
   * @return 结果
   */
  public int updateRole(SysRole role);

  /**
   * 新增角色信息
   *
   * @param role 角色信息
   * @return 结果
   */
  public int insertRole(SysRole role);

  /**
   * 通过角色 ID 删除角色
   *
   * @param roleId 角色 ID
   * @return 结果
   */
  public int deleteRoleById(Long roleId);

  /**
   * 批量删除角色信息
   *
   * @param roleIds 需要删除的角色 ID
   * @return 结果
   */
  public int deleteRoleByIds(Long[] roleIds);
}
