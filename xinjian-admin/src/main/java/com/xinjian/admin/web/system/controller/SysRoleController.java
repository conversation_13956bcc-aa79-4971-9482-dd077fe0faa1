package com.xinjian.admin.web.system.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xinjian.admin.web.system.dto.RoleDeptTreeResponse;
import com.xinjian.admin.web.system.service.SysDeptService;
import com.xinjian.admin.web.system.service.SysRoleService;
import com.xinjian.admin.web.system.service.SysUserService;
import com.xinjian.common.annotation.Log;
import com.xinjian.common.core.domain.dto.LoginUser;
import com.xinjian.common.core.domain.entity.SysDept;
import com.xinjian.common.core.domain.entity.SysRole;
import com.xinjian.common.core.domain.entity.SysUser;
import com.xinjian.common.core.domain.entity.SysUserRole;
import com.xinjian.common.core.domain.query.SysRoleQuery;
import com.xinjian.common.core.domain.query.SysUserQuery;
import com.xinjian.common.core.page.TableDataInfo;
import com.xinjian.common.enums.BusinessType;
import com.xinjian.common.enums.UserStatus;
import com.xinjian.common.exception.status409.ConflictException;
import com.xinjian.common.exception.status500.ServiceException;
import com.xinjian.common.utils.SecurityUtils;
import com.xinjian.common.utils.poi.ExcelUtil;
import com.xinjian.framework.web.service.SysPermissionService;
import com.xinjian.framework.web.service.TokenService;
import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/** 角色信息 */
@RestController
@RequestMapping("/system/role")
public class SysRoleController {
  @Autowired private SysRoleService roleService;

  @Autowired private TokenService tokenService;

  @Autowired private SysPermissionService permissionService;

  @Autowired private SysUserService userService;

  @Autowired private SysDeptService deptService;

  @PreAuthorize("hasAnyAuthority('system:role:list')")
  @GetMapping("/list")
  public TableDataInfo<SysRole> list(SysRoleQuery query) {
    IPage<SysRole> page = roleService.selectRoleListByPage(query);
    return new TableDataInfo<>(page);
  }

  @Log(title = "角色管理", businessType = BusinessType.EXPORT)
  @PreAuthorize("hasAnyAuthority('system:role:export')")
  @PostMapping("/export")
  public void export(HttpServletResponse response, SysRoleQuery query) {
    List<SysRole> list = roleService.selectRoleList(query);
    ExcelUtil<SysRole> util = new ExcelUtil<SysRole>(SysRole.class);
    util.exportExcel(response, list, "角色数据");
  }

  /** 根据角色编号获取详细信息 */
  @PreAuthorize("hasAnyAuthority('system:role:query')")
  @GetMapping(value = "/{roleId}")
  public SysRole getInfo(@PathVariable Long roleId) {
    roleService.checkRoleDataScope(SecurityUtils.getUserId(), roleId);
    return roleService.selectRoleById(roleId);
  }

  /** 新增角色 */
  @PreAuthorize("hasAnyAuthority('system:role:add')")
  @Log(title = "角色管理", businessType = BusinessType.INSERT)
  @PostMapping
  @ResponseStatus(HttpStatus.CREATED)
  public SysRole add(
      @Validated @RequestBody SysRole role, @AuthenticationPrincipal LoginUser loginUser) {
    if (!roleService.checkRoleNameUnique(role)) {
      throw new ConflictException("新增角色'" + role.getRoleName() + "'失败，角色名称已存在");
    } else if (!roleService.checkRoleKeyUnique(role)) {
      throw new ConflictException("新增角色'" + role.getRoleName() + "'失败，角色权限已存在");
    }
    roleService.insertRole(role);
    return role;
  }

  /** 修改保存角色 */
  @PreAuthorize("hasAnyAuthority('system:role:edit')")
  @Log(title = "角色管理", businessType = BusinessType.UPDATE)
  @PutMapping
  public SysRole edit(
      @Validated @RequestBody SysRole role, @AuthenticationPrincipal LoginUser loginUser) {
    roleService.checkRoleAllowed(role);
    roleService.checkRoleDataScope(loginUser.getUserId(), role.getRoleId());
    if (!roleService.checkRoleNameUnique(role)) {
      throw new ConflictException("修改角色'" + role.getRoleName() + "'失败，角色名称已存在");
    } else if (!roleService.checkRoleKeyUnique(role)) {
      throw new ConflictException("修改角色'" + role.getRoleName() + "'失败，角色权限已存在");
    }

    if (roleService.updateRole(role) > 0) {
      // 更新缓存用户权限
      if (!SecurityUtils.isAdminUser(loginUser.getUserId())) {
        // 重新创建 LoginUser 对象并更新权限
        SysUser user = userService.selectUserByUserName(loginUser.getUsername());
        if (user != null) {
          java.util.Set<String> permissions = permissionService.getAllPermissions(user);
          LoginUser updatedLoginUser =
              new LoginUser(
                  user.getUserId(),
                  user.getDeptId(),
                  user.getUserName(),
                  user.getPassword(),
                  !UserStatus.DISABLE.getCode().equals(user.getStatus()),
                  permissions);

          // 更新缓存
          tokenService.setLoginUser(updatedLoginUser);
        }
      }
      return role;
    }
    throw new ServiceException("修改角色'" + role.getRoleName() + "'失败，请联系管理员");
  }

  /** 修改保存数据权限 */
  @PreAuthorize("hasAnyAuthority('system:role:edit')")
  @Log(title = "角色管理", businessType = BusinessType.UPDATE)
  @PutMapping("/dataScope")
  @ResponseStatus(HttpStatus.CREATED)
  public void dataScope(@RequestBody SysRole role, @AuthenticationPrincipal LoginUser loginUser) {
    roleService.checkRoleAllowed(role);
    roleService.checkRoleDataScope(loginUser.getUserId(), role.getRoleId());
    if (roleService.authDataScope(role) <= 0) {
      throw new ServiceException("修改角色数据权限失败");
    }
  }

  /** 状态修改 */
  @PreAuthorize("hasAnyAuthority('system:role:edit')")
  @Log(title = "角色管理", businessType = BusinessType.UPDATE)
  @PutMapping("/changeStatus")
  @ResponseStatus(HttpStatus.CREATED)
  public void changeStatus(
      @RequestBody SysRole role, @AuthenticationPrincipal LoginUser loginUser) {
    roleService.checkRoleAllowed(role);
    roleService.checkRoleDataScope(loginUser.getUserId(), role.getRoleId());
    if (roleService.updateRoleStatus(role) <= 0) {
      throw new ServiceException("修改角色状态失败");
    }
  }

  /** 删除角色 */
  @PreAuthorize("hasAnyAuthority('system:role:remove')")
  @Log(title = "角色管理", businessType = BusinessType.DELETE)
  @DeleteMapping("/{roleIds}")
  @ResponseStatus(HttpStatus.NO_CONTENT)
  public void remove(@PathVariable Long[] roleIds) {
    roleService.deleteRoleByIds(roleIds);
  }

  /** 获取角色选择框列表 */
  @PreAuthorize("hasAnyAuthority('system:role:query')")
  @GetMapping("/optionselect")
  public List<SysRole> optionselect() {
    return roleService.selectRoleAll();
  }

  /** 查询已分配用户角色列表 */
  @PreAuthorize("hasAnyAuthority('system:role:list')")
  @GetMapping("/authUser/allocatedList")
  public TableDataInfo<SysUser> allocatedList(SysUserQuery query) {
    IPage<SysUser> page = userService.selectAllocatedListByPage(query);
    return new TableDataInfo<>(page);
  }

  /** 查询未分配用户角色列表 */
  @PreAuthorize("hasAnyAuthority('system:role:list')")
  @GetMapping("/authUser/unallocatedList")
  public TableDataInfo<SysUser> unallocatedList(SysUserQuery query) {
    IPage<SysUser> page = userService.selectUnallocatedListByPage(query);
    return new TableDataInfo<>(page);
  }

  /** 取消授权用户 */
  @PreAuthorize("hasAnyAuthority('system:role:edit')")
  @Log(title = "角色管理", businessType = BusinessType.GRANT)
  @PutMapping("/authUser/cancel")
  @ResponseStatus(HttpStatus.CREATED)
  public void cancelAuthUser(@RequestBody SysUserRole userRole) {
    if (roleService.deleteAuthUser(userRole) <= 0) {
      throw new ServiceException("取消授权用户失败");
    }
  }

  /** 批量取消授权用户 */
  @PreAuthorize("hasAnyAuthority('system:role:edit')")
  @Log(title = "角色管理", businessType = BusinessType.GRANT)
  @PutMapping("/authUser/cancelAll")
  @ResponseStatus(HttpStatus.CREATED)
  public void cancelAuthUserAll(Long roleId, Long[] userIds) {
    if (roleService.deleteAuthUsers(roleId, userIds) <= 0) {
      throw new ServiceException("批量取消授权用户失败");
    }
  }

  /** 批量选择用户授权 */
  @PreAuthorize("hasAnyAuthority('system:role:edit')")
  @Log(title = "角色管理", businessType = BusinessType.GRANT)
  @PutMapping("/authUser/selectAll")
  @ResponseStatus(HttpStatus.CREATED)
  public void selectAuthUserAll(
      Long roleId, Long[] userIds, @AuthenticationPrincipal LoginUser loginUser) {
    roleService.checkRoleDataScope(loginUser.getUserId(), roleId);
    if (roleService.insertAuthUsers(roleId, userIds) <= 0) {
      throw new ServiceException("批量选择用户授权失败");
    }
  }

  /** 获取对应角色部门树列表 */
  @PreAuthorize("hasAnyAuthority('system:role:query')")
  @GetMapping(value = "/deptTree/{roleId}")
  public RoleDeptTreeResponse deptTree(@PathVariable("roleId") Long roleId) {
    return new RoleDeptTreeResponse(
        deptService.selectDeptListByRoleId(roleId),
        deptService.buildDeptTreeSelect(deptService.selectDeptTreeList(new SysDept())));
  }
}
