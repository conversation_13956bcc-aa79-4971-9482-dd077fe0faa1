package com.xinjian.admin.web.system.mapper;

import com.xinjian.common.core.domain.entity.SysRoleMenu;
import java.util.List;

/** 角色与菜单关联表 数据层 */
public interface SysRoleMenuMapper {
  /**
   * 查询菜单使用数量
   *
   * @param menuId 菜单 ID
   * @return 结果
   */
  public int checkMenuExistRole(Long menuId);

  /**
   * 通过角色 ID 删除角色和菜单关联
   *
   * @param roleId 角色 ID
   * @return 结果
   */
  public int deleteRoleMenuByRoleId(Long roleId);

  /**
   * 批量删除角色菜单关联信息
   *
   * @param ids 需要删除的数据 ID
   * @return 结果
   */
  public int deleteRoleMenu(Long[] ids);

  /**
   * 批量新增角色菜单信息
   *
   * @param roleMenuList 角色菜单列表
   * @return 结果
   */
  public int batchRoleMenu(List<SysRoleMenu> roleMenuList);
}
