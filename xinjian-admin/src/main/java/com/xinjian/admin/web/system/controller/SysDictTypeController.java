package com.xinjian.admin.web.system.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xinjian.admin.web.system.service.SysDictTypeService;
import com.xinjian.common.annotation.Log;
import com.xinjian.common.core.domain.dto.LoginUser;
import com.xinjian.common.core.domain.entity.SysDictType;
import com.xinjian.common.core.domain.query.SysDictTypeQuery;
import com.xinjian.common.core.page.TableDataInfo;
import com.xinjian.common.enums.BusinessType;
import com.xinjian.common.exception.status409.ConflictException;
import com.xinjian.common.exception.status500.ServiceException;
import com.xinjian.common.utils.poi.ExcelUtil;
import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;

/** 数据字典信息 */
@RestController
@RequestMapping("/system/dict/type")
public class SysDictTypeController {
  @Autowired private SysDictTypeService dictTypeService;

  @PreAuthorize("hasAnyAuthority('system:dict:list')")
  @GetMapping("/list")
  public TableDataInfo<SysDictType> list(SysDictTypeQuery query) {
    IPage<SysDictType> page = dictTypeService.selectDictTypeListByPage(query);
    return new TableDataInfo<>(page);
  }

  @Log(title = "字典类型", businessType = BusinessType.EXPORT)
  @PreAuthorize("hasAnyAuthority('system:dict:export')")
  @PostMapping("/export")
  public void export(HttpServletResponse response, SysDictType dictType) {
    List<SysDictType> list = dictTypeService.selectDictTypeList(dictType);
    ExcelUtil<SysDictType> util = new ExcelUtil<SysDictType>(SysDictType.class);
    util.exportExcel(response, list, "字典类型");
  }

  /** 查询字典类型详细 */
  @PreAuthorize("hasAnyAuthority('system:dict:query')")
  @GetMapping(value = "/{dictId}")
  public SysDictType getInfo(@PathVariable Long dictId) {
    return dictTypeService.selectDictTypeById(dictId);
  }

  /** 新增字典类型 */
  @PreAuthorize("hasAnyAuthority('system:dict:add')")
  @Log(title = "字典类型", businessType = BusinessType.INSERT)
  @PostMapping
  @ResponseStatus(HttpStatus.CREATED)
  public SysDictType add(
      @Validated @RequestBody SysDictType dict, @AuthenticationPrincipal LoginUser loginUser) {
    if (!dictTypeService.checkDictTypeUnique(dict)) {
      throw new ConflictException("新增字典'" + dict.getDictName() + "'失败，字典类型已存在");
    }
    dictTypeService.insertDictType(dict);
    return dict;
  }

  /** 修改字典类型 */
  @PreAuthorize("hasAnyAuthority('system:dict:edit')")
  @Log(title = "字典类型", businessType = BusinessType.UPDATE)
  @PutMapping
  public SysDictType edit(
      @Validated @RequestBody SysDictType dict, @AuthenticationPrincipal LoginUser loginUser) {
    if (!dictTypeService.checkDictTypeUnique(dict)) {
      throw new ConflictException("修改字典'" + dict.getDictName() + "'失败，字典类型已存在");
    }
    if (dictTypeService.updateDictType(dict) <= 0) {
      throw new ServiceException("修改字典类型失败");
    }
    return dict;
  }

  /** 删除字典类型 */
  @PreAuthorize("hasAnyAuthority('system:dict:remove')")
  @Log(title = "字典类型", businessType = BusinessType.DELETE)
  @DeleteMapping("/{dictIds}")
  @ResponseStatus(HttpStatus.NO_CONTENT)
  public void remove(@PathVariable Long[] dictIds) {
    dictTypeService.deleteDictTypeByIds(dictIds);
  }

  /** 刷新字典缓存 */
  @PreAuthorize("hasAnyAuthority('system:dict:remove')")
  @Log(title = "字典类型", businessType = BusinessType.CLEAN)
  @DeleteMapping("/refreshCache")
  @ResponseStatus(HttpStatus.NO_CONTENT)
  public void refreshCache() {
    dictTypeService.resetDictCache();
  }

  /** 获取字典选择框列表 */
  @GetMapping("/optionselect")
  public List<SysDictType> optionselect() {
    return dictTypeService.selectDictTypeAll();
  }
}
