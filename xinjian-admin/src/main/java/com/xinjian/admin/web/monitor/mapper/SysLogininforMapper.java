package com.xinjian.admin.web.monitor.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xinjian.common.core.domain.entity.SysLogininfor;
import com.xinjian.common.core.domain.query.SysLogininforQuery;
import java.util.List;
import org.apache.ibatis.annotations.Param;

/** 系统访问日志情况信息 数据层 */
public interface SysLogininforMapper {
  /**
   * 新增系统登录日志
   *
   * @param logininfor 访问日志对象
   */
  public void insertLogininfor(SysLogininfor logininfor);

  /**
   * 查询系统登录日志集合
   *
   * @param query 查询参数对象
   * @return 登录记录集合
   */
  public List<SysLogininfor> selectLogininforList(SysLogininforQuery query);

  /**
   * 分页查询系统登录日志集合
   *
   * @param page 分页对象
   * @param query 查询参数对象
   * @return 登录记录分页集合
   */
  public IPage<SysLogininfor> selectLogininforListByPage(
      @Param("page") IPage<SysLogininfor> page, @Param("query") SysLogininforQuery query);

  /**
   * 批量删除系统登录日志
   *
   * @param infoIds 需要删除的登录日志 ID
   * @return 结果
   */
  public int deleteLogininforByIds(Long[] infoIds);

  /**
   * 清空系统登录日志
   *
   * @return 结果
   */
  public int cleanLogininfor();
}
