package com.xinjian.admin.web.notice.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.xinjian.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
/** 通知公告表 sys_notice */
public class SysNotice extends BaseEntity {

  /** 公告 ID */
  @TableId(type = IdType.AUTO)
  private Long noticeId;

  /** 公告标题 */
  private String noticeTitle;

  /** 公告类型（1 通知 2 公告） */
  private String noticeType;

  /** 公告内容 */
  private String noticeContent;

  /** 公告状态（true 正常 false 关闭） */
  private Boolean status;
}
