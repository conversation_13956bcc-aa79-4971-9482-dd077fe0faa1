package com.xinjian.admin.web.system.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xinjian.admin.web.monitor.mapper.SysOperLogMapper;
import com.xinjian.common.core.domain.entity.SysOperLog;
import com.xinjian.common.core.domain.query.SysOperLogQuery;
import com.xinjian.common.exception.status400.BadRequestException;
import com.xinjian.common.properties.PaginationProperties;
import com.xinjian.common.service.ISysOperLogService;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/** 操作日志 服务层 */
@Service
@RequiredArgsConstructor
public class SysOperLogService implements ISysOperLogService {
  private final SysOperLogMapper operLogMapper;
  private final PaginationProperties paginationProperties;

  /**
   * 新增操作日志
   *
   * @param operLog 操作日志对象
   */
  @Override
  public void insertOperlog(SysOperLog operLog) {
    operLogMapper.insertOperlog(operLog);
  }

  /**
   * 查询系统操作日志集合
   *
   * @param query 查询参数对象
   * @return 操作日志集合
   */
  public List<SysOperLog> selectOperLogList(SysOperLogQuery query) {
    return operLogMapper.selectOperLogList(query);
  }

  /**
   * 查询系统操作日志集合
   *
   * <p>使用统一分页方案：
   *
   * <ul>
   *   <li>处理分页和排序逻辑
   *   <li>自动处理默认值、安全限制和 SQL 注入防护
   *   <li>配置驱动的分页策略
   * </ul>
   *
   * @param query 查询参数对象
   * @return 分页操作日志集合
   */
  public IPage<SysOperLog> selectOperLogListByPage(SysOperLogQuery query) {
    // 处理分页和排序逻辑
    IPage<SysOperLog> page = query.buildPage(paginationProperties);

    // 调用 Mapper 方法查询数据
    List<SysOperLog> records = operLogMapper.selectOperLogListPage(page, query);
    page.setRecords(records);

    return page;
  }

  /**
   * 批量删除系统操作日志
   *
   * @param operIds 需要删除的操作日志 ID
   * @throws BadRequestException 删除失败时抛出异常
   */
  public void deleteOperLogByIds(Long[] operIds) {
    int rows = operLogMapper.deleteOperLogByIds(operIds);
    if (rows == 0) {
      throw new BadRequestException("删除失败");
    }
  }

  /**
   * 查询操作日志详细
   *
   * @param operId 操作 ID
   * @return 操作日志对象
   */
  public SysOperLog selectOperLogById(Long operId) {
    return operLogMapper.selectOperLogById(operId);
  }

  /** 清空操作日志 */
  public void cleanOperLog() {
    operLogMapper.cleanOperLog();
  }
}
