package com.xinjian.admin.web.system.controller;

import com.xinjian.admin.web.system.dto.RoleMenuTreeSelectResponse;
import com.xinjian.admin.web.system.service.SysMenuService;
import com.xinjian.common.annotation.Log;
import com.xinjian.common.core.domain.TreeSelect;
import com.xinjian.common.core.domain.dto.LoginUser;
import com.xinjian.common.core.domain.entity.SysMenu;
import com.xinjian.common.enums.BusinessType;
import com.xinjian.common.exception.status409.ConflictException;
import com.xinjian.common.exception.status422.BusinessRuleViolationException;
import com.xinjian.common.exception.status500.ServiceException;
import java.util.List;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/** 菜单信息 */
@RestController
@RequestMapping("/system/menu")
public class SysMenuController {
  @Autowired private SysMenuService menuService;

  /** 获取菜单列表 */
  @PreAuthorize("hasAnyAuthority('system:menu:list')")
  @GetMapping("/list")
  public List<SysMenu> list(SysMenu menu, @AuthenticationPrincipal LoginUser loginUser) {
    return menuService.selectMenuList(menu, loginUser.getUserId());
  }

  /** 根据菜单编号获取详细信息 */
  @PreAuthorize("hasAnyAuthority('system:menu:query')")
  @GetMapping(value = "/{menuId}")
  public SysMenu getInfo(@PathVariable Long menuId) {
    return menuService.selectMenuById(menuId);
  }

  /** 获取菜单下拉树列表 */
  @GetMapping("/treeselect")
  public List<TreeSelect> treeselect(SysMenu menu, @AuthenticationPrincipal LoginUser loginUser) {
    List<SysMenu> menus = menuService.selectMenuList(menu, loginUser.getUserId());
    return menuService.buildMenuTreeSelect(menus);
  }

  /** 加载对应角色菜单列表树 */
  @GetMapping(value = "/roleMenuTreeselect/{roleId}")
  public RoleMenuTreeSelectResponse roleMenuTreeselect(
      @PathVariable("roleId") Long roleId, @AuthenticationPrincipal LoginUser loginUser) {
    List<SysMenu> menus = menuService.selectMenuList(loginUser.getUserId());
    return new RoleMenuTreeSelectResponse(
        menuService.selectMenuListByRoleId(roleId), menuService.buildMenuTreeSelect(menus));
  }

  /** 新增菜单 */
  @PreAuthorize("hasAnyAuthority('system:menu:add')")
  @Log(title = "菜单管理", businessType = BusinessType.INSERT)
  @PostMapping
  @ResponseStatus(HttpStatus.CREATED)
  public SysMenu add(
      @Validated @RequestBody SysMenu menu, @AuthenticationPrincipal LoginUser loginUser) {
    if (!menuService.checkMenuNameUnique(menu)) {
      throw new ConflictException("新增菜单'" + menu.getMenuName() + "'失败，菜单名称已存在");
    } else if (menu.getIsFrame()
        && !StringUtils.startsWithAny(menu.getPath(), "http://", "https://")) {
      throw new BusinessRuleViolationException(
          "新增菜单'" + menu.getMenuName() + "'失败，地址必须以 http(s)://开头");
    }
    menuService.insertMenu(menu);
    return menu;
  }

  /** 修改菜单 */
  @PreAuthorize("hasAnyAuthority('system:menu:edit')")
  @Log(title = "菜单管理", businessType = BusinessType.UPDATE)
  @PutMapping
  public SysMenu edit(
      @Validated @RequestBody SysMenu menu, @AuthenticationPrincipal LoginUser loginUser) {
    if (!menuService.checkMenuNameUnique(menu)) {
      throw new ConflictException("修改菜单'" + menu.getMenuName() + "'失败，菜单名称已存在");
    } else if (menu.getIsFrame()
        && !StringUtils.startsWithAny(menu.getPath(), "http://", "https://")) {
      throw new BusinessRuleViolationException(
          "修改菜单'" + menu.getMenuName() + "'失败，地址必须以 http(s)://开头");
    } else if (menu.getMenuId().equals(menu.getParentId())) {
      throw new BusinessRuleViolationException("修改菜单'" + menu.getMenuName() + "'失败，上级菜单不能选择自己");
    }
    if (menuService.updateMenu(menu) <= 0) {
      throw new ServiceException("修改菜单失败");
    }
    return menu;
  }

  /** 删除菜单 */
  @PreAuthorize("hasAnyAuthority('system:menu:remove')")
  @Log(title = "菜单管理", businessType = BusinessType.DELETE)
  @DeleteMapping("/{menuId}")
  @ResponseStatus(HttpStatus.NO_CONTENT)
  public void remove(@PathVariable("menuId") Long menuId) {
    if (menuService.hasChildByMenuId(menuId)) {
      throw new BusinessRuleViolationException("存在子菜单，不允许删除");
    }
    if (menuService.checkMenuExistRole(menuId)) {
      throw new ServiceException("菜单已分配，不允许删除");
    }
    if (menuService.deleteMenuById(menuId) <= 0) {
      throw new ServiceException("删除菜单失败");
    }
  }
}
