package com.xinjian.admin.web.system.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xinjian.admin.web.system.service.SysDeptService;
import com.xinjian.admin.web.system.service.SysPostService;
import com.xinjian.admin.web.system.service.SysRoleService;
import com.xinjian.admin.web.system.service.SysUserService;
import com.xinjian.common.annotation.Log;
import com.xinjian.common.core.domain.TreeSelect;
import com.xinjian.common.core.domain.dto.LoginUser;
import com.xinjian.common.core.domain.entity.SysDept;
import com.xinjian.common.core.domain.entity.SysUser;
import com.xinjian.common.core.domain.query.SysUserQuery;
import com.xinjian.common.core.page.TableDataInfo;
import com.xinjian.common.enums.BusinessType;
import com.xinjian.common.exception.status400.BadRequestException;
import com.xinjian.common.exception.status409.ConflictException;
import com.xinjian.common.utils.SecurityUtils;
import com.xinjian.common.utils.poi.ExcelUtil;
import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

/** 用户信息 */
@RestController
@RequestMapping("/system/user")
public class SysUserController {
  @Autowired private SysUserService userService;

  @Autowired private SysRoleService roleService;

  @Autowired private SysDeptService deptService;

  @Autowired private SysPostService postService;

  @Autowired private PasswordEncoder passwordEncoder;

  /** 获取用户列表 */
  @PreAuthorize("hasAnyAuthority('system:user:list')")
  @GetMapping("/list")
  public TableDataInfo<SysUser> list(SysUserQuery query) {
    IPage<SysUser> page = userService.selectUserListByPage(query);
    return new TableDataInfo<>(page);
  }

  @Log(title = "用户管理", businessType = BusinessType.EXPORT)
  @PreAuthorize("hasAnyAuthority('system:user:export')")
  @PostMapping("/export")
  public void export(HttpServletResponse response, SysUserQuery query) {
    List<SysUser> list = userService.selectUserList(query);
    ExcelUtil<SysUser> util = new ExcelUtil<SysUser>(SysUser.class);
    util.exportExcel(response, list, "用户数据");
  }

  @Log(title = "用户管理", businessType = BusinessType.IMPORT)
  @PreAuthorize("hasAnyAuthority('system:user:import')")
  @PostMapping("/importData")
  @ResponseStatus(HttpStatus.CREATED)
  public String importData(
      MultipartFile file, boolean updateSupport, @AuthenticationPrincipal LoginUser loginUser)
      throws Exception {
    ExcelUtil<SysUser> util = new ExcelUtil<SysUser>(SysUser.class);
    List<SysUser> userList = util.importExcel(file.getInputStream());
    String operName = loginUser.getUsername();
    return userService.importUser(userList, updateSupport, operName);
  }

  @PostMapping("/importTemplate")
  public void importTemplate(HttpServletResponse response) {
    ExcelUtil<SysUser> util = new ExcelUtil<SysUser>(SysUser.class);
    util.importTemplateExcel(response, "用户数据");
  }

  /** 根据用户编号获取详细信息 */
  @PreAuthorize("hasAnyAuthority('system:user:query')")
  @GetMapping(value = {"/", "/{userId}"})
  public SysUser getInfo(@PathVariable(value = "userId", required = false) Long userId) {
    userService.checkUserDataScope(userId, SecurityUtils.getUserId());
    return userService.selectUserById(userId);
  }

  /** 新增用户 */
  @PreAuthorize("hasAnyAuthority('system:user:add')")
  @Log(title = "用户管理", businessType = BusinessType.INSERT)
  @PostMapping
  @ResponseStatus(HttpStatus.CREATED)
  public SysUser add(
      @Validated @RequestBody SysUser user,
      @AuthenticationPrincipal LoginUser loginUser,
      @RequestParam(required = false) Long[] roleIds) {
    deptService.checkDeptDataScope(user.getDeptId(), loginUser.getUserId());
    if (roleIds != null) {
      for (Long roleId : roleIds) {
        roleService.checkRoleDataScope(loginUser.getUserId(), roleId);
      }
    }
    if (!userService.checkUserNameUnique(user)) {
      throw new ConflictException("新增用户'" + user.getUserName() + "'失败，登录账号已存在");
    } else if (StringUtils.isNotEmpty(user.getMobile()) && !userService.checkMobileUnique(user)) {
      throw new ConflictException("新增用户'" + user.getUserName() + "'失败，手机号码已存在");
    } else if (StringUtils.isNotEmpty(user.getEmail()) && !userService.checkEmailUnique(user)) {
      throw new ConflictException("新增用户'" + user.getUserName() + "'失败，邮箱账号已存在");
    }
    user.setPassword(passwordEncoder.encode(user.getPassword()));
    userService.insertUser(user);
    // 如果有角色ID，需要单独处理角色关联
    if (roleIds != null && roleIds.length > 0) {
      userService.insertUserAuth(user.getUserId(), roleIds);
    }
    return user;
  }

  /** 修改用户 */
  @PreAuthorize("hasAnyAuthority('system:user:edit')")
  @Log(title = "用户管理", businessType = BusinessType.UPDATE)
  @PutMapping
  public SysUser edit(
      @Validated @RequestBody SysUser user,
      @AuthenticationPrincipal LoginUser loginUser,
      @RequestParam(required = false) Long[] roleIds) {
    userService.checkUserAllowed(user);
    userService.checkUserDataScope(user.getUserId(), loginUser.getUserId());
    deptService.checkDeptDataScope(user.getDeptId(), loginUser.getUserId());
    if (roleIds != null) {
      for (Long roleId : roleIds) {
        roleService.checkRoleDataScope(loginUser.getUserId(), roleId);
      }
    }
    if (!userService.checkUserNameUnique(user)) {
      throw new ConflictException("修改用户'" + user.getUserName() + "'失败，登录账号已存在");
    } else if (StringUtils.isNotEmpty(user.getMobile()) && !userService.checkMobileUnique(user)) {
      throw new ConflictException("修改用户'" + user.getUserName() + "'失败，手机号码已存在");
    } else if (StringUtils.isNotEmpty(user.getEmail()) && !userService.checkEmailUnique(user)) {
      throw new ConflictException("修改用户'" + user.getUserName() + "'失败，邮箱账号已存在");
    }
    userService.updateUser(user);
    // 如果有角色ID，需要单独处理角色关联
    if (roleIds != null) {
      userService.insertUserAuth(user.getUserId(), roleIds);
    }
    return user;
  }

  /** 删除用户 */
  @PreAuthorize("hasAnyAuthority('system:user:remove')")
  @Log(title = "用户管理", businessType = BusinessType.DELETE)
  @DeleteMapping("/{userIds}")
  @ResponseStatus(HttpStatus.NO_CONTENT)
  public void remove(@PathVariable Long[] userIds, @AuthenticationPrincipal LoginUser loginUser) {
    if (ArrayUtils.contains(userIds, loginUser.getUserId())) {
      throw new BadRequestException("当前用户不能删除");
    }
    userService.deleteUserByIds(userIds);
  }

  /** 重置密码 */
  @PreAuthorize("hasAnyAuthority('system:user:resetPwd')")
  @Log(title = "用户管理", businessType = BusinessType.UPDATE)
  @PutMapping("/resetPwd")
  public SysUser resetPwd(@RequestBody SysUser user, @AuthenticationPrincipal LoginUser loginUser) {
    userService.checkUserAllowed(user);
    userService.checkUserDataScope(user.getUserId(), loginUser.getUserId());
    user.setPassword(passwordEncoder.encode(user.getPassword()));
    userService.resetPwd(user);
    return user;
  }

  /** 状态修改 */
  @PreAuthorize("hasAnyAuthority('system:user:edit')")
  @Log(title = "用户管理", businessType = BusinessType.UPDATE)
  @PutMapping("/changeStatus")
  public void changeStatus(
      @RequestBody SysUser user, @AuthenticationPrincipal LoginUser loginUser) {
    userService.checkUserAllowed(user);
    userService.checkUserDataScope(user.getUserId(), loginUser.getUserId());
    userService.updateUserStatus(user);
  }

  /** 根据用户编号获取授权角色 */
  @PreAuthorize("hasAnyAuthority('system:user:query')")
  @GetMapping("/authRole/{userId}")
  public SysUser authRole(@PathVariable("userId") Long userId) {
    return userService.selectUserById(userId);
  }

  /** 用户授权角色 */
  @PreAuthorize("hasAnyAuthority('system:user:edit')")
  @Log(title = "用户管理", businessType = BusinessType.GRANT)
  @PutMapping("/authRole")
  @ResponseStatus(HttpStatus.CREATED)
  public void insertAuthRole(Long userId, Long[] roleIds) {
    userService.checkUserDataScope(userId, SecurityUtils.getUserId());
    for (Long roleId : roleIds) {
      roleService.checkRoleDataScope(SecurityUtils.getUserId(), roleId);
    }
    userService.insertUserAuth(userId, roleIds);
  }

  /** 获取部门树列表 */
  @PreAuthorize("hasAnyAuthority('system:user:list')")
  @GetMapping("/deptTree")
  public List<TreeSelect> deptTree(SysDept dept) {
    return deptService.buildDeptTreeSelect(deptService.selectDeptTreeList(dept));
  }
}
