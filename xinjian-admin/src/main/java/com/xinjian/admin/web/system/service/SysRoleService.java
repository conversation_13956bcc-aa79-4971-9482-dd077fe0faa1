package com.xinjian.admin.web.system.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xinjian.admin.web.system.mapper.SysRoleDeptMapper;
import com.xinjian.admin.web.system.mapper.SysRoleMapper;
import com.xinjian.admin.web.system.mapper.SysRoleMenuMapper;
import com.xinjian.admin.web.system.mapper.SysUserRoleMapper;
import com.xinjian.common.annotation.DataScope;
import com.xinjian.common.core.domain.entity.SysRole;
import com.xinjian.common.core.domain.entity.SysRoleDept;
import com.xinjian.common.core.domain.entity.SysRoleMenu;
import com.xinjian.common.core.domain.entity.SysUserRole;
import com.xinjian.common.core.domain.query.SysRoleQuery;
import com.xinjian.common.exception.status400.BadRequestException;
import com.xinjian.common.properties.PaginationProperties;
import com.xinjian.common.service.ISysRoleService;
import com.xinjian.common.utils.SecurityUtils;
import java.util.*;
import java.util.Objects;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

/** 角色业务层 */
@Service
@RequiredArgsConstructor
public class SysRoleService implements ISysRoleService {
  /**
   * 根据条件分页查询角色数据
   *
   * @param role 角色信息
   * @return 角色数据集合信息
   */
  private final SysRoleMapper roleMapper;

  private final SysRoleMenuMapper roleMenuMapper;
  private final SysUserRoleMapper userRoleMapper;
  private final SysRoleDeptMapper roleDeptMapper;
  private final PaginationProperties paginationProperties;

  @Autowired @Lazy private SysRoleService self;

  /**
   * 根据条件分页查询角色数据
   *
   * @param query 查询参数对象
   * @return 角色数据集合信息
   */
  @DataScope(deptAlias = "d")
  public List<SysRole> selectRoleList(SysRoleQuery query) {
    return roleMapper.selectRoleList(query);
  }

  /**
   * 根据条件分页查询角色数据
   *
   * @param query 查询参数对象
   * @return 分页角色数据集合信息
   */
  @DataScope(deptAlias = "d")
  public IPage<SysRole> selectRoleListByPage(SysRoleQuery query) {

    IPage<SysRole> page = query.buildPage(paginationProperties);

    // 调用 Mapper 方法查询数据
    List<SysRole> records = roleMapper.selectRoleListPage(page, query);
    page.setRecords(records);

    return page;
  }

  /**
   * 根据用户 ID 查询角色列表
   *
   * @param userId 用户 ID
   * @return 角色列表
   */
  /**
   * 根据用户 ID 查询角色
   *
   * @param userId 用户 ID
   * @return 角色列表
   */
  public List<SysRole> selectRolesByUserId(Long userId) {
    List<SysRole> userRoles = roleMapper.selectRolePermissionByUserId(userId);
    List<SysRole> roles = selectRoleAll();
    for (SysRole role : roles) {
      for (SysRole userRole : userRoles) {
        if (role.getRoleId().longValue() == userRole.getRoleId().longValue()) {
          role.setFlag(true);
          break;
        }
      }
    }
    return roles;
  }

  /**
   * 根据用户 ID 查询角色权限
   *
   * @param userId 用户 ID
   * @return 权限列表
   */
  /**
   * 根据用户 ID 查询权限
   *
   * @param userId 用户 ID
   * @return 权限列表
   */
  @Override
  public Set<String> selectRolePermissionByUserId(Long userId) {
    List<SysRole> perms = roleMapper.selectRolePermissionByUserId(userId);
    Set<String> permsSet = new HashSet<>();
    for (SysRole perm : perms) {
      if (Objects.nonNull(perm)) {
        permsSet.addAll(Arrays.asList(perm.getRoleKey().trim().split(",")));
      }
    }
    return permsSet;
  }

  /**
   * 查询所有角色
   *
   * @return 角色列表
   */
  /**
   * 查询所有角色
   *
   * @return 角色列表
   */
  public List<SysRole> selectRoleAll() {
    return self.selectRoleList(new SysRoleQuery());
  }

  /**
   * 根据用户 ID 获取角色选择框列表
   *
   * @param userId 用户 ID
   * @return 选中角色 ID 列表
   */
  /**
   * 根据用户 ID 获取角色选择框列表
   *
   * @param userId 用户 ID
   * @return 选中角色 ID 列表
   */
  public List<Long> selectRoleListByUserId(Long userId) {
    return roleMapper.selectRoleListByUserId(userId);
  }

  /**
   * 通过角色 ID 查询角色
   *
   * @param roleId 角色 ID
   * @return 角色对象信息
   */
  /**
   * 通过角色 ID 查询角色
   *
   * @param roleId 角色 ID
   * @return 角色对象信息
   */
  public SysRole selectRoleById(Long roleId) {
    return roleMapper.selectRoleById(roleId);
  }

  /**
   * 校验角色名称是否唯一
   *
   * @param role 角色信息
   * @return 结果
   */
  /**
   * 校验角色名称是否唯一
   *
   * @param role 角色信息
   * @return 结果
   */
  public boolean checkRoleNameUnique(SysRole role) {
    Long roleId = Objects.isNull(role.getRoleId()) ? -1L : role.getRoleId();
    SysRole info = roleMapper.checkRoleNameUnique(role.getRoleName());
    if (Objects.nonNull(info) && info.getRoleId().longValue() != roleId.longValue()) {
      return false;
    }
    return true;
  }

  /**
   * 校验角色权限是否唯一
   *
   * @param role 角色信息
   * @return 结果
   */
  /**
   * 校验角色权限是否唯一
   *
   * @param role 角色信息
   * @return 结果
   */
  public boolean checkRoleKeyUnique(SysRole role) {
    Long roleId = Objects.isNull(role.getRoleId()) ? -1L : role.getRoleId();
    SysRole info = roleMapper.checkRoleKeyUnique(role.getRoleKey());
    if (Objects.nonNull(info) && info.getRoleId().longValue() != roleId.longValue()) {
      return false;
    }
    return true;
  }

  /**
   * 校验角色是否允许操作
   *
   * @param role 角色信息
   */
  /**
   * 校验角色是否允许操作
   *
   * @param role 角色信息
   */
  public void checkRoleAllowed(SysRole role) {
    if (Objects.nonNull(role.getRoleId()) && SecurityUtils.isAdminRole(role.getRoleId())) {
      throw new BadRequestException("不允许操作超级管理员角色");
    }
  }

  /**
   * 校验角色是否有数据权限
   *
   * @param currentUserId 当前用户ID
   * @param roleId 角色ID
   */
  @Override
  public void checkRoleDataScope(Long currentUserId, Long roleId) {
    if (!SecurityUtils.isAdminUser(currentUserId)) {
      SysRoleQuery query = new SysRoleQuery();
      query.setRoleId(roleId);
      List<SysRole> roles = self.selectRoleList(query);
      if (CollectionUtils.isEmpty(roles)) {
        throw new BadRequestException("无权限访问角色数据");
      }
    }
  }

  /**
   * 通过角色 ID 查询角色使用数量
   *
   * @param roleId 角色 ID
   * @return 结果
   */
  /**
   * 通过角色 ID 查询角色使用数量
   *
   * @param roleId 角色 ID
   * @return 结果
   */
  public int countUserRoleByRoleId(Long roleId) {
    return userRoleMapper.countUserRoleByRoleId(roleId);
  }

  /**
   * 新增保存角色信息
   *
   * @param role 角色信息
   * @return 结果
   */
  /**
   * 新增保存角色信息
   *
   * @param role 角色信息
   * @return 结果
   */
  @Transactional
  public int insertRole(SysRole role) {
    // // 设置审计字段
    // role.setCreateBy(SecurityUtils.getUserId());
    // role.setCreateTime(LocalDateTime.now());
    // role.setUpdateBy(SecurityUtils.getUserId());
    // role.setUpdateTime(LocalDateTime.now());
    // 新增角色信息
    roleMapper.insertRole(role);
    return insertRoleMenu(role);
  }

  /**
   * 修改保存角色信息
   *
   * @param role 角色信息
   * @return 结果
   */
  /**
   * 修改保存角色信息
   *
   * @param role 角色信息
   * @return 结果
   */
  @Transactional
  public int updateRole(SysRole role) {
    // 修改角色信息
    roleMapper.updateRole(role);
    // 删除角色与菜单关联
    roleMenuMapper.deleteRoleMenuByRoleId(role.getRoleId());
    return insertRoleMenu(role);
  }

  /**
   * 修改角色状态
   *
   * @param role 角色信息
   * @return 结果
   */
  /**
   * 修改角色状态
   *
   * @param role 角色信息
   * @return 结果
   */
  public int updateRoleStatus(SysRole role) {
    return roleMapper.updateRole(role);
  }

  /**
   * 修改数据权限信息
   *
   * @param role 角色信息
   * @return 结果
   */
  /**
   * 修改数据权限信息
   *
   * @param role 角色信息
   * @return 结果
   */
  @Transactional
  public int authDataScope(SysRole role) {
    // 修改角色信息
    roleMapper.updateRole(role);
    // 删除角色与部门关联
    roleDeptMapper.deleteRoleDeptByRoleId(role.getRoleId());
    // 新增角色和部门信息（数据权限）
    return insertRoleDept(role);
  }

  /**
   * 通过角色 ID 删除角色
   *
   * @param roleId 角色 ID
   * @return 结果
   */
  /**
   * 通过角色 ID 删除角色
   *
   * @param roleId 角色 ID
   * @return 结果
   */
  @Transactional
  public int deleteRoleById(Long roleId) {
    // 删除角色与菜单关联
    roleMenuMapper.deleteRoleMenuByRoleId(roleId);
    // 删除角色与部门关联
    roleDeptMapper.deleteRoleDeptByRoleId(roleId);
    return roleMapper.deleteRoleById(roleId);
  }

  /**
   * 批量删除角色信息
   *
   * @param roleIds 需要删除的角色 ID
   * @return 结果
   */
  /**
   * 批量删除角色信息
   *
   * @param roleIds 需要删除的角色 ID
   * @return 结果
   */
  @Transactional
  public void deleteRoleByIds(Long[] roleIds) {
    for (Long roleId : roleIds) {
      checkRoleAllowed(new SysRole(roleId));
      checkRoleDataScope(SecurityUtils.getUserId(), roleId);
      SysRole role = selectRoleById(roleId);
      if (countUserRoleByRoleId(roleId) > 0) {
        throw new BadRequestException(String.format("%s 已分配，不能删除", role.getRoleName()));
      }
    }
    // 删除角色与菜单关联
    roleMenuMapper.deleteRoleMenu(roleIds);
    // 删除角色与部门关联
    roleDeptMapper.deleteRoleDept(roleIds);
    int rows = roleMapper.deleteRoleByIds(roleIds);
    if (rows == 0) {
      throw new BadRequestException("删除角色失败");
    }
  }

  /**
   * 取消授权用户角色
   *
   * @param userRole 用户和角色关联信息
   * @return 结果
   */
  /**
   * 取消授权用户角色
   *
   * @param userRole 用户和角色关联信息
   * @return 结果
   */
  public int deleteAuthUser(SysUserRole userRole) {
    return userRoleMapper.deleteUserRoleInfo(userRole);
  }

  /**
   * 批量取消授权用户角色
   *
   * @param roleId 角色 ID
   * @param userIds 需要取消授权的用户数据 ID
   * @return 结果
   */
  /**
   * 批量取消授权用户角色
   *
   * @param roleId 角色 ID
   * @param userIds 需要取消授权的用户数据 ID
   * @return 结果
   */
  public int deleteAuthUsers(Long roleId, Long[] userIds) {
    return userRoleMapper.deleteUserRoleInfos(roleId, userIds);
  }

  /**
   * 批量选择授权用户角色
   *
   * @param roleId 角色 ID
   * @param userIds 需要删除的用户数据 ID
   * @return 结果
   */
  /**
   * 批量选择授权用户角色
   *
   * @param roleId 角色 ID
   * @param userIds 需要授权的用户数据 ID
   * @return 结果
   */
  public int insertAuthUsers(Long roleId, Long[] userIds) {
    // 新增用户与角色管理
    List<SysUserRole> list = new ArrayList<SysUserRole>();
    for (Long userId : userIds) {
      SysUserRole ur = new SysUserRole();
      ur.setUserId(userId);
      ur.setRoleId(roleId);
      list.add(ur);
    }
    return userRoleMapper.batchUserRole(list);
  }

  /**
   * 新增角色菜单信息
   *
   * @param role 角色对象
   */
  public int insertRoleMenu(SysRole role) {
    int rows = 1;
    // 新增用户与角色管理
    List<SysRoleMenu> list = new ArrayList<SysRoleMenu>();
    for (Long menuId : role.getMenuIds()) {
      SysRoleMenu rm = new SysRoleMenu();
      rm.setRoleId(role.getRoleId());
      rm.setMenuId(menuId);
      list.add(rm);
    }
    if (list.size() > 0) {
      rows = roleMenuMapper.batchRoleMenu(list);
    }
    return rows;
  }

  /**
   * 新增角色部门信息 (数据权限)
   *
   * @param role 角色对象
   */
  public int insertRoleDept(SysRole role) {
    int rows = 1;
    // 新增角色与部门（数据权限）管理
    List<SysRoleDept> list = new ArrayList<SysRoleDept>();
    for (Long deptId : role.getDeptIds()) {
      SysRoleDept rd = new SysRoleDept();
      rd.setRoleId(role.getRoleId());
      rd.setDeptId(deptId);
      list.add(rd);
    }
    if (list.size() > 0) {
      rows = roleDeptMapper.batchRoleDept(list);
    }
    return rows;
  }

  @Override
  public List<String> selectRoleKeysByUserId(Long userId) {
    List<SysRole> userRoles = roleMapper.selectRolePermissionByUserId(userId);
    List<String> roleKeys = new ArrayList<>();
    for (SysRole role : userRoles) {
      if (Objects.nonNull(role) && StringUtils.isNotEmpty(role.getRoleKey())) {
        roleKeys.add(role.getRoleKey());
      }
    }
    return roleKeys;
  }
}
