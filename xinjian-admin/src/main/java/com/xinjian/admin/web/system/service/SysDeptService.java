package com.xinjian.admin.web.system.service;

import com.xinjian.admin.web.system.mapper.SysDeptMapper;
import com.xinjian.admin.web.system.mapper.SysRoleMapper;
import com.xinjian.common.annotation.DataScope;
import com.xinjian.common.core.domain.TreeSelect;
import com.xinjian.common.core.domain.entity.SysDept;
import com.xinjian.common.core.domain.entity.SysRole;
import com.xinjian.common.core.text.Convert;
import com.xinjian.common.exception.status400.BadRequestException;
import com.xinjian.common.service.ISysDeptService;
import com.xinjian.common.utils.SecurityUtils;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

/** 部门管理 服务实现 */
@Service
public class SysDeptService implements ISysDeptService {
  @Autowired private SysDeptMapper deptMapper;

  @Autowired private SysRoleMapper roleMapper;

  @Autowired @Lazy private SysDeptService self;

  /**
   * 查询部门管理数据
   *
   * @param dept 部门信息
   * @return 部门信息集合
   */
  @DataScope(deptAlias = "d")
  public List<SysDept> selectDeptList(SysDept dept) {
    return deptMapper.selectDeptList(dept);
  }

  /**
   * 查询部门树结构信息
   *
   * @param dept 部门信息
   * @return 部门树信息集合
   */
  public List<SysDept> selectDeptTreeList(SysDept dept) {
    List<SysDept> depts = self.selectDeptList(dept);
    return buildDeptTree(depts);
  }

  /**
   * 构建前端所需要树结构
   *
   * @param depts 部门列表
   * @return 树结构列表
   */
  public List<SysDept> buildDeptTree(List<SysDept> depts) {
    List<SysDept> returnList = new ArrayList<SysDept>();
    List<Long> tempList = depts.stream().map(SysDept::getDeptId).collect(Collectors.toList());
    for (SysDept dept : depts) {
      // 如果是顶级节点，遍历该父节点的所有子节点
      if (!tempList.contains(dept.getParentId())) {
        recursionFn(depts, dept);
        returnList.add(dept);
      }
    }
    if (returnList.isEmpty()) {
      returnList = depts;
    }
    return returnList;
  }

  /**
   * 构建前端所需要下拉树结构
   *
   * @param depts 部门列表
   * @return 下拉树结构列表
   */
  public List<TreeSelect> buildDeptTreeSelect(List<SysDept> depts) {
    List<SysDept> deptTrees = buildDeptTree(depts);
    return deptTrees.stream().map(TreeSelect::new).collect(Collectors.toList());
  }

  /**
   * 根据角色 ID 查询部门树信息
   *
   * @param roleId 角色 ID
   * @return 选中部门列表
   */
  public List<Long> selectDeptListByRoleId(Long roleId) {
    SysRole role = roleMapper.selectRoleById(roleId);
    return deptMapper.selectDeptListByRoleId(roleId, role.isDeptCheckStrictly());
  }

  /**
   * 根据角色ID查询部门树信息
   *
   * @param roleId 角色ID
   * @param deptCheckStrictly 部门树选择是否关联显示
   * @return 部门树信息列表
   */
  @Override
  public List<Long> selectDeptListByRoleId(Long roleId, boolean deptCheckStrictly) {
    return deptMapper.selectDeptListByRoleId(roleId, deptCheckStrictly);
  }

  /**
   * 根据部门 ID 查询信息
   *
   * @param deptId 部门 ID
   * @return 部门信息
   */
  public SysDept selectDeptById(Long deptId) {
    return deptMapper.selectDeptById(deptId);
  }

  /**
   * 根据 ID 查询所有子部门（正常状态）
   *
   * @param deptId 部门 ID
   * @return 子部门数
   */
  public int selectNormalChildrenDeptById(Long deptId) {
    return deptMapper.selectNormalChildrenDeptById(deptId);
  }

  /**
   * 是否存在子节点
   *
   * @param deptId 部门 ID
   * @return 结果
   */
  public boolean hasChildByDeptId(Long deptId) {
    int result = deptMapper.hasChildByDeptId(deptId);
    return result > 0;
  }

  /**
   * 查询部门是否存在用户
   *
   * @param deptId 部门 ID
   * @return 结果 true 存在 false 不存在
   */
  public boolean checkDeptExistUser(Long deptId) {
    int result = deptMapper.checkDeptExistUser(deptId);
    return result > 0;
  }

  /**
   * 查询部门是否存在用户
   *
   * @param deptId 部门 ID
   * @return 结果 true 存在 false 不存在
   */
  public boolean hasDeptUser(Long deptId) {
    return checkDeptExistUser(deptId);
  }

  /**
   * 查询部门是否存在用户
   *
   * @param deptIds 部门 ID 组
   * @return 结果 true 存在 false 不存在
   */
  public boolean hasDeptUser(Long[] deptIds) {
    if (deptIds == null || deptIds.length == 0) {
      return false;
    }
    for (Long deptId : deptIds) {
      if (checkDeptExistUser(deptId)) {
        return true;
      }
    }
    return false;
  }

  /**
   * 根据部门名称查询部门信息
   *
   * @param deptName 部门名称
   * @return 部门信息
   */
  public SysDept selectDeptByName(String deptName) {
    return deptMapper.checkDeptNameUnique(deptName, null);
  }

  /**
   * 根据用户ID查询部门
   *
   * @param userId 用户ID
   * @return 部门信息
   */
  public SysDept selectDeptByUserId(Long userId) {
    // 这里需要根据实际业务逻辑实现
    return null;
  }

  /**
   * 修改子元素关系
   *
   * @param dept 被修改的部门
   * @param oldDeptId 修改前的父部门ID
   */
  public void updateDeptChildren(SysDept dept, Long oldDeptId) {
    updateDeptChildren(dept.getDeptId(), dept.getAncestors(), String.valueOf(oldDeptId));
  }

  /**
   * 修改所在部门的父级部门状态
   *
   * @param dept 当前部门
   */
  public void updateParentDeptStatus(SysDept dept) {
    updateParentDeptStatusNormal(dept);
  }

  /**
   * 校验部门名称是否唯一
   *
   * @param dept 部门信息
   * @return 结果
   */
  public boolean checkDeptNameUnique(SysDept dept) {
    Long deptId = Objects.isNull(dept.getDeptId()) ? -1L : dept.getDeptId();
    SysDept info = deptMapper.checkDeptNameUnique(dept.getDeptName(), dept.getParentId());
    if (Objects.nonNull(info) && info.getDeptId().longValue() != deptId.longValue()) {
      return false;
    }
    return true;
  }

  /**
   * 校验部门是否有数据权限
   *
   * @param deptId 部门 id
   */
  @Override
  public void checkDeptDataScope(Long deptId) {
    Long currentUserId = SecurityUtils.getLoginUser().getUserId();
    if (!SecurityUtils.isAdminUser(currentUserId) && Objects.nonNull(deptId)) {
      SysDept dept = new SysDept();
      dept.setDeptId(deptId);
      List<SysDept> depts = self.selectDeptList(dept);
      if (depts.isEmpty()) {
        throw new BadRequestException("无权限访问部门数据");
      }
    }
  }

  /**
   * 校验部门是否有数据权限
   *
   * @param deptId 部门 id
   * @param currentUserId 当前用户id
   */
  @Override
  public void checkDeptDataScope(Long deptId, Long currentUserId) {
    if (!SecurityUtils.isAdminUser(currentUserId) && Objects.nonNull(deptId)) {
      SysDept dept = new SysDept();
      dept.setDeptId(deptId);
      List<SysDept> depts = self.selectDeptList(dept);
      if (depts.isEmpty()) {
        throw new BadRequestException("无权限访问部门数据");
      }
    }
  }

  /**
   * 新增保存部门信息
   *
   * @param dept 部门信息
   * @return 结果
   */
  public int insertDept(SysDept dept) {
    SysDept info = deptMapper.selectDeptById(dept.getParentId());
    // 如果父节点不为正常状态，则不允许新增子节点
    if (!info.getStatus()) {
      throw new BadRequestException("部门已停用，不允许新增子部门");
    }
    dept.setAncestors(info.getAncestors() + "," + dept.getParentId());
    // 设置审计字段
    // dept.setCreateBy(SecurityUtils.getUserId());
    // dept.setCreateTime(LocalDateTime.now());
    // dept.setUpdateBy(SecurityUtils.getUserId());
    // dept.setUpdateTime(LocalDateTime.now());
    return deptMapper.insertDept(dept);
  }

  /**
   * 修改保存部门信息
   *
   * @param dept 部门信息
   * @return 结果
   */
  public int updateDept(SysDept dept) {
    SysDept newParentDept = deptMapper.selectDeptById(dept.getParentId());
    SysDept oldDept = deptMapper.selectDeptById(dept.getDeptId());
    if (Objects.nonNull(newParentDept) && Objects.nonNull(oldDept)) {
      String newAncestors = newParentDept.getAncestors() + "," + newParentDept.getDeptId();
      String oldAncestors = oldDept.getAncestors();
      dept.setAncestors(newAncestors);
      updateDeptChildren(dept.getDeptId(), newAncestors, oldAncestors);
    }
    int result = deptMapper.updateDept(dept);
    if (dept.getStatus()
        && StringUtils.isNotEmpty(dept.getAncestors())
        && !StringUtils.equals("0", dept.getAncestors())) {
      // 如果该部门是启用状态，则启用该部门的所有上级部门
      updateParentDeptStatusNormal(dept);
    }
    return result;
  }

  /**
   * 修改该部门的父级部门状态
   *
   * @param dept 当前部门
   */
  private void updateParentDeptStatusNormal(SysDept dept) {
    String ancestors = dept.getAncestors();
    Long[] deptIds = Convert.toLongArray(ancestors);
    deptMapper.updateDeptStatusNormal(deptIds);
  }

  /**
   * 修改子元素关系
   *
   * @param deptId 被修改的部门 ID
   * @param newAncestors 新的父 ID 集合
   * @param oldAncestors 旧的父 ID 集合
   */
  public void updateDeptChildren(Long deptId, String newAncestors, String oldAncestors) {
    List<SysDept> children = deptMapper.selectChildrenDeptById(deptId);
    for (SysDept child : children) {
      child.setAncestors(child.getAncestors().replaceFirst(oldAncestors, newAncestors));
    }
    if (children.size() > 0) {
      deptMapper.updateDeptChildren(children);
    }
  }

  /**
   * 删除部门管理信息
   *
   * @param deptId 部门 ID
   * @return 结果
   * @throws BadRequestException 删除失败时抛出异常
   */
  public int deleteDeptById(Long deptId) {
    int rows = deptMapper.deleteDeptById(deptId);
    if (rows == 0) {
      throw new BadRequestException("删除失败");
    }
    return rows;
  }

  /** 递归列表 */
  private void recursionFn(List<SysDept> list, SysDept t) {
    // 得到子节点列表
    List<SysDept> childList = getChildList(list, t);
    t.setChildren(childList);
    for (SysDept tChild : childList) {
      if (hasChild(list, tChild)) {
        recursionFn(list, tChild);
      }
    }
  }

  /** 得到子节点列表 */
  private List<SysDept> getChildList(List<SysDept> list, SysDept t) {
    List<SysDept> tlist = new ArrayList<SysDept>();
    Iterator<SysDept> it = list.iterator();
    while (it.hasNext()) {
      SysDept n = (SysDept) it.next();
      if (Objects.nonNull(n.getParentId())
          && n.getParentId().longValue() == t.getDeptId().longValue()) {
        tlist.add(n);
      }
    }
    return tlist;
  }

  /** 判断是否有子节点 */
  private boolean hasChild(List<SysDept> list, SysDept t) {
    return getChildList(list, t).size() > 0;
  }
}
