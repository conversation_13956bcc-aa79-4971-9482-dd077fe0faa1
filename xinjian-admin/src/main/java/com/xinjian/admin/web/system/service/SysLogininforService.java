package com.xinjian.admin.web.system.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xinjian.admin.web.monitor.mapper.SysLogininforMapper;
import com.xinjian.common.core.domain.entity.SysLogininfor;
import com.xinjian.common.core.domain.query.SysLogininforQuery;
import com.xinjian.common.exception.status400.BadRequestException;
import com.xinjian.common.properties.PaginationProperties;
import com.xinjian.common.service.ISysLogininforService;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/** 系统访问日志情况信息 服务层 */
@Service
@RequiredArgsConstructor
public class SysLogininforService implements ISysLogininforService {

  private final SysLogininforMapper logininforMapper;
  private final PaginationProperties paginationProperties;

  /**
   * 新增系统登录日志
   *
   * @param logininfor 访问日志对象
   */
  @Override
  public void insertLogininfor(SysLogininfor logininfor) {
    logininforMapper.insertLogininfor(logininfor);
  }

  /**
   * 查询系统登录日志集合
   *
   * @param query 查询参数对象
   * @return 登录记录集合
   */
  public List<SysLogininfor> selectLogininforList(SysLogininforQuery query) {
    return logininforMapper.selectLogininforList(query);
  }

  /**
   * 分页查询系统登录日志集合
   *
   * <p>使用统一分页方案处理分页和排序逻辑
   *
   * @param query 查询参数对象
   * @return 登录记录分页集合
   */
  public IPage<SysLogininfor> selectLogininforListByPage(SysLogininforQuery query) {
    // 处理分页和排序逻辑
    IPage<SysLogininfor> page = query.buildPage(paginationProperties);
    return logininforMapper.selectLogininforListByPage(page, query);
  }

  /**
   * 批量删除系统登录日志
   *
   * @param infoIds 需要删除的登录日志 ID
   * @throws BadRequestException 删除失败时抛出异常
   */
  public void deleteLogininforByIds(Long[] infoIds) {
    int rows = logininforMapper.deleteLogininforByIds(infoIds);
    if (rows == 0) {
      throw new BadRequestException("删除失败");
    }
  }

  /** 清空系统登录日志 */
  public void cleanLogininfor() {
    logininforMapper.cleanLogininfor();
  }
}
