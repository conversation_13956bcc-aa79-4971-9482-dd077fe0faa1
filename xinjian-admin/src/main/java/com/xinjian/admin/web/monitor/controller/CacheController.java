package com.xinjian.admin.web.monitor.controller;

import com.xinjian.admin.web.monitor.dto.CacheInfoResponse;
import com.xinjian.admin.web.monitor.dto.CommandStatResponse;
import com.xinjian.common.constant.CacheConstants;
import com.xinjian.common.core.domain.entity.SysCache;
import com.xinjian.common.properties.AppProperties;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Properties;
import java.util.Set;
import java.util.TreeSet;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisCallback;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.http.HttpStatus;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

/** 缓存监控 */
@RestController
@RequestMapping("/monitor/cache")
public class CacheController {
  private static final List<SysCache> caches = new ArrayList<SysCache>();
  @Autowired private RedisTemplate<String, String> redisTemplate;
  @Autowired private AppProperties appProperties;

  {
    caches.add(new SysCache(CacheConstants.LOGIN_TOKEN_KEY, "用户信息"));
    caches.add(new SysCache(CacheConstants.SYS_CONFIG_KEY, "配置信息"));
    caches.add(new SysCache(CacheConstants.SYS_DICT_KEY, "数据字典"));
    caches.add(new SysCache(CacheConstants.CAPTCHA_CODE_KEY, "验证码"));
    caches.add(new SysCache(CacheConstants.REPEAT_SUBMIT_KEY, "防重提交"));
    caches.add(new SysCache(CacheConstants.RATE_LIMIT_KEY, "限流处理"));
    caches.add(new SysCache(CacheConstants.PWD_ERR_CNT_KEY, "密码错误次数"));
  }

  @PreAuthorize("hasAnyAuthority('monitor:cache:list')")
  @GetMapping()
  public CacheInfoResponse getInfo() throws Exception {
    Properties info =
        (Properties) redisTemplate.execute((RedisCallback<Object>) connection -> connection.info());
    Properties commandStats =
        (Properties)
            redisTemplate.execute(
                (RedisCallback<Object>) connection -> connection.info("commandstats"));
    Object dbSize =
        redisTemplate.execute((RedisCallback<Object>) connection -> connection.dbSize());

    List<CommandStatResponse> commandStatList = new ArrayList<>();
    commandStats
        .stringPropertyNames()
        .forEach(
            key -> {
              String property = commandStats.getProperty(key);
              CommandStatResponse stat =
                  new CommandStatResponse(
                      StringUtils.removeStart(key, "cmdstat_"),
                      StringUtils.substringBetween(property, "calls=", ",usec"));
              commandStatList.add(stat);
            });
    return new CacheInfoResponse(info, dbSize, commandStatList);
  }

  @PreAuthorize("hasAnyAuthority('monitor:cache:list')")
  @GetMapping("/getNames")
  public List<SysCache> cache() {
    return caches;
  }

  @PreAuthorize("hasAnyAuthority('monitor:cache:list')")
  @GetMapping("/getKeys/{cacheName}")
  public Set<String> getCacheKeys(@PathVariable String cacheName) {
    // 构建完整的键模式，包含应用前缀
    String fullPattern = appProperties.getNamespace() + ":" + cacheName + "*";
    Set<String> cacheKeys = redisTemplate.keys(fullPattern);

    // 如果没有找到键，尝试不带应用前缀的模式
    if (cacheKeys == null || cacheKeys.isEmpty()) {
      fullPattern = cacheName + "*";
      cacheKeys = redisTemplate.keys(fullPattern);
    }

    return cacheKeys != null ? new TreeSet<>(cacheKeys) : new TreeSet<>();
  }

  @PreAuthorize("hasAnyAuthority('monitor:cache:list')")
  @GetMapping("/getValue/{cacheName}/{cacheKey}")
  public SysCache getCacheValue(@PathVariable String cacheName, @PathVariable String cacheKey) {
    String cacheValue = redisTemplate.opsForValue().get(cacheKey);
    return new SysCache(cacheName, cacheKey, cacheValue);
  }

  @PreAuthorize("hasAnyAuthority('monitor:cache:list')")
  @DeleteMapping("/clearCacheName/{cacheName}")
  @ResponseStatus(HttpStatus.NO_CONTENT)
  public void clearCacheName(@PathVariable String cacheName) {
    // 构建完整的键模式，包含应用前缀
    String fullPattern = appProperties.getNamespace() + ":" + cacheName + "*";
    Collection<String> cacheKeys = redisTemplate.keys(fullPattern);

    // 如果没有找到键，尝试不带应用前缀的模式
    if (cacheKeys == null || cacheKeys.isEmpty()) {
      fullPattern = cacheName + "*";
      cacheKeys = redisTemplate.keys(fullPattern);
    }

    if (cacheKeys != null && !cacheKeys.isEmpty()) {
      redisTemplate.delete(cacheKeys);
    }
  }

  @PreAuthorize("hasAnyAuthority('monitor:cache:list')")
  @DeleteMapping("/clearCacheKey/{cacheKey}")
  @ResponseStatus(HttpStatus.NO_CONTENT)
  public void clearCacheKey(@PathVariable String cacheKey) {
    redisTemplate.delete(cacheKey);
  }

  @PreAuthorize("hasAnyAuthority('monitor:cache:list')")
  @DeleteMapping("/clearCacheAll")
  @ResponseStatus(HttpStatus.NO_CONTENT)
  public void clearCacheAll() {
    Collection<String> cacheKeys = redisTemplate.keys("*");
    redisTemplate.delete(cacheKeys);
  }
}
