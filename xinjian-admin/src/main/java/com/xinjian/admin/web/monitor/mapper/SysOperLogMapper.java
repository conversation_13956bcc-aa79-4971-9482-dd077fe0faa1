package com.xinjian.admin.web.monitor.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xinjian.common.core.domain.entity.SysOperLog;
import com.xinjian.common.core.domain.query.SysOperLogQuery;
import java.util.List;
import org.apache.ibatis.annotations.Param;

/** 操作日志 数据层 */
public interface SysOperLogMapper {
  /**
   * 新增操作日志
   *
   * @param operLog 操作日志对象
   */
  public void insertOperlog(SysOperLog operLog);

  /**
   * 查询系统操作日志集合
   *
   * @param query 查询参数对象
   * @return 操作日志集合
   */
  public List<SysOperLog> selectOperLogList(SysOperLogQuery query);

  /**
   * 查询系统操作日志集合
   *
   * @param page 分页对象
   * @param query 查询参数对象
   * @return 当页的数据列表。分页的总数等信息，由分页插件填充到传入的 page 对象中。
   */
  public List<SysOperLog> selectOperLogListPage(
      @Param("page") IPage<SysOperLog> page, @Param("query") SysOperLogQuery query);

  /**
   * 批量删除系统操作日志
   *
   * @param operIds 需要删除的操作日志 ID
   * @return 结果
   */
  public int deleteOperLogByIds(Long[] operIds);

  /**
   * 查询操作日志详细
   *
   * @param operId 操作 ID
   * @return 操作日志对象
   */
  public SysOperLog selectOperLogById(Long operId);

  /** 清空操作日志 */
  public void cleanOperLog();
}
