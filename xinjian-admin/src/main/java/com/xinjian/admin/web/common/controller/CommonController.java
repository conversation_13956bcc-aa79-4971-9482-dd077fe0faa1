package com.xinjian.admin.web.common.controller;

import com.xinjian.admin.web.common.dto.FileUploadResponse;
import com.xinjian.admin.web.common.dto.FilesUploadResponse;
import com.xinjian.common.exception.status400.BadRequestException;
import com.xinjian.common.properties.ApiProperties;
import com.xinjian.common.properties.StorageProperties;
import com.xinjian.common.service.FileStorageService;
import com.xinjian.common.utils.ServletUtils;
import com.xinjian.common.utils.file.FileUtils;
import java.util.ArrayList;
import java.util.List;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

/** 通用请求处理 */
@Slf4j
@RestController
@RequestMapping("/common")
@RequiredArgsConstructor
public class CommonController {
  private static final String FILE_DELIMETER = ",";
  private final ApiProperties apiProperties;
  private final StorageProperties storageProperties;
  private final FileStorageService fileStorageService;

  /**
   * 通用下载请求
   *
   * @param fileName 文件名称
   * @param delete 是否删除
   */
  @GetMapping("/download")
  public void fileDownload(
      String fileName, Boolean delete, HttpServletResponse response, HttpServletRequest request) {
    try {
      if (!FileUtils.checkAllowDownload(fileName)) {
        throw new Exception(String.format("文件名称 (%s) 非法，不允许下载。 ", fileName));
      }
      String realFileName =
          System.currentTimeMillis() + fileName.substring(fileName.indexOf("_") + 1);
      String filePath = fileStorageService.getDownloadPath() + fileName;

      response.setContentType(MediaType.APPLICATION_OCTET_STREAM_VALUE);
      FileUtils.setAttachmentResponseHeader(response, realFileName);
      FileUtils.writeBytes(filePath, response.getOutputStream());
      if (delete) {
        FileUtils.deleteFile(filePath);
      }
    } catch (Exception e) {
      log.error("下载文件失败", e);
    }
  }

  /** 通用上传请求（单个） */
  @PostMapping("/upload")
  @ResponseStatus(HttpStatus.CREATED)
  public FileUploadResponse uploadFile(MultipartFile file, HttpServletRequest request)
      throws Exception {
    try {
      // 上传文件路径
      String filePath = fileStorageService.getUploadPath();
      // 上传并返回新文件名称
      String fileName = fileStorageService.upload(filePath, file);
      String url = ServletUtils.getDomain(request) + fileName;

      return new FileUploadResponse(
          url, fileName, FileUtils.getName(fileName), file.getOriginalFilename());
    } catch (Exception e) {
      throw new BadRequestException("文件上传失败：" + e.getMessage());
    }
  }

  /** 通用上传请求（多个） */
  @PostMapping("/uploads")
  @ResponseStatus(HttpStatus.CREATED)
  public FilesUploadResponse uploadFiles(List<MultipartFile> files, HttpServletRequest request)
      throws Exception {
    try {
      // 上传文件路径
      String filePath = fileStorageService.getUploadPath();
      List<String> urls = new ArrayList<String>();
      List<String> fileNames = new ArrayList<String>();
      List<String> newFileNames = new ArrayList<String>();
      List<String> originalFilenames = new ArrayList<String>();
      for (MultipartFile file : files) {
        // 上传并返回新文件名称
        String fileName = fileStorageService.upload(filePath, file);
        String url = ServletUtils.getDomain(request) + fileName;
        urls.add(url);
        fileNames.add(fileName);
        newFileNames.add(FileUtils.getName(fileName));
        originalFilenames.add(file.getOriginalFilename());
      }

      return new FilesUploadResponse(
          StringUtils.join(urls, FILE_DELIMETER),
          StringUtils.join(fileNames, FILE_DELIMETER),
          StringUtils.join(newFileNames, FILE_DELIMETER),
          StringUtils.join(originalFilenames, FILE_DELIMETER));
    } catch (Exception e) {
      throw new BadRequestException("文件上传失败：" + e.getMessage());
    }
  }

  /** 本地资源通用下载 */
  @GetMapping("/download/resource")
  public void resourceDownload(
      String resource, HttpServletRequest request, HttpServletResponse response) throws Exception {
    try {
      if (!FileUtils.checkAllowDownload(resource)) {
        throw new Exception(String.format("资源文件 (%s) 非法，不允许下载。 ", resource));
      }
      // 本地资源路径
      String localPath = storageProperties.getPath();
      // 数据库资源地址
      String downloadPath =
          localPath
              + StringUtils.substringAfter(
                  resource, apiProperties.getBaseUrl() + storageProperties.getEndpoint());
      // 下载名称
      String downloadName = StringUtils.substringAfterLast(downloadPath, "/");
      response.setContentType(MediaType.APPLICATION_OCTET_STREAM_VALUE);
      FileUtils.setAttachmentResponseHeader(response, downloadName);
      FileUtils.writeBytes(downloadPath, response.getOutputStream());
    } catch (Exception e) {
      log.error("下载文件失败", e);
    }
  }
}
