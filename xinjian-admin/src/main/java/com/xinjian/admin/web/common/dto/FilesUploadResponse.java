package com.xinjian.admin.web.common.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/** 多文件上传响应对象 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class FilesUploadResponse {

  /** 文件访问 URL 列表（逗号分隔） */
  private String urls;

  /** 文件存储名称列表（逗号分隔） */
  private String fileNames;

  /** 文件新名称列表（逗号分隔） */
  private String newFileNames;

  /** 原始文件名列表（逗号分隔） */
  private String originalFilenames;
}
