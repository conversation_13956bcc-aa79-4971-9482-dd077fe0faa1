package com.xinjian.admin.web.system.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xinjian.admin.web.system.mapper.SysDictDataMapper;
import com.xinjian.common.core.domain.entity.SysDictData;
import com.xinjian.common.core.domain.query.SysDictDataQuery;
import com.xinjian.common.exception.status404.ResourceNotFoundException;
import com.xinjian.common.exception.status500.ServiceException;
import com.xinjian.common.properties.PaginationProperties;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/** 字典 业务层处理 */
@Service
@RequiredArgsConstructor
public class SysDictDataService {
  private final SysDictDataMapper dictDataMapper;
  private final SysDictTypeService dictTypeService;
  private final PaginationProperties paginationProperties;

  /**
   * 根据条件分页查询字典数据
   *
   * @param dictData 字典数据信息
   * @return 字典数据集合信息
   */
  public List<SysDictData> selectDictDataList(SysDictData dictData) {
    return dictDataMapper.selectDictDataList(dictData);
  }

  /**
   * 分页查询字典数据集合
   *
   * @param query 查询参数对象
   * @return 分页字典数据集合
   */
  public IPage<SysDictData> selectDictDataListByPage(SysDictDataQuery query) {

    IPage<SysDictData> page = query.buildPage(paginationProperties);
    return dictDataMapper.selectDictDataListByPage(page, query);
  }

  /**
   * 根据字典类型和字典键值查询字典数据信息
   *
   * @param dictType 字典类型
   * @param dictValue 字典键值
   * @return 字典标签
   */
  public String selectDictLabel(String dictType, String dictValue) {
    return dictDataMapper.selectDictLabel(dictType, dictValue);
  }

  /**
   * 根据字典数据 ID 查询信息
   *
   * @param dictCode 字典数据 ID
   * @return 字典数据
   */
  public SysDictData selectDictDataById(Long dictCode) {
    return dictDataMapper.selectDictDataById(dictCode);
  }

  /**
   * 批量删除字典数据信息
   *
   * @param dictCodes 需要删除的字典数据 ID
   */
  public void deleteDictDataByIds(Long[] dictCodes) {
    // 收集需要清除缓存的字典类型
    java.util.Set<String> dictTypes = new java.util.HashSet<>();

    for (Long dictCode : dictCodes) {
      SysDictData dictData = selectDictDataById(dictCode);
      if (dictData != null) {
        dictTypes.add(dictData.getDictType());
        dictDataMapper.deleteDictDataById(dictCode);
      }
    }

    // 清除受影响的字典类型缓存
    for (String dictType : dictTypes) {
      dictTypeService.evictDictCache(dictType);
    }
  }

  /**
   * 新增保存字典数据信息
   *
   * @param data 字典数据信息
   */
  public void insertDictData(SysDictData data) {
    int row = dictDataMapper.insertDictData(data);
    if (row <= 0) {
      throw new ServiceException("新增字典数据失败，未知错误");
    }
    // 调用 TypeService 的缓存清除方法
    dictTypeService.evictDictCache(data.getDictType());
  }

  /**
   * 修改保存字典数据信息
   *
   * @param data 字典数据信息
   */
  public void updateDictData(SysDictData data) {
    int row = dictDataMapper.updateDictData(data);
    if (row <= 0) {
      throw new ResourceNotFoundException("字典数据不存在，无法更新");
    }
    // 调用 TypeService 的缓存清除方法
    dictTypeService.evictDictCache(data.getDictType());
  }
}
