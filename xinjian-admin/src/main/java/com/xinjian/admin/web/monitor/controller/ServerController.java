package com.xinjian.admin.web.monitor.controller;

import com.xinjian.admin.web.monitor.entity.ServerInfo;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/** 服务器监控 */
@RestController
@RequestMapping("/monitor/server")
public class ServerController {
  @PreAuthorize("hasAnyAuthority('monitor:server:list')")
  @GetMapping()
  public ServerInfo getInfo() throws Exception {
    ServerInfo server = new ServerInfo();
    server.copyTo();
    return server;
  }
}
