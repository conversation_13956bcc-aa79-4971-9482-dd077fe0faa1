package com.xinjian.admin.web.common.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/** 文件上传响应对象 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class FileUploadResponse {

  /** 文件访问 URL */
  private String url;

  /** 文件存储名称 */
  private String fileName;

  /** 文件新名称（不含路径） */
  private String newFileName;

  /** 原始文件名 */
  private String originalFilename;
}
