package com.xinjian.admin.web.system.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xinjian.admin.web.system.service.SysConfigService;
import com.xinjian.common.annotation.Log;
import com.xinjian.common.core.domain.dto.LoginUser;
import com.xinjian.common.core.domain.entity.SysConfig;
import com.xinjian.common.core.domain.query.SysConfigQuery;
import com.xinjian.common.core.page.TableDataInfo;
import com.xinjian.common.enums.BusinessType;
import com.xinjian.common.exception.status409.ConflictException;
import com.xinjian.common.exception.status500.ServiceException;
import com.xinjian.common.utils.poi.ExcelUtil;
import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/** 参数配置 信息操作处理 */
@RestController
@RequestMapping("/system/config")
public class SysConfigController {
  @Autowired private SysConfigService configService;

  /** 获取参数配置列表 */
  @PreAuthorize("hasAnyAuthority('system:config:list')")
  @GetMapping("/list")
  public TableDataInfo<SysConfig> list(SysConfigQuery query) {
    IPage<SysConfig> page = configService.selectConfigListByPage(query);
    return new TableDataInfo<>(page);
  }

  @Log(title = "参数管理", businessType = BusinessType.EXPORT)
  @PreAuthorize("hasAnyAuthority('system:config:export')")
  @PostMapping("/export")
  public void export(HttpServletResponse response, SysConfigQuery query) {
    List<SysConfig> list = configService.selectConfigList(query);
    ExcelUtil<SysConfig> util = new ExcelUtil<SysConfig>(SysConfig.class);
    util.exportExcel(response, list, "参数数据");
  }

  /** 根据参数编号获取详细信息 */
  @PreAuthorize("hasAnyAuthority('system:config:query')")
  @GetMapping(value = "/{configId}")
  public SysConfig getInfo(@PathVariable Long configId) {
    return configService.selectConfigById(configId);
  }

  /** 根据参数键名查询参数值 */
  @GetMapping(value = "/configKey/{configKey}")
  public String getConfigKey(@PathVariable String configKey) {
    return configService.selectConfigByKey(configKey);
  }

  /** 新增参数配置 */
  @PreAuthorize("hasAnyAuthority('system:config:add')")
  @Log(title = "参数管理", businessType = BusinessType.INSERT)
  @PostMapping
  @ResponseStatus(HttpStatus.CREATED)
  public SysConfig add(
      @Validated @RequestBody SysConfig config, @AuthenticationPrincipal LoginUser loginUser) {
    if (!configService.checkConfigKeyUnique(config)) {
      throw new ConflictException("新增参数'" + config.getConfigName() + "'失败，参数键名已存在");
    }
    configService.insertConfig(config);
    return config;
  }

  /** 修改参数配置 */
  @PreAuthorize("hasAnyAuthority('system:config:edit')")
  @Log(title = "参数管理", businessType = BusinessType.UPDATE)
  @PutMapping
  public SysConfig edit(
      @Validated @RequestBody SysConfig config, @AuthenticationPrincipal LoginUser loginUser) {
    if (!configService.checkConfigKeyUnique(config)) {
      throw new ConflictException("修改参数'" + config.getConfigName() + "'失败，参数键名已存在");
    }
    if (configService.updateConfig(config) <= 0) {
      throw new ServiceException("修改参数配置失败");
    }
    return config;
  }

  /** 删除参数配置 */
  @PreAuthorize("hasAnyAuthority('system:config:remove')")
  @Log(title = "参数管理", businessType = BusinessType.DELETE)
  @DeleteMapping("/{configIds}")
  @ResponseStatus(HttpStatus.NO_CONTENT)
  public void remove(@PathVariable Long[] configIds) {
    configService.deleteConfigByIds(configIds);
  }

  /** 刷新参数缓存 */
  @PreAuthorize("hasAnyAuthority('system:config:remove')")
  @Log(title = "参数管理", businessType = BusinessType.CLEAN)
  @DeleteMapping("/refreshCache")
  @ResponseStatus(HttpStatus.NO_CONTENT)
  public void refreshCache() {
    configService.resetConfigCache();
  }
}
