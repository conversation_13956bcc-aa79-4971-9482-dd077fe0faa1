package com.xinjian.admin.web.system.service;

import cn.hutool.core.util.RandomUtil;
import com.xinjian.admin.web.system.mapper.SysMenuMapper;
import com.xinjian.admin.web.system.mapper.SysRoleMapper;
import com.xinjian.admin.web.system.mapper.SysRoleMenuMapper;
import com.xinjian.common.core.domain.TreeSelect;
import com.xinjian.common.core.domain.entity.SysMenu;
import com.xinjian.common.core.domain.entity.SysRole;
import com.xinjian.common.core.domain.vo.MetaVo;
import com.xinjian.common.core.domain.vo.RouterVo;
import com.xinjian.common.enums.ComponentType;
import com.xinjian.common.enums.MenuType;
import com.xinjian.common.service.ISysMenuService;
import com.xinjian.common.utils.CaseUtils;
import com.xinjian.common.utils.SecurityUtils;
import java.util.*;
import java.util.Objects;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/** 菜单 业务层处理 */
@Slf4j
@Service
public class SysMenuService implements ISysMenuService {
  public static final String PREMISSION_STRING = "perms[\"{0}\"]";

  @Autowired private SysMenuMapper menuMapper;

  @Autowired private SysRoleMapper roleMapper;

  @Autowired private SysRoleMenuMapper roleMenuMapper;

  /**
   * 根据用户查询系统菜单列表
   *
   * @param userId 用户 ID
   * @return 菜单列表
   */
  public List<SysMenu> selectMenuList(Long userId) {
    try {
      return selectMenuList(new SysMenu(), userId);
    } catch (Exception e) {
      log.error("查询用户菜单列表失败，用户 ID: {}, 错误：{}", userId, e.getMessage(), e);
      return Collections.emptyList();
    }
  }

  /**
   * 查询系统菜单列表
   *
   * @param menu 菜单信息
   * @return 菜单列表
   */
  public List<SysMenu> selectMenuList(SysMenu menu, Long userId) {
    try {
      List<SysMenu> menuList = null;
      // 管理员显示所有菜单信息
      if (SecurityUtils.isAdminUser(userId)) {
        menuList = menuMapper.selectMenuList(menu);
      } else {
        menuList = menuMapper.selectMenuListByUserId(menu, userId);
      }
      return menuList;
    } catch (Exception e) {
      log.error("查询菜单列表失败，用户 ID: {}, 错误：{}", userId, e.getMessage(), e);
      return Collections.emptyList();
    }
  }

  /**
   * 根据用户 ID 查询权限
   *
   * @param userId 用户 ID
   * @return 权限列表
   */
  @Override
  public Set<String> selectMenuPermsByUserId(Long userId) {
    try {
      List<String> perms = menuMapper.selectMenuPermsByUserId(userId);
      Set<String> permsSet = new HashSet<>();
      for (String perm : perms) {
        if (StringUtils.isNotEmpty(perm)) {
          permsSet.addAll(Arrays.asList(perm.trim().split(",")));
        }
      }
      return permsSet;
    } catch (Exception e) {
      log.error("查询用户权限失败，用户 ID: {}, 错误：{}", userId, e.getMessage(), e);
      return Collections.emptySet();
    }
  }

  /**
   * 根据角色 ID 查询权限
   *
   * @param roleId 角色 ID
   * @return 权限列表
   */
  public Set<String> selectMenuPermsByRoleId(Long roleId) {
    try {
      List<String> perms = menuMapper.selectMenuPermsByRoleId(roleId);
      Set<String> permsSet = new HashSet<>();
      for (String perm : perms) {
        if (StringUtils.isNotEmpty(perm)) {
          permsSet.addAll(Arrays.asList(perm.trim().split(",")));
        }
      }
      return permsSet;
    } catch (Exception e) {
      log.error("查询角色权限失败，角色 ID: {}, 错误：{}", roleId, e.getMessage(), e);
      return Collections.emptySet();
    }
  }

  /**
   * 根据角色 ID 列表查询权限
   *
   * @param roleIds 角色 ID 列表
   * @return 权限列表
   */
  @Override
  public Set<String> selectMenuPermsByRoleIds(List<Long> roleIds) {
    try {
      List<String> perms = menuMapper.selectMenuPermsByRoleIds(roleIds);
      Set<String> permsSet = new HashSet<>();
      for (String perm : perms) {
        if (StringUtils.isNotEmpty(perm)) {
          permsSet.addAll(Arrays.asList(perm.trim().split(",")));
        }
      }
      return permsSet;
    } catch (Exception e) {
      log.error("查询角色权限列表失败，角色 ID 数量：{}, 错误：{}", roleIds.size(), e.getMessage(), e);
      return Collections.emptySet();
    }
  }

  /**
   * 根据用户 ID 查询菜单
   *
   * @param userId 用户名称
   * @return 菜单列表
   */
  public List<SysMenu> selectMenuTreeByUserId(Long userId) {
    try {
      List<SysMenu> menus = null;
      if (SecurityUtils.isAdminUser(userId)) {
        menus = menuMapper.selectMenuTreeAll();
      } else {
        menus = menuMapper.selectMenuTreeByUserId(userId);
      }
      return getChildPerms(menus, 0);
    } catch (Exception e) {
      log.error("查询用户菜单树失败，用户 ID: {}, 错误：{}", userId, e.getMessage(), e);
      return Collections.emptyList();
    }
  }

  /**
   * 根据角色 ID 查询菜单树信息
   *
   * @param roleId 角色 ID
   * @return 选中菜单列表
   */
  public List<Long> selectMenuListByRoleId(Long roleId) {
    try {
      SysRole role = roleMapper.selectRoleById(roleId);
      if (role == null) {
        log.warn("查询角色菜单树失败，角色不存在，角色 ID: {}", roleId);
        return Collections.emptyList();
      }
      return menuMapper.selectMenuListByRoleId(roleId, role.isMenuCheckStrictly());
    } catch (Exception e) {
      log.error("查询角色菜单树失败，角色 ID: {}, 错误：{}", roleId, e.getMessage(), e);
      return Collections.emptyList();
    }
  }

  /**
   * 构建前端路由所需要的菜单
   *
   * @param menus 菜单列表
   * @return 路由列表
   */
  public List<RouterVo> buildMenus(List<SysMenu> menus, String prefix) {
    try {
      List<RouterVo> routers = new LinkedList<>();
      for (SysMenu menu : menus) {
        RouterVo router = new RouterVo();
        router.setHidden(menu.getVisible() == null || !menu.getVisible());
        router.setName(prefix + getRouteName(menu));
        router.setPath(getRouterPath(menu));
        router.setComponent(getComponent(menu));
        router.setQuery(menu.getQuery());
        router.setMeta(
            new MetaVo(menu.getMenuName(), menu.getIcon(), menu.getIsCache(), menu.getPath()));
        List<SysMenu> cMenus = menu.getChildren();
        if (!cMenus.isEmpty() && MenuType.DIR.getCode().equals(menu.getMenuType())) {
          router.setAlwaysShow(true);
          router.setRedirect("noRedirect");
          router.setChildren(buildMenus(cMenus, prefix + getRouteName(menu)));
        } else if (isMenuFrame(menu)) {
          router.setMeta(null);
          List<RouterVo> childrenList = new ArrayList<>();
          RouterVo children = new RouterVo();
          children.setPath(menu.getPath());
          children.setComponent(menu.getComponent());
          children.setName(prefix + CaseUtils.toPascalCase(menu.getPath()));
          children.setMeta(
              new MetaVo(menu.getMenuName(), menu.getIcon(), menu.getIsCache(), menu.getPath()));
          children.setQuery(menu.getQuery());
          childrenList.add(children);
          router.setChildren(childrenList);
        } else if (menu.getParentId().intValue() == 0 && isInnerLink(menu)) {
          router.setMeta(new MetaVo(menu.getMenuName(), menu.getIcon()));
          router.setPath("/inner");
          List<RouterVo> childrenList = new ArrayList<>();
          RouterVo children = new RouterVo();
          String routerPath =
              StringUtils.replaceEach(
                  menu.getPath(), new String[] {"http://", "https://"}, new String[] {"", ""});
          children.setPath(routerPath);
          children.setComponent(ComponentType.INNER_LINK.getValue());
          children.setName(prefix + CaseUtils.toPascalCase(routerPath));
          children.setMeta(new MetaVo(menu.getMenuName(), menu.getIcon(), menu.getPath()));
          childrenList.add(children);
          router.setChildren(childrenList);
        }
        routers.add(router);
      }
      return routers;
    } catch (Exception e) {
      log.error("构建前端路由失败，菜单数量：{}, 错误：{}", menus.size(), e.getMessage(), e);
      return Collections.emptyList();
    }
  }

  /**
   * 构建前端所需要树结构
   *
   * @param menus 菜单列表
   * @return 树结构列表
   */
  public List<SysMenu> buildMenuTree(List<SysMenu> menus) {
    try {
      List<SysMenu> returnList = new ArrayList<SysMenu>();
      List<Long> tempList = menus.stream().map(SysMenu::getMenuId).collect(Collectors.toList());
      for (Iterator<SysMenu> iterator = menus.iterator(); iterator.hasNext(); ) {
        SysMenu menu = (SysMenu) iterator.next();
        // 如果是顶级节点，遍历该父节点的所有子节点
        if (!tempList.contains(menu.getParentId())) {
          recursionFn(menus, menu);
          returnList.add(menu);
        }
      }
      if (returnList.isEmpty()) {
        returnList = menus;
      }
      return returnList;
    } catch (Exception e) {
      log.error("构建菜单树失败，菜单数量：{}, 错误：{}", menus.size(), e.getMessage(), e);
      return Collections.emptyList();
    }
  }

  /**
   * 构建前端所需要下拉树结构
   *
   * @param menus 菜单列表
   * @return 下拉树结构列表
   */
  public List<TreeSelect> buildMenuTreeSelect(List<SysMenu> menus) {
    try {
      List<SysMenu> menuTrees = buildMenuTree(menus);
      return menuTrees.stream().map(TreeSelect::new).collect(Collectors.toList());
    } catch (Exception e) {
      log.error("构建菜单下拉树失败，菜单数量：{}, 错误：{}", menus.size(), e.getMessage(), e);
      return Collections.emptyList();
    }
  }

  /**
   * 根据菜单 ID 查询信息
   *
   * @param menuId 菜单 ID
   * @return 菜单信息
   */
  public SysMenu selectMenuById(Long menuId) {
    try {
      SysMenu menu = menuMapper.selectMenuById(menuId);
      if (menu == null) {
        log.warn("查询菜单信息失败，菜单不存在，菜单 ID: {}", menuId);
      }
      return menu;
    } catch (Exception e) {
      log.error("查询菜单信息失败，菜单 ID: {}, 错误：{}", menuId, e.getMessage(), e);
      return null;
    }
  }

  /**
   * 是否存在菜单子节点
   *
   * @param menuId 菜单 ID
   * @return 结果
   */
  public boolean hasChildByMenuId(Long menuId) {
    try {
      int result = menuMapper.hasChildByMenuId(menuId);
      return result > 0;
    } catch (Exception e) {
      log.error("检查菜单子节点失败，菜单 ID: {}, 错误：{}", menuId, e.getMessage(), e);
      return false;
    }
  }

  /**
   * 查询菜单使用数量
   *
   * @param menuId 菜单 ID
   * @return 结果
   */
  public boolean checkMenuExistRole(Long menuId) {
    try {
      int result = roleMenuMapper.checkMenuExistRole(menuId);
      return result > 0;
    } catch (Exception e) {
      log.error("检查菜单是否被角色使用失败，菜单 ID: {}, 错误：{}", menuId, e.getMessage(), e);
      return false;
    }
  }

  /**
   * 新增保存菜单信息
   *
   * @param menu 菜单信息
   * @return 结果
   */
  public int insertMenu(SysMenu menu) {
    try {
      int result = menuMapper.insertMenu(menu);
      if (result > 0) {
        log.info("成功新增菜单，菜单名称：{}, 菜单 ID: {}", menu.getMenuName(), menu.getMenuId());
      }
      return result;
    } catch (Exception e) {
      log.error("新增菜单失败，菜单名称：{}, 错误：{}", menu.getMenuName(), e.getMessage(), e);
      return 0;
    }
  }

  /**
   * 修改保存菜单信息
   *
   * @param menu 菜单信息
   * @return 结果
   */
  public int updateMenu(SysMenu menu) {
    try {
      int result = menuMapper.updateMenu(menu);
      if (result > 0) {
        log.info("成功修改菜单，菜单 ID: {}, 菜单名称：{}", menu.getMenuId(), menu.getMenuName());
      }
      return result;
    } catch (Exception e) {
      log.error(
          "修改菜单失败，菜单 ID: {}, 菜单名称：{}, 错误：{}",
          menu.getMenuId(),
          menu.getMenuName(),
          e.getMessage(),
          e);
      return 0;
    }
  }

  /**
   * 删除菜单管理信息
   *
   * @param menuId 菜单 ID
   * @return 结果
   */
  public int deleteMenuById(Long menuId) {
    try {
      int result = menuMapper.deleteMenuById(menuId);
      if (result > 0) {
        log.info("成功删除菜单，菜单 ID: {}", menuId);
      }
      return result;
    } catch (Exception e) {
      log.error("删除菜单失败，菜单 ID: {}, 错误：{}", menuId, e.getMessage(), e);
      return 0;
    }
  }

  @Override
  public Set<String> selectAllMenuPerms() {
    try {
      List<String> perms = menuMapper.selectAllMenuPerms();
      if (perms == null) {
        return Collections.emptySet();
      }
      // 过滤掉空值，并转换为 Set
      return perms.stream().filter(StringUtils::isNotBlank).collect(Collectors.toSet());
    } catch (Exception e) {
      log.error("查询所有菜单权限失败，错误：{}", e.getMessage(), e);
      return Collections.emptySet();
    }
  }

  /**
   * 校验菜单名称是否唯一
   *
   * @param menu 菜单信息
   * @return 结果
   */
  public boolean checkMenuNameUnique(SysMenu menu) {
    try {
      Long menuId = Objects.isNull(menu.getMenuId()) ? -1L : menu.getMenuId();
      SysMenu info = menuMapper.checkMenuNameUnique(menu.getMenuName(), menu.getParentId());
      boolean isUnique =
          !(Objects.nonNull(info) && info.getMenuId().longValue() != menuId.longValue());
      return isUnique ? true : false;
    } catch (Exception e) {
      log.error(
          "检查菜单名称唯一性失败，菜单名称：{}, 父菜单 ID: {}, 错误：{}",
          menu.getMenuName(),
          menu.getParentId(),
          e.getMessage(),
          e);
      return false;
    }
  }

  /**
   * 获取路由名称
   *
   * @param menu 菜单信息
   * @return 路由名称
   */
  public String getRouteName(SysMenu menu) {
    try {
      // 外链菜单不能使用 path 作为 name，使用 query 参数作为 name，若无 query 则随机生成字符串，防止路由命名冲突
      if (menu.getPath().contains("https") || menu.getPath().contains("http")) {
        String BASE_CHAR = "abcdefghijklmnopqrstuvwxyz";
        String hackName =
            StringUtils.isBlank(menu.getQuery())
                ? RandomUtil.randomString(BASE_CHAR, 3)
                : menu.getQuery();
        return CaseUtils.toPascalCase(hackName);
      }
      // 非外链并且是一级目录（类型为目录）name 设为空，其他菜单使用 path 大驼峰作为 name，子路由拼接父路由作为前缀
      return isMenuFrame(menu) ? "" : CaseUtils.toPascalCase(menu.getPath());
    } catch (Exception e) {
      log.error(
          "获取路由名称失败，菜单 ID: {}, 菜单名称：{}, 错误：{}",
          menu.getMenuId(),
          menu.getMenuName(),
          e.getMessage(),
          e);
      return "";
    }
  }

  /**
   * 获取路由地址
   *
   * @param menu 菜单信息
   * @return 路由地址
   */
  public String getRouterPath(SysMenu menu) {
    try {
      String routerPath = menu.getPath();
      // 内链打开外网方式
      if (menu.getParentId().intValue() != 0 && isInnerLink(menu)) {
        routerPath = innerLinkReplaceEach(routerPath);
      }
      // 非外链并且是一级目录（类型为目录）
      if (0 == menu.getParentId().intValue()
          && MenuType.DIR.getCode().equals(menu.getMenuType())
          && !menu.getIsFrame()) {
        routerPath = "/" + menu.getPath();
      }
      // 非外链并且是一级目录（类型为菜单）
      else if (isMenuFrame(menu)) {
        routerPath = "/";
      }
      return routerPath;
    } catch (Exception e) {
      log.error(
          "获取路由地址失败，菜单 ID: {}, 菜单名称：{}, 错误：{}",
          menu.getMenuId(),
          menu.getMenuName(),
          e.getMessage(),
          e);
      return "/";
    }
  }

  /**
   * 获取组件信息
   *
   * @param menu 菜单信息
   * @return 组件信息
   */
  public String getComponent(SysMenu menu) {
    try {
      String component = ComponentType.LAYOUT.getValue();
      if (StringUtils.isNotEmpty(menu.getComponent()) && !isMenuFrame(menu)) {
        component = menu.getComponent();
      } else if (StringUtils.isBlank(menu.getComponent())
          && menu.getParentId().intValue() != 0
          && isInnerLink(menu)) {
        component = ComponentType.INNER_LINK.getValue();
      } else if (StringUtils.isBlank(menu.getComponent()) && isParentView(menu)) {
        component = ComponentType.PARENT_VIEW.getValue();
      }
      return component;
    } catch (Exception e) {
      log.error(
          "获取组件信息失败，菜单 ID: {}, 菜单名称：{}, 错误：{}",
          menu.getMenuId(),
          menu.getMenuName(),
          e.getMessage(),
          e);
      return ComponentType.LAYOUT.getValue();
    }
  }

  /**
   * 是否为菜单内部跳转
   *
   * @param menu 菜单信息
   * @return 结果
   */
  public boolean isMenuFrame(SysMenu menu) {
    return menu.getParentId().intValue() == 0
        && MenuType.MENU.getCode().equals(menu.getMenuType())
        && !menu.getIsFrame();
  }

  /**
   * 是否为内链组件
   *
   * @param menu 菜单信息
   * @return 结果
   */
  public boolean isInnerLink(SysMenu menu) {
    return !menu.getIsFrame() && StringUtils.startsWithAny(menu.getPath(), "http://", "https://");
  }

  /**
   * 是否为 parent_view 组件
   *
   * @param menu 菜单信息
   * @return 结果
   */
  public boolean isParentView(SysMenu menu) {
    return menu.getParentId().intValue() != 0 && MenuType.DIR.getCode().equals(menu.getMenuType());
  }

  /**
   * 根据父节点的 ID 获取所有子节点
   *
   * @param list 分类表
   * @param parentId 传入的父节点 ID
   * @return String
   */
  public List<SysMenu> getChildPerms(List<SysMenu> list, int parentId) {
    try {
      List<SysMenu> returnList = new ArrayList<SysMenu>();
      for (Iterator<SysMenu> iterator = list.iterator(); iterator.hasNext(); ) {
        SysMenu t = (SysMenu) iterator.next();
        // 一、根据传入的某个父节点 ID，遍历该父节点的所有子节点
        if (t.getParentId() == parentId) {
          recursionFn(list, t);
          returnList.add(t);
        }
      }
      return returnList;
    } catch (Exception e) {
      log.error("获取子节点失败，父节点 ID: {}, 错误：{}", parentId, e.getMessage(), e);
      return Collections.emptyList();
    }
  }

  /**
   * 递归列表
   *
   * @param list 分类表
   * @param t 子节点
   */
  private void recursionFn(List<SysMenu> list, SysMenu t) {
    try {
      // 得到子节点列表
      List<SysMenu> childList = getChildList(list, t);
      t.setChildren(childList);
      for (SysMenu tChild : childList) {
        if (hasChild(list, tChild)) {
          recursionFn(list, tChild);
        }
      }
    } catch (Exception e) {
      log.error("递归列表失败，菜单 ID: {}, 错误：{}", t.getMenuId(), e.getMessage(), e);
    }
  }

  /** 得到子节点列表 */
  private List<SysMenu> getChildList(List<SysMenu> list, SysMenu t) {
    try {
      List<SysMenu> tlist = new ArrayList<SysMenu>();
      Iterator<SysMenu> it = list.iterator();
      while (it.hasNext()) {
        SysMenu n = (SysMenu) it.next();
        if (n.getParentId().longValue() == t.getMenuId().longValue()) {
          tlist.add(n);
        }
      }
      return tlist;
    } catch (Exception e) {
      log.error("获取子节点列表失败，菜单 ID: {}, 错误：{}", t.getMenuId(), e.getMessage(), e);
      return Collections.emptyList();
    }
  }

  /** 判断是否有子节点 */
  private boolean hasChild(List<SysMenu> list, SysMenu t) {
    try {
      return getChildList(list, t).size() > 0;
    } catch (Exception e) {
      log.error("判断是否有子节点失败，菜单 ID: {}, 错误：{}", t.getMenuId(), e.getMessage(), e);
      return false;
    }
  }

  /**
   * 内链域名特殊字符替换
   *
   * @return 替换后的内链域名
   */
  public String innerLinkReplaceEach(String path) {
    try {
      return StringUtils.replaceEach(
          path,
          new String[] {"http://", "https://", "www.", ".", ":"},
          new String[] {"", "", "", "/", "/"});
    } catch (Exception e) {
      log.error("内链域名特殊字符替换失败，路径：{}, 错误：{}", path, e.getMessage(), e);
      return path;
    }
  }
}
