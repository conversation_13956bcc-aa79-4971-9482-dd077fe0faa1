<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xmlns="http://maven.apache.org/POM/4.0.0"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <parent>
    <artifactId>xinjian</artifactId>
    <groupId>com.xinjian</groupId>
    <version>1.0.0</version>
  </parent>
  <modelVersion>4.0.0</modelVersion>
  <!-- 打包类型，通过 profile 动态设置 -->
  <packaging>${packaging.type}</packaging>
  <artifactId>xinjian-admin</artifactId>

  <description>Web 服务入口，所有模块的汇集点</description>

  <dependencies>
    <!-- 内部模块依赖 -->
    <!-- 核心框架模块 -->
    <dependency>
      <groupId>com.xinjian</groupId>
      <artifactId>xinjian-framework</artifactId>
    </dependency>
    <dependency>
      <groupId>com.xinjian</groupId>
      <artifactId>xinjian-common</artifactId>
    </dependency>
    <!-- 定时任务模块 -->
    <dependency>
      <groupId>com.xinjian</groupId>
      <artifactId>xinjian-quartz</artifactId>
    </dependency>
    <!-- 代码生成模块 -->
    <dependency>
      <groupId>com.xinjian</groupId>
      <artifactId>xinjian-generator</artifactId>
    </dependency>
    <!-- Redis Starter 模块 -->
    <dependency>
      <groupId>com.xinjian</groupId>
      <artifactId>xinjian-starter-redis</artifactId>
      <version>${xinjian.version}</version>
    </dependency>
    <!-- MyBatis Starter 模块 -->
    <dependency>
      <groupId>com.xinjian</groupId>
      <artifactId>xinjian-starter-mybatis</artifactId>
      <version>${xinjian.version}</version>
    </dependency>
    <!-- Captcha 模块 -->
    <dependency>
      <groupId>com.xinjian</groupId>
      <artifactId>xinjian-module-captcha</artifactId>
      <version>${xinjian.version}</version>
    </dependency>
    <!-- Spring Boot DevTools: 提供开发阶段的热部署、自动重启等功能 -->
    <!--
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-devtools</artifactId>
      <optional>true</optional> 
    </dependency>
    -->

    <!-- Spring Boot Configuration Processor: 用于编译时生成配置元数据，方便在 application.yml 中自动提示 -->
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-configuration-processor</artifactId>
      <optional>true</optional>
    </dependency>

    <!-- OpenAPI 3 (Swagger) -->
    <!-- SpringDoc OpenAPI UI: 自动化生成 API 文档，并提供一个可视化的 UI 界面 -->
    <dependency>
      <groupId>org.springdoc</groupId>
      <artifactId>springdoc-openapi-ui</artifactId>
    </dependency>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-web</artifactId>
    </dependency>
    <!-- SpringDoc OpenAPI Security: 集成 Spring Security，在 API 文档中添加安全认证支持 -->
    <dependency>
      <groupId>org.springdoc</groupId>
      <artifactId>springdoc-openapi-security</artifactId>
    </dependency>
    <!-- SpringDoc OpenAPI Data REST: 为 Spring Data REST 生成 API 文档 -->
    <dependency>
      <groupId>org.springdoc</groupId>
      <artifactId>springdoc-openapi-data-rest</artifactId>
    </dependency>
    <!-- 动态数据源，支持多数据源切换 -->
    <dependency>
      <groupId>com.baomidou</groupId>
      <artifactId>dynamic-datasource-spring-boot-starter</artifactId>
    </dependency>
    <!-- 数据库驱动 -->
    <!-- MySQL 数据库驱动 -->
    <dependency>
      <groupId>com.mysql</groupId>
      <artifactId>mysql-connector-j</artifactId>
    </dependency>
    <!-- MariaDB 数据库驱动 -->
    <dependency>
      <groupId>org.mariadb.jdbc</groupId>
      <artifactId>mariadb-java-client</artifactId>
    </dependency>
    <!-- OceanBase 数据库驱动 -->
    <dependency>
      <groupId>com.oceanbase</groupId>
      <artifactId>oceanbase-client</artifactId>
    </dependency>

    <!-- Lombok，通过注解简化 Java 代码 -->
    <dependency>
      <groupId>org.projectlombok</groupId>
      <artifactId>lombok</artifactId>
      <scope>provided</scope>
    </dependency>
    <!-- MapStruct Bean 映射工具 -->
    <dependency>
      <groupId>org.mapstruct</groupId>
      <artifactId>mapstruct</artifactId>
    </dependency>
    <dependency>
      <groupId>org.mapstruct</groupId>
      <artifactId>mapstruct-processor</artifactId>
      <scope>provided</scope>
    </dependency>

    <!-- OSHI 系统信息 -->
    <dependency>
      <groupId>com.github.oshi</groupId>
      <artifactId>oshi-core</artifactId>
    </dependency>
  </dependencies>

  <!-- 环境配置 -->
  <profiles>
    <!-- 开发环境 -->
    <profile>
      <id>dev</id>
      <activation>
        <activeByDefault>true</activeByDefault>
      </activation>
      <properties>
        <environment>dev</environment>
        <packaging.type>jar</packaging.type>
      </properties>
    </profile>
    <!-- 测试环境 -->
    <profile>
      <id>staging</id>
      <properties>
        <environment>staging</environment>
        <packaging.type>jar</packaging.type>
      </properties>
    </profile>
    <!-- 生产环境 -->
    <profile>
      <id>production</id>
      <properties>
        <environment>production</environment>
        <packaging.type>jar</packaging.type>
      </properties>
    </profile>
    <!-- 信创环境 -->
    <profile>
      <id>xc</id>
      <properties>
        <environment>xc</environment>
        <packaging.type>war</packaging.type>
      </properties>
    </profile>
  </profiles>

  <build>
    <plugins>
      <!-- Spring Boot Maven 插件: 用于将应用打包成可执行的 JAR 或 WAR -->
      <plugin>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-maven-plugin</artifactId>
        <version>${spring-boot.version}</version>
        <executions>
          <execution>
            <goals>
              <goal>repackage</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
      <!-- Maven WAR 插件: 用于打包 WAR 文件 -->
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-war-plugin</artifactId>
        <version>${maven-war.version}</version>
        <configuration>
          <failOnMissingWebXml>false</failOnMissingWebXml>
          <warName>${project.artifactId}</warName>
        </configuration>
      </plugin>
      <!-- Build Helper Maven 插件: 用于执行一些辅助性的构建任务，如此处生成构建时间戳 -->
      <plugin>
        <groupId>org.codehaus.mojo</groupId>
        <artifactId>build-helper-maven-plugin</artifactId>
        <version>${build-helper.version}</version>
        <executions>
          <execution>
            <id>timestamp-property</id>
            <goals>
              <goal>timestamp-property</goal>
            </goals>
            <configuration>
              <name>build.time</name>
              <pattern>yyMMdd-HHmm</pattern>
              <timeZone>GMT+8</timeZone>
            </configuration>
          </execution>
        </executions>
      </plugin>
    </plugins>
    <!-- 构建最终文件名格式 -->
    <finalName>${project.artifactId}_${environment}_${build.time}</finalName>
  </build>

</project>
