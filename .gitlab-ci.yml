variables:
  GIT_DEPTH: '1'
  MEMORY_SIZE: '2g'

  STAGE_USER: 'gxxj'
  STAGE_SERVERS: '************'

  PROD_USER: 'gxxj'
  PROD_SERVERS: '************ ************'

  DEPLOY_PATH: '${CI_PROJECT_PATH}'
  UPLOAD_PATH: '${CI_PROJECT_PATH}/files'

  MODULE: 'xinjian-admin'
  JAR_NAME: ${CI_PROJECT_NAME}

.build_template: &build_template
  stage: build
  before_script:
    - echo "Mvn $(./mvnw -version)"
  artifacts:
    expire_in: 1 day
    paths:
      - ${MODULE}/target/*.jar
      - ${MODULE}/target/*.war

build xc package:
    <<: *build_template
    tags:
        - dev-backend-02
    only:
        - xc
    script:
        - ./mvnw clean package -P xc -DskipGitHooks -T 4

build staging package:
  <<: *build_template
  tags:
    - dev-backend-01
  only:
    - main
    - master
  script:
    - ./mvnw clean package -P staging -DskipTest -DskipGitHooks -T 4
    - cd ${MODULE}/target && find . -name '*.jar' -exec bash -c 'mv "$1" "${1//${MODULE}/${JAR_NAME}}"' _ {} \;

deploy to staging server:
  stage: deploy
  tags:
    - dev-backend-01
  only:
    - main
    - master
  script:
    - |
      for SERVER in $STAGE_SERVERS; do
        echo "---> Server: $SERVER"
        ssh "${STAGE_USER}@${SERVER}" "mkdir -p ${UPLOAD_PATH} ${DEPLOY_PATH} && cd ${DEPLOY_PATH} && pwd && exit"
        rsync -avzq --delete ${MODULE}/target/*.jar up.sh "${STAGE_USER}@${SERVER}:${DEPLOY_PATH}"
        ssh "${STAGE_USER}@${SERVER}" "bash -l -c 'cd ${DEPLOY_PATH} && chmod u+x ./up.sh && ./up.sh'"
        sleep 15
      done

build production package:
  <<: *build_template
  tags:
    - edu-backend-01
  only:
    - release
    - production
  script:
    - ./mvnw clean package -P production -DskipTest -DskipGitHooks -T 4
    - cd ${MODULE}/target && find . -name '*.jar' -exec bash -c 'mv "$1" "${1//${MODULE}/${JAR_NAME}}"' _ {} \;

deploy to production server:
  stage: deploy
  tags:
    - edu-backend-01
  only:
    - release
    - production
  script:
    - |
      for SERVER in $PROD_SERVERS; do
        echo "---> Server: $SERVER"
        ssh "${PROD_USER}@${SERVER}" "mkdir -p ${UPLOAD_PATH} ${DEPLOY_PATH} && cd ${DEPLOY_PATH} && pwd && exit"
        rsync -avzq --delete ${MODULE}/target/*.jar up.sh "${PROD_USER}@${SERVER}:${DEPLOY_PATH}"
        ssh "${PROD_USER}@${SERVER}" "bash -l -c 'cd ${DEPLOY_PATH} && chmod u+x ./up.sh && ./up.sh -m ${MEMORY_SIZE}'"
        sleep 15
      done

dependency-track:
  tags:
    - murphysec
  only:
    - schedules
  script:
    - ./mvnw org.cyclonedx:cyclonedx-maven-plugin:makeAggregateBom
    - |
      curl -X POST "$DT_API_URL/api/v1/bom" \
        -H "Content-Type: multipart/form-data" \
        -H "X-Api-Key: $DT_API_KEY" \
        -F "autoCreate=true" \
        -F "projectName=$CI_PROJECT_NAME" \
        -F "bom=@./target/bom.xml"
  artifacts:
    paths:
      - target/bom.xml
