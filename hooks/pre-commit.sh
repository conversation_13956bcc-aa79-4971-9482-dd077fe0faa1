#!/bin/bash

# Git Pre-commit Hook - 自动格式化代码
# 核心功能：在提交前执行 mvn spotless:apply 格式化代码

set -e  # 遇到错误立即退出

echo "🔧 Git Pre-commit Hook: 开始代码格式化检查..."

# 检查是否有暂存的文件
if git diff --cached --quiet; then
    echo "ℹ️  没有暂存的文件，跳过格式化"
    exit 0
fi

# 获取暂存的文件列表
STAGED_FILES=$(git diff --cached --name-only)
echo "📝 暂存的文件："
echo "$STAGED_FILES" | sed 's/^/  - /'

# 检查是否有 Java 文件需要格式化
JAVA_FILES=$(echo "$STAGED_FILES" | grep -E '\.(java|xml|yml|yaml|properties|json)$' || true)
if [ -z "$JAVA_FILES" ]; then
    echo "ℹ️  没有需要格式化的文件，跳过 spotless"
    exit 0
fi

echo "🎯 需要格式化的文件："
echo "$JAVA_FILES" | sed 's/^/  - /'

# 检查是否在 Maven 项目根目录
if [ ! -f "pom.xml" ]; then
    echo "❌ 错误：找不到 pom.xml，请确保在 Maven 项目根目录中运行"
    exit 1
fi

# 确定使用哪个 Maven 命令
MAVEN_CMD=""
if [ -f "./mvnw" ]; then
    MAVEN_CMD="./mvnw"
    echo "🔧 使用 Maven Wrapper: ./mvnw"
elif command -v mvn &> /dev/null; then
    MAVEN_CMD="mvn"
    echo "🔧 使用系统 Maven: mvn"
else
    echo "❌ 错误：找不到 Maven 命令"
    echo "💡 请确保以下任一条件满足："
    echo "   - 项目根目录存在 mvnw 文件"
    echo "   - 系统已安装 Maven 并在 PATH 中"
    exit 1
fi

# 执行 spotless:apply 格式化代码
echo "🚀 正在执行 $MAVEN_CMD spotless:apply..."
if ! $MAVEN_CMD spotless:apply -q; then
    echo "❌ 代码格式化失败，请检查代码格式问题"
    echo "💡 提示：可以手动运行 '$MAVEN_CMD spotless:apply' 查看详细错误"
    exit 1
fi

echo "✅ 代码格式化完成"

# 重新添加格式化后的文件到暂存区
echo "📤 重新添加格式化后的文件到暂存区..."
for file in $STAGED_FILES; do
    if [ -f "$file" ]; then
        git add "$file"
        echo "  ✓ 已添加：$file"
    fi
done

# 检查格式化后是否还有未格式化的代码
echo "🔍 验证代码格式..."
if ! $MAVEN_CMD spotless:check -q; then
    echo "⚠️ 警告：仍有代码格式问题，但继续提交"
    echo "💡 建议：提交后再次运行 '$MAVEN_CMD spotless:apply' 确保代码格式正确"
else
    echo "✅ 代码格式验证通过"
fi

echo "🎉 Git Pre-commit Hook: 执行完成，准备提交代码"
echo ""
