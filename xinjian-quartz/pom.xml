<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xmlns="http://maven.apache.org/POM/4.0.0"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <parent>
    <artifactId>xinjian</artifactId>
    <groupId>com.xinjian</groupId>
    <version>1.0.0</version>
  </parent>
  <modelVersion>4.0.0</modelVersion>

  <artifactId>xinjian-quartz</artifactId>

  <description>定时任务模块</description>

  <dependencies>

    <!-- 内部模块依赖, 提供通用工具类 -->
    <dependency>
      <groupId>com.xinjian</groupId>
      <artifactId>xinjian-common</artifactId>
    </dependency>

    <!-- Quartz, 一个功能丰富的开源作业调度库 -->
    <dependency>
      <groupId>org.quartz-scheduler</groupId>
      <artifactId>quartz</artifactId>
      <exclusions>
        <exclusion>
          <groupId>com.mchange</groupId>
          <artifactId>c3p0</artifactId>
        </exclusion>
      </exclusions>
    </dependency>

    <!-- Lombok，通过注解简化 Java 代码 -->
    <dependency>
      <groupId>org.projectlombok</groupId>
      <artifactId>lombok</artifactId>
      <scope>provided</scope>
    </dependency>

  </dependencies>

</project>
