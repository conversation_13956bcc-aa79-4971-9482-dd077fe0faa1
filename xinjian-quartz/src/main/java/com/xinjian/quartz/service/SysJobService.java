package com.xinjian.quartz.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xinjian.common.constant.ScheduleConstants;
import com.xinjian.common.exception.status400.BadRequestException;
import com.xinjian.common.exception.status404.ResourceNotFoundException;
import com.xinjian.common.properties.PaginationProperties;
import com.xinjian.quartz.domain.SysJob;
import com.xinjian.quartz.domain.query.SysJobQuery;
import com.xinjian.quartz.mapper.SysJobMapper;
import com.xinjian.quartz.util.CronUtils;
import com.xinjian.quartz.util.ScheduleUtils;
import java.util.List;
import javax.annotation.PostConstruct;
import lombok.RequiredArgsConstructor;
import org.quartz.JobDataMap;
import org.quartz.JobKey;
import org.quartz.Scheduler;
import org.quartz.SchedulerException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/** 定时任务调度信息服务 */
@Service
@RequiredArgsConstructor
public class SysJobService {

  private final Scheduler scheduler;
  private final SysJobMapper jobMapper;
  private final PaginationProperties paginationProperties;

  /** 项目启动时，初始化定时器 主要是防止手动修改数据库导致未同步到定时任务处理（注：不能手动修改数据库 ID 和任务组名，否则会导致脏数据） */
  @PostConstruct
  public void init() throws SchedulerException {
    scheduler.clear();
    List<SysJob> jobList = jobMapper.selectJobAll();
    for (SysJob job : jobList) {
      ScheduleUtils.createScheduleJob(scheduler, job);
    }
  }

  /**
   * 获取 quartz 调度器的计划任务列表
   *
   * @param job 调度信息
   * @return 调度任务列表
   */
  public List<SysJob> selectJobList(SysJob job) {
    return jobMapper.selectJobList(job);
  }

  /**
   * 分页获取 quartz 调度器的计划任务列表
   *
   * <p>使用统一分页方案处理分页和排序逻辑
   *
   * @param query 查询条件
   * @return 调度任务分页列表
   */
  public IPage<SysJob> selectJobListByPage(SysJobQuery query) {
    // 处理分页和排序逻辑
    IPage<SysJob> page = query.buildPage(paginationProperties);
    return jobMapper.selectJobListByPage(page, query);
  }

  /**
   * 通过调度任务 ID 查询调度信息
   *
   * @param jobId 调度任务 ID
   * @return 调度任务对象信息
   */
  public SysJob selectJobById(Long jobId) {
    return jobMapper.selectJobById(jobId);
  }

  /**
   * 暂停任务
   *
   * @param job 调度信息
   * @throws SchedulerException 调度异常
   * @throws BadRequestException 业务规则违反异常
   */
  @Transactional(rollbackFor = Exception.class)
  public void pauseJob(SysJob job) throws SchedulerException, BadRequestException {
    Long jobId = job.getJobId();
    String jobGroup = job.getJobGroup();
    job.setStatus(false); // false 表示暂停
    int rows = jobMapper.updateJob(job);
    if (rows > 0) {
      scheduler.pauseJob(ScheduleUtils.getJobKey(jobId, jobGroup));
    } else {
      throw new BadRequestException("暂停任务失败");
    }
  }

  /**
   * 恢复任务
   *
   * @param job 调度信息
   * @throws SchedulerException 调度异常
   * @throws BadRequestException 业务规则违反异常
   */
  @Transactional(rollbackFor = Exception.class)
  public void resumeJob(SysJob job) throws SchedulerException, BadRequestException {
    Long jobId = job.getJobId();
    String jobGroup = job.getJobGroup();
    job.setStatus(true); // true 表示正常
    int rows = jobMapper.updateJob(job);
    if (rows > 0) {
      scheduler.resumeJob(ScheduleUtils.getJobKey(jobId, jobGroup));
    } else {
      throw new BadRequestException("恢复任务失败");
    }
  }

  /**
   * 删除任务后，所对应的 trigger 也将被删除
   *
   * @param job 调度信息
   * @throws SchedulerException 调度异常
   * @throws BadRequestException 业务规则违反异常
   */
  @Transactional(rollbackFor = Exception.class)
  public void deleteJob(SysJob job) throws SchedulerException, BadRequestException {
    Long jobId = job.getJobId();
    String jobGroup = job.getJobGroup();
    int rows = jobMapper.deleteJobById(jobId);
    if (rows > 0) {
      scheduler.deleteJob(ScheduleUtils.getJobKey(jobId, jobGroup));
    } else {
      throw new BadRequestException("删除任务失败");
    }
  }

  /**
   * 批量删除调度信息
   *
   * @param jobIds 需要删除的任务 ID
   * @throws SchedulerException 调度异常
   * @throws BadRequestException 业务规则违反异常
   */
  @Transactional(rollbackFor = Exception.class)
  public void deleteJobByIds(Long[] jobIds) throws SchedulerException, BadRequestException {
    for (Long jobId : jobIds) {
      SysJob job = jobMapper.selectJobById(jobId);
      if (job != null) {
        deleteJob(job);
      }
    }
  }

  /**
   * 任务调度状态修改
   *
   * @param job 调度信息
   * @throws SchedulerException 调度异常
   * @throws BadRequestException 业务规则违反异常
   */
  @Transactional(rollbackFor = Exception.class)
  public void changeStatus(SysJob job) throws SchedulerException, BadRequestException {
    if (job.getStatus()) {
      resumeJob(job);
    } else {
      pauseJob(job);
    }
  }

  /**
   * 立即运行任务
   *
   * @param job 调度信息
   * @throws SchedulerException 调度异常
   * @throws ResourceNotFoundException 资源未找到异常
   */
  @Transactional(rollbackFor = Exception.class)
  public void run(SysJob job) throws SchedulerException, ResourceNotFoundException {
    Long jobId = job.getJobId();
    String jobGroup = job.getJobGroup();
    SysJob properties = selectJobById(job.getJobId());

    // 如果任务不存在，抛出 ResourceNotFoundException
    if (properties == null) {
      throw new ResourceNotFoundException("任务不存在或已过期");
    }

    // 参数
    JobDataMap dataMap = new JobDataMap();
    dataMap.put(ScheduleConstants.TASK_PROPERTIES, properties);
    JobKey jobKey = ScheduleUtils.getJobKey(jobId, jobGroup);
    if (scheduler.checkExists(jobKey)) {
      scheduler.triggerJob(jobKey, dataMap);
    } else {
      throw new ResourceNotFoundException("任务不存在或已过期");
    }
  }

  /**
   * 新增任务
   *
   * @param job 调度信息
   * @return 新增的任务对象
   * @throws SchedulerException 调度异常
   * @throws BadRequestException 业务规则违反异常
   */
  @Transactional(rollbackFor = Exception.class)
  public SysJob insertJob(SysJob job) throws SchedulerException, BadRequestException {
    job.setStatus(false); // 新增任务默认为暂停状态
    int rows = jobMapper.insertJob(job);
    if (rows > 0) {
      ScheduleUtils.createScheduleJob(scheduler, job);
      // 返回创建的任务对象
      return selectJobById(job.getJobId());
    } else {
      throw new BadRequestException("新增任务失败");
    }
  }

  /**
   * 更新任务的时间表达式
   *
   * @param job 调度信息
   * @return 更新后的任务对象
   * @throws SchedulerException 调度异常
   * @throws BadRequestException 业务规则违反异常
   */
  @Transactional(rollbackFor = Exception.class)
  public SysJob updateJob(SysJob job) throws SchedulerException, BadRequestException {
    SysJob properties = selectJobById(job.getJobId());
    int rows = jobMapper.updateJob(job);
    if (rows > 0) {
      updateSchedulerJob(job, properties.getJobGroup());
      // 返回更新后的任务对象
      return selectJobById(job.getJobId());
    } else {
      throw new BadRequestException("修改任务失败");
    }
  }

  /**
   * 更新任务
   *
   * @param job 任务对象
   * @param jobGroup 任务组名
   * @throws SchedulerException 调度异常
   */
  public void updateSchedulerJob(SysJob job, String jobGroup) throws SchedulerException {
    Long jobId = job.getJobId();
    // 判断是否存在
    JobKey jobKey = ScheduleUtils.getJobKey(jobId, jobGroup);
    if (scheduler.checkExists(jobKey)) {
      // 防止创建时存在数据问题 先移除，然后在执行创建操作
      scheduler.deleteJob(jobKey);
    }
    ScheduleUtils.createScheduleJob(scheduler, job);
  }

  /**
   * 校验 cron 表达式是否有效
   *
   * @param cronExpression 表达式
   * @return 结果
   */
  public boolean checkCronExpressionIsValid(String cronExpression) {
    return CronUtils.isValid(cronExpression);
  }
}
