package com.xinjian.quartz.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xinjian.common.exception.status400.BadRequestException;
import com.xinjian.quartz.domain.SysJobLog;
import com.xinjian.quartz.domain.query.SysJobLogQuery;
import com.xinjian.quartz.mapper.SysJobLogMapper;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class SysJobLogService {
  @Autowired private SysJobLogMapper jobLogMapper;

  /**
   * 获取 quartz 调度器日志的计划任务
   *
   * @param jobLog 调度日志信息
   * @return 调度任务日志集合
   */
  public List<SysJobLog> selectJobLogList(SysJobLog jobLog) {
    return jobLogMapper.selectJobLogList(jobLog);
  }

  /**
   * 分页获取 quartz 调度器日志的计划任务
   *
   * @param query 查询条件
   * @return 调度任务日志分页集合
   */
  public IPage<SysJobLog> selectJobLogListByPage(SysJobLogQuery query) {
    return jobLogMapper.selectJobLogListByPage(query);
  }

  /**
   * 通过调度任务日志 ID 查询调度信息
   *
   * @param jobLogId 调度任务日志 ID
   * @return 调度任务日志对象信息
   */
  public SysJobLog selectJobLogById(Long jobLogId) {
    return jobLogMapper.selectJobLogById(jobLogId);
  }

  /**
   * 新增任务日志
   *
   * @param jobLog 调度日志信息
   */
  public void addJobLog(SysJobLog jobLog) {
    jobLogMapper.insertJobLog(jobLog);
  }

  /**
   * 批量删除调度日志信息
   *
   * @param logIds 需要删除的数据 ID
   */
  public void deleteJobLogByIds(Long[] logIds) {
    int rows = jobLogMapper.deleteJobLogByIds(logIds);
    if (rows == 0) {
      throw new BadRequestException("删除日志失败");
    }
  }

  /**
   * 删除任务日志
   *
   * @param jobId 调度日志 ID
   * @return 结果
   */
  public int deleteJobLogById(Long jobId) {
    return jobLogMapper.deleteJobLogById(jobId);
  }

  /** 清空任务日志 */
  public void cleanJobLog() {
    jobLogMapper.cleanJobLog();
  }
}
