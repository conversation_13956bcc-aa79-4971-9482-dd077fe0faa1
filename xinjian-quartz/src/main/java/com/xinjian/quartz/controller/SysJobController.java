package com.xinjian.quartz.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xinjian.common.annotation.Log;
import com.xinjian.common.constant.Constants;
import com.xinjian.common.core.domain.dto.LoginUser;
import com.xinjian.common.core.page.TableDataInfo;
import com.xinjian.common.enums.BusinessType;
import com.xinjian.common.exception.status422.BusinessRuleViolationException;
import com.xinjian.common.exception.status422.ValidationFailedException;
import com.xinjian.common.utils.poi.ExcelUtil;
import com.xinjian.quartz.domain.SysJob;
import com.xinjian.quartz.domain.query.SysJobQuery;
import com.xinjian.quartz.service.SysJobService;
import com.xinjian.quartz.util.CronUtils;
import com.xinjian.quartz.util.ScheduleUtils;
import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.apache.commons.lang3.StringUtils;
import org.quartz.SchedulerException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;

/** 调度任务信息操作处理 */
@RestController
@RequestMapping("/monitor/job")
public class SysJobController {
  @Autowired private SysJobService jobService;

  /** 查询定时任务列表 */
  @PreAuthorize("hasAnyAuthority('monitor:job:list')")
  @GetMapping("/list")
  public TableDataInfo<SysJob> list(SysJobQuery query) {
    IPage<SysJob> page = jobService.selectJobListByPage(query);
    return new TableDataInfo<>(page);
  }

  /** 导出定时任务列表 */
  @PreAuthorize("hasAnyAuthority('monitor:job:export')")
  @Log(title = "定时任务", businessType = BusinessType.EXPORT)
  @PostMapping("/export")
  public void export(HttpServletResponse response, SysJob sysJob) {
    List<SysJob> list = jobService.selectJobList(sysJob);
    ExcelUtil<SysJob> util = new ExcelUtil<SysJob>(SysJob.class);
    util.exportExcel(response, list, "定时任务");
  }

  /** 获取定时任务详细信息 */
  @PreAuthorize("hasAnyAuthority('monitor:job:query')")
  @GetMapping(value = "/{jobId}")
  public SysJob getInfo(@PathVariable("jobId") Long jobId) {
    return jobService.selectJobById(jobId);
  }

  /** 新增定时任务 */
  @PreAuthorize("hasAnyAuthority('monitor:job:add')")
  @Log(title = "定时任务", businessType = BusinessType.INSERT)
  @PostMapping
  @ResponseStatus(HttpStatus.CREATED)
  public SysJob add(@RequestBody SysJob job, @AuthenticationPrincipal LoginUser loginUser)
      throws SchedulerException {
    // 验证 Cron 表达式
    if (!CronUtils.isValid(job.getCronExpression())) {
      throw new ValidationFailedException("新增任务'" + job.getJobName() + "'失败，Cron 表达式不正确");
    }

    // 验证目标字符串
    if (StringUtils.containsIgnoreCase(job.getInvokeTarget(), Constants.LOOKUP_RMI)) {
      throw new BusinessRuleViolationException("新增任务'" + job.getJobName() + "'失败，目标字符串不允许'rmi'调用");
    }

    if (StringUtils.containsAnyIgnoreCase(
        job.getInvokeTarget(), new String[] {Constants.LOOKUP_LDAP, Constants.LOOKUP_LDAPS})) {
      throw new BusinessRuleViolationException(
          "新增任务'" + job.getJobName() + "'失败，目标字符串不允许'ldap(s)'调用");
    }

    if (StringUtils.containsAnyIgnoreCase(
        job.getInvokeTarget(), new String[] {"http://", "https://"})) {
      throw new BusinessRuleViolationException(
          "新增任务'" + job.getJobName() + "'失败，目标字符串不允许'http(s)'调用");
    }

    if (StringUtils.containsAnyIgnoreCase(job.getInvokeTarget(), Constants.JOB_ERROR_STR)) {
      throw new BusinessRuleViolationException("新增任务'" + job.getJobName() + "'失败，目标字符串存在违规");
    }

    if (!ScheduleUtils.whiteList(job.getInvokeTarget())) {
      throw new BusinessRuleViolationException("新增任务'" + job.getJobName() + "'失败，目标字符串不在白名单内");
    }

    jobService.insertJob(job);
    // 返回创建的任务对象
    return job;
  }

  /** 修改定时任务 */
  @PreAuthorize("hasAnyAuthority('monitor:job:edit')")
  @Log(title = "定时任务", businessType = BusinessType.UPDATE)
  @PutMapping
  public SysJob edit(@RequestBody SysJob job, @AuthenticationPrincipal LoginUser loginUser)
      throws SchedulerException {
    // 验证 Cron 表达式
    if (!CronUtils.isValid(job.getCronExpression())) {
      throw new ValidationFailedException("修改任务'" + job.getJobName() + "'失败，Cron 表达式不正确");
    }

    // 验证目标字符串
    if (StringUtils.containsIgnoreCase(job.getInvokeTarget(), Constants.LOOKUP_RMI)) {
      throw new BusinessRuleViolationException("修改任务'" + job.getJobName() + "'失败，目标字符串不允许'rmi'调用");
    }

    if (StringUtils.containsAnyIgnoreCase(
        job.getInvokeTarget(), new String[] {Constants.LOOKUP_LDAP, Constants.LOOKUP_LDAPS})) {
      throw new BusinessRuleViolationException(
          "修改任务'" + job.getJobName() + "'失败，目标字符串不允许'ldap(s)'调用");
    }

    if (StringUtils.containsAnyIgnoreCase(
        job.getInvokeTarget(), new String[] {"http://", "https://"})) {
      throw new BusinessRuleViolationException(
          "修改任务'" + job.getJobName() + "'失败，目标字符串不允许'http(s)'调用");
    }

    if (StringUtils.containsAnyIgnoreCase(job.getInvokeTarget(), Constants.JOB_ERROR_STR)) {
      throw new BusinessRuleViolationException("修改任务'" + job.getJobName() + "'失败，目标字符串存在违规");
    }

    if (!ScheduleUtils.whiteList(job.getInvokeTarget())) {
      throw new BusinessRuleViolationException("修改任务'" + job.getJobName() + "'失败，目标字符串不在白名单内");
    }

    jobService.updateJob(job);
    // 返回更新后的任务对象
    return job;
  }

  /** 定时任务状态修改 */
  @PreAuthorize("hasAnyAuthority('monitor:job:changeStatus')")
  @Log(title = "定时任务", businessType = BusinessType.UPDATE)
  @PutMapping("/changeStatus")
  public void changeStatus(@RequestBody SysJob job) throws SchedulerException {
    SysJob existingJob = jobService.selectJobById(job.getJobId());
    existingJob.setStatus(job.getStatus());
    jobService.changeStatus(existingJob);
  }

  /** 定时任务立即执行一次 */
  @PreAuthorize("hasAnyAuthority('monitor:job:changeStatus')")
  @Log(title = "定时任务", businessType = BusinessType.UPDATE)
  @PutMapping("/run")
  public void run(@RequestBody SysJob job) throws SchedulerException {
    jobService.run(job);
  }

  /** 删除定时任务 */
  @PreAuthorize("hasAnyAuthority('monitor:job:remove')")
  @Log(title = "定时任务", businessType = BusinessType.DELETE)
  @DeleteMapping("/{jobIds}")
  @ResponseStatus(HttpStatus.NO_CONTENT)
  public void remove(@PathVariable Long[] jobIds) throws SchedulerException {
    jobService.deleteJobByIds(jobIds);
  }
}
