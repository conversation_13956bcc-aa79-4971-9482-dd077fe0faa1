package com.xinjian.quartz.util;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.exceptions.ExceptionUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.xinjian.common.constant.ScheduleConstants;
import com.xinjian.quartz.domain.SysJob;
import com.xinjian.quartz.domain.SysJobLog;
import com.xinjian.quartz.service.SysJobLogService;
import java.time.LocalDateTime;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.quartz.Job;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;

/** 抽象 quartz 调用 */
@Slf4j
public abstract class AbstractQuartzJob implements Job {

  /** 线程本地变量 */
  private static ThreadLocal<LocalDateTime> threadLocal = new ThreadLocal<>();

  @Override
  public void execute(JobExecutionContext context) throws JobExecutionException {
    SysJob sysJob = new SysJob();
    BeanUtil.copyProperties(
        context.getMergedJobDataMap().get(ScheduleConstants.TASK_PROPERTIES), sysJob);
    try {
      before(context, sysJob);
      doExecute(context, sysJob);
      after(context, sysJob, null);
    } catch (Exception e) {
      log.error("任务执行异常  - ：", e);
      after(context, sysJob, e);
    }
  }

  /**
   * 执行前
   *
   * @param context 工作执行上下文对象
   * @param sysJob 系统计划任务
   */
  protected void before(JobExecutionContext context, SysJob sysJob) {
    threadLocal.set(LocalDateTime.now());
  }

  /**
   * 执行后
   *
   * @param context 工作执行上下文对象
   * @param sysJob 系统计划任务
   * @param e
   */
  protected void after(JobExecutionContext context, SysJob sysJob, Exception e) {
    LocalDateTime startTime = threadLocal.get();
    threadLocal.remove();

    final SysJobLog sysJobLog = new SysJobLog();
    sysJobLog.setJobName(sysJob.getJobName());
    sysJobLog.setJobGroup(sysJob.getJobGroup());
    sysJobLog.setInvokeTarget(sysJob.getInvokeTarget());
    sysJobLog.setStartTime(startTime);
    sysJobLog.setStopTime(LocalDateTime.now());
    long runMs = java.time.Duration.between(startTime, sysJobLog.getStopTime()).toMillis();
    sysJobLog.setJobMessage(sysJobLog.getJobName() + " 总共耗时：" + runMs + "毫秒");
    if (e != null) {
      sysJobLog.setStatus(false);
      String errorMsg = StringUtils.substring(ExceptionUtil.getMessage(e), 0, 2000);
      sysJobLog.setExceptionInfo(errorMsg);
    } else {
      sysJobLog.setStatus(true);
    }

    // 写入数据库当中
    SpringUtil.getBean(SysJobLogService.class).addJobLog(sysJobLog);
  }

  /**
   * 执行方法，由子类重载
   *
   * @param context 工作执行上下文对象
   * @param sysJob 系统计划任务
   * @throws Exception 执行过程中的异常
   */
  protected abstract void doExecute(JobExecutionContext context, SysJob sysJob) throws Exception;
}
