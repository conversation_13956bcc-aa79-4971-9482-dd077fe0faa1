package com.xinjian.quartz.util;

import java.text.ParseException;
import java.util.Date;
import org.quartz.CronExpression;

/** cron 表达式工具类 */
public class CronUtils {
  /**
   * 返回一个布尔值代表一个给定的 Cron 表达式的有效性
   *
   * @param cronExpression Cron 表达式
   * @return boolean 表达式是否有效
   */
  public static boolean isValid(String cronExpression) {
    return CronExpression.isValidExpression(cronExpression);
  }

  /**
   * 返回一个字符串值，表示该消息无效 Cron 表达式给出有效性
   *
   * @param cronExpression Cron 表达式
   * @return String 无效时返回表达式错误描述，如果有效返回 null
   */
  public static String getInvalidMessage(String cronExpression) {
    try {
      new CronExpression(cronExpression);
      return null;
    } catch (ParseException pe) {
      return pe.getMessage();
    }
  }

  /**
   * 返回下一个执行时间根据给定的 Cron 表达式
   *
   * @param cronExpression Cron 表达式
   * @return Date 下次 Cron 表达式执行时间
   */
  public static Date getNextExecution(String cronExpression) {
    try {
      CronExpression cron = new CronExpression(cronExpression);
      return cron.getNextValidTimeAfter(new Date(System.currentTimeMillis()));
    } catch (ParseException e) {
      throw new IllegalArgumentException(e.getMessage());
    }
  }
}
