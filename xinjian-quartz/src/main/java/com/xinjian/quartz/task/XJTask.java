package com.xinjian.quartz.task;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/** 定时任务调度测试 */
@Component("xjTask")
@Slf4j
public class XJTask {
  public void xjMultipleParams(String s, <PERSON><PERSON><PERSON> b, <PERSON> l, Double d, Integer i) {
    log.info(String.format("执行多参方法：字符串类型%s，布尔类型%s，长整型%s，浮点型%s，整形%s", s, b, l, d, i));
  }

  public void xjParams(String params) {
    log.info("执行有参方法：{}", params);
  }

  public void xjNoParams() {
    log.info("执行无参方法");
  }
}
