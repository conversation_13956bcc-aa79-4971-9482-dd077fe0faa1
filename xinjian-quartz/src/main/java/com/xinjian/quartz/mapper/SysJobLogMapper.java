package com.xinjian.quartz.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xinjian.quartz.domain.SysJobLog;
import com.xinjian.quartz.domain.query.SysJobLogQuery;
import java.util.List;

/** 调度任务日志信息 数据层 */
public interface SysJobLogMapper {
  /**
   * 获取 quartz 调度器日志的计划任务
   *
   * @param jobLog 调度日志信息
   * @return 调度任务日志集合
   */
  public List<SysJobLog> selectJobLogList(SysJobLog jobLog);

  /**
   * 分页获取 quartz 调度器日志的计划任务
   *
   * @param query 查询条件
   * @return 调度任务日志分页集合
   */
  public IPage<SysJobLog> selectJobLogListByPage(SysJobLogQuery query);

  /**
   * 查询所有调度任务日志
   *
   * @return 调度任务日志列表
   */
  public List<SysJobLog> selectJobLogAll();

  /**
   * 通过调度任务日志 ID 查询调度信息
   *
   * @param jobLogId 调度任务日志 ID
   * @return 调度任务日志对象信息
   */
  public SysJobLog selectJobLogById(Long jobLogId);

  /**
   * 新增任务日志
   *
   * @param jobLog 调度日志信息
   * @return 结果
   */
  public int insertJobLog(SysJobLog jobLog);

  /**
   * 批量删除调度日志信息
   *
   * @param logIds 需要删除的数据 ID
   * @return 结果
   */
  public int deleteJobLogByIds(Long[] logIds);

  /**
   * 删除任务日志
   *
   * @param jobId 调度日志 ID
   * @return 结果
   */
  public int deleteJobLogById(Long jobId);

  /** 清空任务日志 */
  public void cleanJobLog();
}
