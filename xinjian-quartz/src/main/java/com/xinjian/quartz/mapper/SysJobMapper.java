package com.xinjian.quartz.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xinjian.quartz.domain.SysJob;
import com.xinjian.quartz.domain.query.SysJobQuery;
import java.util.List;
import org.apache.ibatis.annotations.Param;

/** 调度任务信息 数据层 */
public interface SysJobMapper {
  /**
   * 查询调度任务日志集合
   *
   * @param job 调度信息
   * @return 操作日志集合
   */
  public List<SysJob> selectJobList(SysJob job);

  /**
   * 分页查询调度任务日志集合
   *
   * @param page 分页对象
   * @param query 查询条件
   * @return 操作日志分页集合
   */
  public IPage<SysJob> selectJobListByPage(
      @Param("page") IPage<SysJob> page, @Param("query") SysJobQuery query);

  /**
   * 查询所有调度任务
   *
   * @return 调度任务列表
   */
  public List<SysJob> selectJobAll();

  /**
   * 通过调度 ID 查询调度任务信息
   *
   * @param jobId 调度 ID
   * @return 角色对象信息
   */
  public SysJob selectJobById(Long jobId);

  /**
   * 通过调度 ID 删除调度任务信息
   *
   * @param jobId 调度 ID
   * @return 结果
   */
  public int deleteJobById(Long jobId);

  /**
   * 批量删除调度任务信息
   *
   * @param ids 需要删除的数据 ID
   * @return 结果
   */
  public int deleteJobByIds(Long[] ids);

  /**
   * 修改调度任务信息
   *
   * @param job 调度任务信息
   * @return 结果
   */
  public int updateJob(SysJob job);

  /**
   * 新增调度任务信息
   *
   * @param job 调度任务信息
   * @return 结果
   */
  public int insertJob(SysJob job);
}
