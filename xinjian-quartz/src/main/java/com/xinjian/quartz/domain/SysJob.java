package com.xinjian.quartz.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.xinjian.common.annotation.Excel;
import com.xinjian.common.annotation.Excel.ColumnType;
import com.xinjian.common.constant.ScheduleConstants;
import com.xinjian.common.core.domain.BaseEntity;
import com.xinjian.quartz.util.CronUtils;
import java.util.Date;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.StringUtils;

@Data
@EqualsAndHashCode(callSuper = true)
/** 定时任务调度表 sys_job */
public class SysJob extends BaseEntity {
  private static final long serialVersionUID = 1L;

  /** 任务 ID */
  @Excel(name = "任务序号", cellType = ColumnType.NUMERIC)
  @TableId(type = IdType.AUTO)
  private Long jobId;

  /** 任务名称 */
  @NotBlank(message = "任务名称不能为空")
  @Size(min = 0, max = 64, message = "任务名称不能超过 64 个字符")
  private String jobName;

  /** 任务组名 */
  @Excel(name = "任务组名")
  private String jobGroup;

  /** 调用目标字符串 */
  @NotBlank(message = "调用目标字符串不能为空")
  @Size(min = 0, max = 500, message = "调用目标字符串长度不能超过 500 个字符")
  private String invokeTarget;

  /** cron 执行表达式 */
  @NotBlank(message = "Cron 执行表达式不能为空")
  @Size(min = 0, max = 255, message = "Cron 执行表达式不能超过 255 个字符")
  private String cronExpression;

  /** cron 计划策略 */
  @Excel(name = "计划策略 ", readConverterExp = "0=默认，1=立即触发执行，2=触发一次执行，3=不触发立即执行")
  private String misfirePolicy = ScheduleConstants.MISFIRE_DEFAULT;

  /** 是否并发执行（0 允许 1 禁止） */
  @Excel(name = "并发执行", readConverterExp = "0=允许，1=禁止")
  private String concurrent;

  /** 任务状态（true 正常 false 暂停） */
  @Excel(name = "任务状态", readConverterExp = "true=正常，false=暂停")
  private Boolean status;

  @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
  public Date getNextValidTime() {
    if (StringUtils.isNotEmpty(cronExpression)) {
      return CronUtils.getNextExecution(cronExpression);
    }
    return null;
  }
}
