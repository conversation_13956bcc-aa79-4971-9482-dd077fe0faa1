package com.xinjian.quartz.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.xinjian.common.annotation.Excel;
import com.xinjian.common.core.domain.BaseEntity;
import java.time.LocalDateTime;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
/** 定时任务调度日志表 sys_job_log */
public class SysJobLog extends BaseEntity {
  private static final long serialVersionUID = 1L;

  /** ID */
  @Excel(name = "日志序号")
  @TableId(type = IdType.AUTO)
  private Long jobLogId;

  /** 任务名称 */
  @Excel(name = "任务名称")
  private String jobName;

  /** 任务组名 */
  @Excel(name = "任务组名")
  private String jobGroup;

  /** 调用目标字符串 */
  @Excel(name = "调用目标字符串")
  private String invokeTarget;

  /** 日志信息 */
  @Excel(name = "日志信息")
  private String jobMessage;

  /** 执行状态（true 正常 false 失败） */
  @Excel(name = "执行状态", readConverterExp = "true=正常，false=失败")
  private Boolean status;

  /** 异常信息 */
  @Excel(name = "异常信息")
  private String exceptionInfo;

  /** 开始时间 */
  @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
  private LocalDateTime startTime;

  /** 停止时间 */
  @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
  private LocalDateTime stopTime;
}
