package com.xinjian.quartz.domain.query;

import com.xinjian.common.core.domain.BaseQuery;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class SysJobQuery extends BaseQuery<SysJobQuery> {

  // 业务查询字段
  private String jobName;
  private String jobGroup;
  private Boolean status;
  private String invokeTarget;

  @Override
  public SysJobQuery getQueryParams() {
    return this;
  }
}
