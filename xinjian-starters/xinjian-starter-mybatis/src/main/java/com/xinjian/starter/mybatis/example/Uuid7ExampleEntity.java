package com.xinjian.starter.mybatis.example;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * UUIDv7 实体类示例
 *
 * <p>展示如何在实体类中使用 UUIDv7 作为主键
 */
@Data
public class Uuid7ExampleEntity {

  /**
   * 主键 ID - 使用 UUIDv7
   *
   * <p>数据库字段类型 BINARY(16) 实体字段类型为 String 且 IdType 为 ASSIGN_UUID 时 MyBatis Plus 会自动使用全局配置的
   * IdentifierGenerator 的 nextUUID() 方法
   */
  @TableId(type = IdType.ASSIGN_UUID)
  private String id;

  /** 创建时间 - 自动填充 */
  private LocalDateTime createTime;

  /** 创建人 - 自动填充 */
  private Long createBy;

  /** 更新时间 - 自动填充 */
  private LocalDateTime updateTime;

  /** 更新人 - 自动填充 */
  private Long updateBy;

  /** 示例字段 */
  private String name;

  /** 示例描述 */
  private String description;
}
