package com.xinjian.starter.mybatis.generator;

import com.baomidou.mybatisplus.core.incrementer.IdentifierGenerator;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.github.f4b6a3.uuid.UuidCreator;
import org.springframework.stereotype.Component;

/**
 * 统一 ID 生成器 (混合模式)
 *
 * <p>整合了两种业界最佳的分布式 ID 策略：
 *
 * <ul>
 *   <li><b>雪花算法 (Snowflake)</b>: 用于生成 Long 类型的 ID (对应 <code>@TableId(type = IdType.ASSIGN_ID)
 *       </code>)。
 *   <li><b>UUIDv7</b>: 用于生成 String 类型的 ID (对应 <code>@TableId(type = IdType.ASSIGN_UUID)</code>)。
 * </ul>
 *
 * MyBatis Plus 会根据实体类主键的类型，自动调用此类中对应的方法。
 */
@Component("unifiedIdGenerator")
public class UnifiedIdGenerator implements IdentifierGenerator {

  /**
   * 为实体中 String 类型的 ID (ASSIGN_UUID) 生成 UUIDv7
   *
   * <p>对应数据库字段类型 BIRARY(16)
   *
   * <p>@TableId(type = IdType.ASSIGN_UUID)
   *
   * @param entity 实体对象
   * @return 时间有序的 UUIDv7 字符串
   */
  @Override
  public String nextUUID(Object entity) {
    // 使用 uuid-creator 库生成时间有序的 UUID (v7)
    return UuidCreator.getTimeOrderedEpoch().toString();
  }

  /**
   * 为实体中 Long/Integer 等数字类型的 ID (ASSIGN_ID) 生成雪花 ID
   *
   * <p>对应数据库字段类型 BIGINT(20)
   *
   * <p>@TableId(type = IdType.ASSIGN_ID)
   *
   * @param entity 实体对象
   * @return 雪花算法生成的 Long 类型 ID
   */
  @Override
  public Number nextId(Object entity) {
    // 直接使用 MyBatis Plus 自带的、高效的 IdWorker (雪花算法实现)
    // FIXME: 会循环调用 nextId 方法，导致死循环
    return IdWorker.getId();
  }
}
