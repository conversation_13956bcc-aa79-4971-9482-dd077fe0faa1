package com.xinjian.starter.mybatis.handler;

import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import com.xinjian.starter.mybatis.auditor.UserAuditor;
import java.time.LocalDateTime;
import org.apache.ibatis.reflection.MetaObject;

/**
 * MyBatis-Plus 自动填充处理器
 *
 * <p>负责在插入和更新时自动填充 createBy、updateBy、createTime、updateTime 等字段
 */
public class MyMetaObjectHandler implements MetaObjectHandler {

  private final UserAuditor userAuditor;

  /** 通过构造函数注入 UserAuditor 接口 */
  public MyMetaObjectHandler(UserAuditor userAuditor) {
    this.userAuditor = userAuditor;
  }

  /**
   * 插入时自动填充
   *
   * @param metaObject MyBatis 的 MetaObject 对象
   */
  @Override
  public void insertFill(MetaObject metaObject) {
    // 审计字段（创建时间、创建人、更新时间、更新人）
    this.strictInsertFill(metaObject, "createTime", LocalDateTime.class, LocalDateTime.now());
    this.strictInsertFill(metaObject, "updateTime", LocalDateTime.class, LocalDateTime.now());
    this.strictInsertFill(metaObject, "createBy", Long.class, userAuditor.getCurrentUserId());
    this.strictInsertFill(metaObject, "updateBy", Long.class, userAuditor.getCurrentUserId());

    // 逻辑删除字段，默认为 "未删除" (false)
    this.strictInsertFill(metaObject, "isDeleted", Boolean.class, false);
  }

  /**
   * 更新时自动填充
   *
   * @param metaObject MyBatis 的 MetaObject 对象
   */
  @Override
  public void updateFill(MetaObject metaObject) {
    // 审计字段（更新时间、更新人）
    this.strictUpdateFill(metaObject, "updateTime", LocalDateTime.class, LocalDateTime.now());
    this.strictUpdateFill(metaObject, "updateBy", Long.class, userAuditor.getCurrentUserId());
  }
}
