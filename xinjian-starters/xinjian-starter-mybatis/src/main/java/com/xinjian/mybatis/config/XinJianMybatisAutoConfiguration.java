package com.xinjian.mybatis.config;

import com.baomidou.mybatisplus.annotation.DbType;
import com.baomidou.mybatisplus.autoconfigure.MybatisPlusPropertiesCustomizer;
import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import com.baomidou.mybatisplus.core.incrementer.IdentifierGenerator;
import com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor;
import com.baomidou.mybatisplus.extension.plugins.handler.DataPermissionHandler;
import com.baomidou.mybatisplus.extension.plugins.inner.DataPermissionInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.PaginationInnerInterceptor;
import com.xinjian.starter.mybatis.auditor.UserAuditor;
import com.xinjian.starter.mybatis.handler.MyMetaObjectHandler;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/** MyBatis Plus 自动配置类 */
@Configuration
public class XinJianMybatisAutoConfiguration {

  /** 配置 MybatisPlusInterceptor 拦截器 */
  @Bean
  @ConditionalOnMissingBean
  public MybatisPlusInterceptor mybatisPlusInterceptor(
      ObjectProvider<DataPermissionHandler> dataPermissionHandlerProvider) {
    MybatisPlusInterceptor interceptor = new MybatisPlusInterceptor();
    // 添加分页插件
    interceptor.addInnerInterceptor(new PaginationInnerInterceptor(DbType.MYSQL));

    // 使用 ObjectProvider 安全地获取 DataPermissionHandler
    // 如果业务应用中定义了 DataPermissionHandler Bean，就获取并使用它
    dataPermissionHandlerProvider.ifAvailable(
        handler -> {
          interceptor.addInnerInterceptor(new DataPermissionInterceptor(handler));
        });

    return interceptor;
  }

  /** 配置 MetaObjectHandler Bean Spring 会自动寻找一个 UserAuditor 类型的 Bean 注入进来 */
  @Bean
  @ConditionalOnMissingBean
  public MetaObjectHandler metaObjectHandler(UserAuditor userAuditor) {
    return new MyMetaObjectHandler(userAuditor);
  }

  /**
   * 提供一个默认的 UserAuditor 实现 @ConditionalOnMissingBean 确保了只有在业务应用没有提供自己的 UserAuditor Bean 时，这个默认的才会生效
   */
  @Bean
  @ConditionalOnMissingBean
  public UserAuditor defaultUserAuditor() {
    // 默认实现：返回一个代表"系统"或"未知"的 ID，例如 0L 或 null
    // 这确保了即使在没有安全框架的单元测试或批处理应用中，项目也能正常启动
    return () -> 0L;
  }

  /**
   * 通过 MybatisPlusPropertiesCustomizer 来自定义 MyBatis Plus 的行为
   *
   * @param uuid7Generator 我们自己创建的 UUIDv7 生成器 Bean
   * @return MybatisPlusPropertiesCustomizer 配置器
   */
  @Bean
  @ConditionalOnMissingBean
  public MybatisPlusPropertiesCustomizer plusPropertiesCustomizer(
      IdentifierGenerator uuid7Generator) {
    return plusProperties -> {
      // 设置全局的 IdentifierGenerator 为我们自定义的 Uuid7IdentifierGenerator
      plusProperties.getGlobalConfig().setIdentifierGenerator(uuid7Generator);
    };
  }
}
