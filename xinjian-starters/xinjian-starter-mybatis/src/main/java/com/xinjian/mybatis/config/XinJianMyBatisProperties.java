package com.xinjian.mybatis.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

/** XinJian MyBatis 配置属性 */
@Data
@ConfigurationProperties(prefix = "xinjian.mybatis")
public class XinJianMyBatisProperties {

  /** 是否启用动态数据源 */
  private boolean enabled = true;

  /** 默认数据源类型 */
  private String defaultDataSource = "MASTER";

  /** 严格模式：未配置的数据源是否抛出异常 */
  private boolean strict = false;

  /** 是否启用数据源健康检查 */
  private boolean healthCheckEnabled = true;

  /** 数据源切换日志级别 */
  private String logLevel = "DEBUG";
}
