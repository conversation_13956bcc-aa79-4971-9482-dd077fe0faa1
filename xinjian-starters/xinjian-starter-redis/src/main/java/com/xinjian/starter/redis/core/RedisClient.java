package com.xinjian.starter.redis.core;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import org.springframework.data.redis.core.RedisTemplate;

/**
 * 类型安全、前缀感知的编程式 Redis 客户端
 *
 * <p>提供类型安全的 Redis 操作，支持自动前缀管理、类型转换和丰富的操作方法。所有的键都会自动添加配置的前缀，避免多项目/多环境下的键冲突。
 *
 * <p>使用示例：
 *
 * <pre>
 * // 1. 基本字符串操作
 * redisClient.set("user:1", userDto);
 * Optional<UserDto> user = redisClient.get("user:1", UserDto.class);
 *
 * // 2. 带过期时间的操作
 * redisClient.set("session:abc123", sessionData, 30, TimeUnit.MINUTES);
 *
 * // 3. 哈希操作
 * redisClient.hSet("product:1", "price", 99.99);
 * Optional<Double> price = redisClient.hGet("product:1", "price", Double.class);
 *
 * // 4. 列表操作
 * redisClient.lPush("queue:tasks", task1, task2, task3);
 * Optional<Task> task = redisClient.rPop("queue:tasks", Task.class);
 *
 * // 5. 集合操作
 * redisClient.sAdd("tags:article:1", "java", "spring", "redis");
 * Set<Object> tags = redisClient.sMembers("tags:article:1");
 * </pre>
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public class RedisClient {

  private final RedisTemplate<String, Object> redisTemplate;
  private final ObjectMapper objectMapper;
  private final String keyPrefix;

  /**
   * 构造函数
   *
   * @param redisTemplate Redis 模板
   * @param objectMapper JSON 对象映射器
   * @param keyPrefix 键前缀，用于多项目/多环境隔离
   */
  public RedisClient(
      RedisTemplate<String, Object> redisTemplate, ObjectMapper objectMapper, String keyPrefix) {
    this.redisTemplate = redisTemplate;
    this.objectMapper = objectMapper;
    this.keyPrefix = keyPrefix;
  }

  /**
   * 为键添加前缀
   *
   * @param key 原始键
   * @return 带前缀的键
   */
  private String applyPrefix(String key) {
    if (keyPrefix == null || keyPrefix.isEmpty()) {
      return key;
    }
    // 如果键已经包含了前缀，则不再添加前缀
    // 这样可以避免重复添加前缀，例如：demo:sys_dict:sys_user_sex
    if (key.startsWith(keyPrefix)) {
      return key;
    }
    return keyPrefix + key;
  }

  /**
   * 为多个键添加前缀
   *
   * @param keys 键集合
   * @return 带前缀的键集合
   */
  private Collection<String> applyPrefix(Collection<String> keys) {
    if (keyPrefix == null || keyPrefix.isEmpty()) {
      return keys;
    }
    return keys.stream().map(this::applyPrefix).collect(Collectors.toList());
  }

  // ===== 1. 通用 Key 操作 =====

  /**
   * 删除指定的键
   *
   * <pre>
   * redisClient.delete("user:1");
   * redisClient.delete("session:abc123");
   * </pre>
   *
   * @param key 键名
   */
  public void delete(final String key) {
    redisTemplate.delete(applyPrefix(key));
  }

  /**
   * 批量删除多个键
   *
   * <pre>
   * List<String> keys = Arrays.asList("user:1", "user:2", "user:3");
   * redisClient.delete(keys);
   * </pre>
   *
   * @param keys 键名集合
   */
  public void delete(final Collection<String> keys) {
    redisTemplate.delete(applyPrefix(keys));
  }

  /**
   * 检查键是否存在
   *
   * <pre>
   * boolean exists = redisClient.hasKey("user:1");
   * if (exists) {
   *     // 键存在
   * }
   * </pre>
   *
   * @param key 键名
   * @return true 如果键存在，false 如果键不存在
   */
  public boolean hasKey(final String key) {
    return Boolean.TRUE.equals(redisTemplate.hasKey(applyPrefix(key)));
  }

  /**
   * 为键设置过期时间
   *
   * <pre>
   * redisClient.expire("user:1", 1, TimeUnit.HOURS);
   * redisClient.expire("session:abc123", 30, TimeUnit.MINUTES);
   * </pre>
   *
   * @param key 键名
   * @param timeout 过期时间
   * @param unit 时间单位
   */
  public void expire(final String key, final long timeout, final TimeUnit unit) {
    redisTemplate.expire(applyPrefix(key), timeout, unit);
  }

  /**
   * 根据模式匹配查找键
   *
   * <pre>
   * Set<String> allUsers = redisClient.keys("user:*");
   * Set<String> adminUsers = redisClient.keys("user:admin:*");
   * Set<String> sessions = redisClient.keys("session:*");
   * </pre>
   *
   * @param pattern 匹配模式，支持通配符 * 和 ?
   * @return 匹配的键集合
   */
  public Set<String> keys(final String pattern) {
    return redisTemplate.keys(applyPrefix(pattern));
  }

  // ===== 2. STRING (字符串/对象) 操作 =====

  /**
   * 设置键值对
   *
   * <pre>
   * redisClient.set("user:1", userDto);
   * redisClient.set("config:theme", "dark");
   * </pre>
   *
   * @param key 键名
   * @param value 值对象
   */
  public void set(final String key, final Object value) {
    redisTemplate.opsForValue().set(applyPrefix(key), value);
  }

  /**
   * 设置键值对并指定过期时间
   *
   * <pre>
   * redisClient.set("session:abc123", sessionData, 30, TimeUnit.MINUTES);
   * redisClient.set("cache:weather", weatherData, 1, TimeUnit.HOURS);
   * </pre>
   *
   * @param key 键名
   * @param value 值对象
   * @param timeout 过期时间
   * @param unit 时间单位
   */
  public void set(final String key, final Object value, final long timeout, final TimeUnit unit) {
    redisTemplate.opsForValue().set(applyPrefix(key), value, timeout, unit);
  }

  /**
   * 获取键值并转换为指定类型
   *
   * <pre>
   * Optional<UserDto> user = redisClient.get("user:1", UserDto.class);
   * user.ifPresent(u -> System.out.println(u.getUsername()));
   *
   * Optional<String> theme = redisClient.get("config:theme", String.class);
   * </pre>
   *
   * @param <T> 目标类型
   * @param key 键名
   * @param clazz 目标类型类对象
   * @return 包含值的 Optional，如果键不存在返回 Optional.empty()
   */
  public <T> Optional<T> get(final String key, final Class<T> clazz) {
    Object value = redisTemplate.opsForValue().get(applyPrefix(key));
    if (value == null) {
      return Optional.empty();
    }
    return Optional.of(objectMapper.convertValue(value, clazz));
  }

  /**
   * 获取键值并转换为复杂类型（如 List、Map 等）
   *
   * <pre>
   * Optional<List<UserDto>> users = redisClient.get("users:all",
   *     new TypeReference<List<UserDto>>() {});
   *
   * Optional<Map<String, Object>> config = redisClient.get("config:app",
   *     new TypeReference<Map<String, Object>>() {});
   * </pre>
   *
   * @param <T> 目标类型
   * @param key 键名
   * @param typeReference 类型引用，用于复杂类型转换
   * @return 包含值的 Optional，如果键不存在返回 Optional.empty()
   */
  public <T> Optional<T> get(final String key, final TypeReference<T> typeReference) {
    Object value = redisTemplate.opsForValue().get(applyPrefix(key));
    if (value == null) {
      return Optional.empty();
    }
    return Optional.of(objectMapper.convertValue(value, typeReference));
  }

  /**
   * 原子递增操作
   *
   * <pre>
   * long counter = redisClient.increment("counter:visits", 1);
   * long views = redisClient.increment("article:1:views", 1);
   * long stock = redisClient.increment("product:1:stock", -1);
   * </pre>
   *
   * @param key 键名
   * @param delta 递增量（可以为负数）
   * @return 递增后的值
   */
  public long increment(final String key, final long delta) {
    return Optional.ofNullable(redisTemplate.opsForValue().increment(applyPrefix(key), delta))
        .orElse(0L);
  }

  // ===== 3. HASH (哈希) 操作 =====

  /**
   * 设置哈希字段值
   *
   * <pre>
   * redisClient.hSet("product:1", "name", "iPhone 15");
   * redisClient.hSet("product:1", "price", 7999.00);
   * redisClient.hSet("user:1", "last_login", LocalDateTime.now());
   * </pre>
   *
   * @param key 哈希键名
   * @param field 字段名
   * @param value 字段值
   */
  public void hSet(final String key, final String field, final Object value) {
    redisTemplate.opsForHash().put(applyPrefix(key), field, value);
  }

  /**
   * 获取哈希字段值并转换为指定类型
   *
   * <pre>
   * Optional<String> name = redisClient.hGet("product:1", "name", String.class);
   * Optional<Double> price = redisClient.hGet("product:1", "price", Double.class);
   * Optional<LocalDateTime> lastLogin = redisClient.hGet("user:1", "last_login", LocalDateTime.class);
   * </pre>
   *
   * @param <T> 目标类型
   * @param key 哈希键名
   * @param field 字段名
   * @param clazz 目标类型类对象
   * @return 包含字段值的 Optional，如果字段不存在返回 Optional.empty()
   */
  public <T> Optional<T> hGet(final String key, final String field, final Class<T> clazz) {
    Object value = redisTemplate.opsForHash().get(applyPrefix(key), field);
    if (value == null) {
      return Optional.empty();
    }
    return Optional.of(objectMapper.convertValue(value, clazz));
  }

  /**
   * 获取哈希表中的所有字段和值
   *
   * <pre>
   * Map<Object, Object> productInfo = redisClient.hGetAll("product:1");
   * productInfo.forEach((field, value) -> {
   *     System.out.println(field + ": " + value);
   * });
   * </pre>
   *
   * @param key 哈希键名
   * @return 包含所有字段和值的 Map
   */
  public Map<Object, Object> hGetAll(final String key) {
    return redisTemplate.opsForHash().entries(applyPrefix(key));
  }

  /**
   * 删除哈希表中的一个或多个字段
   *
   * <pre>
   * long deleted = redisClient.hDelete("product:1", "old_price", "discount");
   * System.out.println("删除了 " + deleted + " 个字段");
   * </pre>
   *
   * @param key 哈希键名
   * @param fields 要删除的字段名
   * @return 实际删除的字段数量
   */
  public long hDelete(final String key, final Object... fields) {
    return redisTemplate.opsForHash().delete(applyPrefix(key), fields);
  }

  // ===== 4. LIST (列表) 操作 =====

  /**
   * 从左侧（头部）推入一个或多个值到列表
   *
   * <pre>
   * long length = redisClient.lPush("queue:tasks", task1, task2, task3);
   * long count = redisClient.lPush("log:errors", "Error 1", "Error 2");
   * </pre>
   *
   * @param key 列表键名
   * @param values 要推入的值
   * @return 推入后的列表长度
   */
  public long lPush(final String key, final Object... values) {
    return Optional.ofNullable(redisTemplate.opsForList().leftPushAll(applyPrefix(key), values))
        .orElse(0L);
  }

  /**
   * 从右侧（尾部）弹出一个值
   *
   * <pre>
   * Optional<Task> task = redisClient.rPop("queue:tasks", Task.class);
   * task.ifPresent(t -> System.out.println("处理任务：" + t.getName()));
   *
   * Optional<String> log = redisClient.rPop("log:errors", String.class);
   * </pre>
   *
   * @param <T> 目标类型
   * @param key 列表键名
   * @param clazz 目标类型类对象
   * @return 包含弹出值的 Optional，如果列表为空返回 Optional.empty()
   */
  public <T> Optional<T> rPop(final String key, Class<T> clazz) {
    Object value = redisTemplate.opsForList().rightPop(applyPrefix(key));
    if (value == null) {
      return Optional.empty();
    }
    return Optional.of(objectMapper.convertValue(value, clazz));
  }

  /**
   * 获取列表中指定范围的元素
   *
   * <pre>
   * // 获取前 10 个元素
   * List<Task> recentTasks = redisClient.lRange("queue:tasks", 0, 9, Task.class);
   *
   * // 获取所有元素
   * List<String> allLogs = redisClient.lRange("log:errors", 0, -1, String.class);
   *
   * // 获取最后 5 个元素
   * List<String> lastLogs = redisClient.lRange("log:errors", -5, -1, String.class);
   * </pre>
   *
   * @param <T> 目标类型
   * @param key 列表键名
   * @param start 起始索引（0 表示第一个元素）
   * @param end 结束索引（-1 表示最后一个元素）
   * @param clazz 目标类型类对象
   * @return 指定范围内的元素列表
   */
  public <T> List<T> lRange(final String key, long start, long end, final Class<T> clazz) {
    List<Object> range = redisTemplate.opsForList().range(applyPrefix(key), start, end);
    if (range == null) {
      return Collections.emptyList();
    }
    return range.stream()
        .map(item -> objectMapper.convertValue(item, clazz))
        .collect(Collectors.toList());
  }

  // ===== 5. SET (集合) 操作 =====

  /**
   * 向集合中添加一个或多个成员
   *
   * <pre>
   * long added = redisClient.sAdd("tags:article:1", "java", "spring", "redis");
   * long count = redisClient.sAdd("roles:user:1", "admin", "user", "editor");
   * </pre>
   *
   * @param key 集合键名
   * @param values 要添加的成员
   * @return 实际添加的成员数量（不包括已存在的成员）
   */
  public long sAdd(final String key, final Object... values) {
    return Optional.ofNullable(redisTemplate.opsForSet().add(applyPrefix(key), values)).orElse(0L);
  }

  /**
   * 判断成员是否存在于集合中
   *
   * <pre>
   * boolean hasJavaTag = redisClient.sIsMember("tags:article:1", "java");
   * boolean isAdmin = redisClient.sIsMember("roles:user:1", "admin");
   *
   * if (hasJavaTag) {
   *     System.out.println("文章包含 Java 标签");
   * }
   * </pre>
   *
   * @param key 集合键名
   * @param member 要检查的成员
   * @return true 如果成员存在，false 如果成员不存在
   */
  public boolean sIsMember(final String key, final Object member) {
    return Boolean.TRUE.equals(redisTemplate.opsForSet().isMember(applyPrefix(key), member));
  }

  /**
   * 获取集合中的所有成员
   *
   * <pre>
   * Set<Object> tags = redisClient.sMembers("tags:article:1");
   * tags.forEach(tag -> System.out.println("标签：" + tag));
   *
   * Set<Object> roles = redisClient.sMembers("roles:user:1");
   * roles.forEach(role -> System.out.println("角色：" + role));
   * </pre>
   *
   * @param key 集合键名
   * @return 包含所有成员的 Set
   */
  public Set<Object> sMembers(final String key) {
    return redisTemplate.opsForSet().members(applyPrefix(key));
  }
}
