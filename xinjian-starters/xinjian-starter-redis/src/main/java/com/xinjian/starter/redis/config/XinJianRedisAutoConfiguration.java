package com.xinjian.starter.redis.config;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.PropertyAccessor;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.xinjian.starter.redis.core.RedisClient;
import com.xinjian.starter.redis.properties.XinJianRedisProperties;
import java.time.Duration;
import java.util.HashMap;
import java.util.Map;
import org.apache.commons.lang3.StringUtils;
import org.springframework.boot.autoconfigure.cache.CacheProperties;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.cache.CacheManager;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.data.redis.cache.RedisCacheConfiguration;
import org.springframework.data.redis.cache.RedisCacheManager;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.serializer.Jackson2JsonRedisSerializer;
import org.springframework.data.redis.serializer.RedisSerializationContext;
import org.springframework.data.redis.serializer.StringRedisSerializer;

/**
 * XinJian Redis 自动配置类
 *
 * <p>提供 Redis 的自动配置功能，包括：
 *
 * <ul>
 *   <li>RedisTemplate 配置 - 使用 Jackson 进行 JSON 序列化
 *   <li>RedisClient 客户端 - 类型安全的编程式操作接口
 *   <li>CacheManager 配置 - 支持 Spring Cache 注解
 *   <li>键前缀管理 - 避免多项目/多环境下的键冲突
 * </ul>
 *
 * <p>使用条件：
 *
 * <ul>
 *   <li>类路径中存在 RedisTemplate
 *   <li>配置属性 xinjian.redis.enabled 为 true（默认值）
 * </ul>
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Configuration
@ConditionalOnClass(RedisTemplate.class)
@EnableCaching
@EnableConfigurationProperties({XinJianRedisProperties.class, CacheProperties.class})
@ConditionalOnProperty(
    prefix = "xinjian.redis",
    name = "enabled",
    havingValue = "true",
    matchIfMissing = true)
public class XinJianRedisAutoConfiguration {

  /**
   * 配置 Redis 专用的 ObjectMapper
   *
   * <p>配置特性：
   *
   * <ul>
   *   <li>支持 Java 8 时间类型（LocalDateTime, LocalDate 等）
   *   <li>自动检测所有属性的可见性
   *   <li>使用 Jackson 的默认配置
   * </ul>
   *
   * @return 配置好的 ObjectMapper 实例
   */
  @Bean
  @ConditionalOnMissingBean(name = "redisObjectMapper")
  public ObjectMapper redisObjectMapper() {
    ObjectMapper objectMapper = new ObjectMapper();
    objectMapper.registerModule(new JavaTimeModule());
    objectMapper.setVisibility(PropertyAccessor.ALL, JsonAutoDetect.Visibility.ANY);
    return objectMapper;
  }

  /**
   * 配置主 RedisTemplate
   *
   * <p>序列化策略：
   *
   * <ul>
   *   <li>Key: 使用 StringRedisSerializer
   *   <li>Hash Key: 使用 StringRedisSerializer
   *   <li>Value: 使用 Jackson2JsonRedisSerializer
   *   <li>Hash Value: 使用 Jackson2JsonRedisSerializer
   * </ul>
   *
   * <p>特性：
   *
   * <ul>
   *   <li>标记为 @Primary，作为默认的 RedisTemplate
   *   <li>支持复杂对象的 JSON 序列化
   *   <li>键的可读性好（字符串形式）
   * </ul>
   *
   * @param connectionFactory Redis 连接工厂
   * @param redisObjectMapper Redis 专用的 ObjectMapper
   * @return 配置好的 RedisTemplate 实例
   */
  @Bean
  @ConditionalOnMissingBean
  @Primary // 标记为主 Bean
  public RedisTemplate<String, Object> redisTemplate(
      RedisConnectionFactory connectionFactory, ObjectMapper redisObjectMapper) {

    RedisTemplate<String, Object> template = new RedisTemplate<>();
    template.setConnectionFactory(connectionFactory);

    Jackson2JsonRedisSerializer<Object> jacksonSerializer =
        new Jackson2JsonRedisSerializer<>(Object.class);
    jacksonSerializer.setObjectMapper(redisObjectMapper);
    StringRedisSerializer stringSerializer = new StringRedisSerializer();

    template.setKeySerializer(stringSerializer);
    template.setHashKeySerializer(stringSerializer);
    template.setValueSerializer(jacksonSerializer);
    template.setHashValueSerializer(jacksonSerializer);

    template.afterPropertiesSet();
    return template;
  }

  /**
   * 创建 RedisClient 客户端实例
   *
   * <p>RedisClient 提供类型安全的编程式 Redis 操作接口，支持：
   *
   * <ul>
   *   <li>自动键前缀管理
   *   <li>类型安全的值操作
   *   <li>丰富的数据结构操作（String, Hash, List, Set）
   * </ul>
   *
   * <p>键前缀优先级：
   *
   * <ol>
   *   <li>xinjian.redis.key-prefix（最高优先级）
   *   <li>spring.cache.redis.key-prefix
   *   <li>无前缀
   * </ol>
   *
   * @param redisTemplate Redis 模板
   * @param redisObjectMapper Redis 专用的 ObjectMapper
   * @param xinJianRedisProperties XinJian Redis 配置属性
   * @param cacheProperties Spring Cache 配置属性
   * @return 配置好的 RedisClient 实例
   */
  @Bean
  @ConditionalOnMissingBean
  public RedisClient redisClient(
      RedisTemplate<String, Object> redisTemplate,
      ObjectMapper redisObjectMapper,
      XinJianRedisProperties xinJianRedisProperties,
      CacheProperties cacheProperties) {

    String finalKeyPrefix = determineFinalKeyPrefix(xinJianRedisProperties, cacheProperties);
    return new RedisClient(redisTemplate, redisObjectMapper, finalKeyPrefix);
  }

  /**
   * 配置 Spring Cache 的 CacheManager
   *
   * <p>支持 @Cacheable、@CacheEvict、@CachePut 等注解，特性包括：
   *
   * <ul>
   *   <li>默认 TTL 配置
   *   <li>按缓存名称的专属 TTL 配置
   *   <li>键前缀管理
   *   <li>JSON 序列化
   *   <li>禁止缓存 null 值
   * </ul>
   *
   * <p>配置示例：
   *
   * <pre>
   * xinjian:
   *   redis:
   *     default-ttl: 1h
   *     specs:
   *       users: 30m
   *       products: 2h
   *       sessions: 24h
   * </pre>
   *
   * @param connectionFactory Redis 连接工厂
   * @param redisObjectMapper Redis 专用的 ObjectMapper
   * @param xinJianRedisProperties XinJian Redis 配置属性
   * @param cacheProperties Spring Cache 配置属性
   * @return 配置好的 CacheManager 实例
   */
  @Bean
  @Primary
  @ConditionalOnMissingBean
  public CacheManager cacheManager(
      RedisConnectionFactory connectionFactory,
      ObjectMapper redisObjectMapper,
      XinJianRedisProperties xinJianRedisProperties,
      CacheProperties cacheProperties) {

    // 获取最终的前缀配置
    String keyPrefix = determineFinalKeyPrefix(xinJianRedisProperties, cacheProperties);

    // 创建基础配置
    RedisCacheConfiguration defaultConfig =
        createBaseRedisCacheConfiguration(
            xinJianRedisProperties.getDefaultTtl(), redisObjectMapper, keyPrefix);

    // 为每个缓存名称创建配置
    Map<String, RedisCacheConfiguration> cacheConfigurations = new HashMap<>();
    if (xinJianRedisProperties.getSpecs() != null) {
      for (Map.Entry<String, Duration> entry : xinJianRedisProperties.getSpecs().entrySet()) {
        String cacheName = entry.getKey();
        Duration ttl = entry.getValue();

        // 复制默认配置，并设置 TTL
        RedisCacheConfiguration config = defaultConfig.entryTtl(ttl);
        cacheConfigurations.put(cacheName, config);
      }
    }

    RedisCacheManager.RedisCacheManagerBuilder builder =
        RedisCacheManager.builder(connectionFactory)
            .cacheDefaults(defaultConfig)
            .withInitialCacheConfigurations(cacheConfigurations);

    return builder.build();
  }

  /**
   * 确定最终的键前缀
   *
   * <p>优先级顺序：
   *
   * <ol>
   *   <li>xinjian.redis.key-prefix（最高优先级）
   *   <li>spring.cache.redis.key-prefix
   *   <li>空字符串（无前缀）
   * </ol>
   *
   * <p>前缀处理规则：
   *
   * <ul>
   *   <li>自动确保前缀以冒号结尾
   *   <li>避免重复冒号
   *   <li>返回空字符串而非 null，避免 NPE
   * </ul>
   *
   * @param xinJianProps XinJian Redis 配置属性
   * @param springProps Spring Cache 配置属性
   * @return 最终的键前缀
   */
  private String determineFinalKeyPrefix(
      XinJianRedisProperties xinJianProps, CacheProperties springProps) {
    // 优先使用自定义前缀
    String customPrefix = xinJianProps.getKeyPrefix();
    if (StringUtils.isNotBlank(customPrefix)) {
      // 确保前缀以冒号结尾
      return customPrefix.endsWith(":") ? customPrefix : customPrefix + ":";
    }
    // 回退到 Spring Cache 配置
    CacheProperties.Redis redisProps = springProps.getRedis();
    if (redisProps.isUseKeyPrefix() && StringUtils.isNotBlank(redisProps.getKeyPrefix())) {
      // 确保前缀以冒号结尾
      String prefix = redisProps.getKeyPrefix();
      return prefix.endsWith(":") ? prefix : prefix + ":";
    }
    return ""; // 返回空字符串而非 null，避免 NPE
  }

  /**
   * 创建基础的 Redis 缓存配置
   *
   * <p>配置特性：
   *
   * <ul>
   *   <li>Key 使用 String 序列化
   *   <li>Value 使用 JSON 序列化
   *   <li>禁止缓存 null 值
   *   <li>支持 TTL 设置
   *   <li>支持键前缀
   * </ul>
   *
   * <p>键前缀处理逻辑：
   *
   * <pre>
   * keyPrefix = "demo:"
   * cacheName = "users"
   * 最终键 = "demo:users:123"
   *
   * </pre>
   *
   * @param ttl 缓存过期时间
   * @param redisObjectMapper Redis 专用的 ObjectMapper
   * @param keyPrefix 键前缀
   * @return 配置好的 RedisCacheConfiguration 实例
   */
  private RedisCacheConfiguration createBaseRedisCacheConfiguration(
      Duration ttl, ObjectMapper redisObjectMapper, String keyPrefix) {

    Jackson2JsonRedisSerializer<Object> jacksonSerializer =
        new Jackson2JsonRedisSerializer<>(Object.class);
    jacksonSerializer.setObjectMapper(redisObjectMapper);

    RedisCacheConfiguration config =
        RedisCacheConfiguration.defaultCacheConfig()
            .serializeKeysWith(
                RedisSerializationContext.SerializationPair.fromSerializer(
                    new StringRedisSerializer()))
            .serializeValuesWith(
                RedisSerializationContext.SerializationPair.fromSerializer(jacksonSerializer))
            .disableCachingNullValues();

    // 设置 TTL（Duration.ZERO 表示永久缓存，不设置过期时间）
    if (ttl != null && !ttl.isZero()) {
      config = config.entryTtl(ttl);
    }

    // 设置键前缀（关键部分）
    if (StringUtils.isNotBlank(keyPrefix)) {
      config =
          config.computePrefixWith(
              cacheName -> {
                // 如果 cacheName 已经以冒号结尾，则不再添加额外冒号
                if (cacheName.endsWith(":")) {
                  return keyPrefix + cacheName;
                }
                // 否则添加单个冒号作为分隔符
                return keyPrefix + cacheName + ":";
              });
    }

    return config;
  }
}
