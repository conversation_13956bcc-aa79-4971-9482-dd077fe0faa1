package com.xinjian.starter.redis.properties;

import java.time.Duration;
import java.util.HashMap;
import java.util.Map;
import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * XinJian Redis Starter 配置属性
 *
 * <p>提供 XinJian Redis Starter 的配置选项，包括键前缀管理、TTL 配置等。
 *
 * <p>配置示例：
 *
 * <pre>
 * xinjian:
 *   redis:
 *     enabled: true
 *     key-prefix: demo:
 *     default-ttl: 1h
 *     specs:
 *       users: 30m
 *       products: 2h
 *       sessions: 24h
 *       cache-name: 1d
 * </pre>
 *
 * <p>配置说明：
 *
 * <ul>
 *   <li>enabled: 是否启用 XinJian Redis Starter（默认：true）
 *   <li>key-prefix: 全局键前缀，用于多项目/多环境隔离（最高优先级）
 *   <li>default-ttl: @Cacheable 注解的全局默认过期时间（默认：1 小时）
 *   <li>specs: 为特定缓存名称配置专属 TTL
 * </ul>
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@ConfigurationProperties(prefix = "xinjian.redis")
public class XinJianRedisProperties {

  /**
   * 是否启用 XinJian Redis Starter
   *
   * <p>默认值：true
   *
   * <p>设置为 false 可以禁用自动配置
   */
  private boolean enabled = true;

  /**
   * 全局 Key 前缀，用于多项目/多环境隔离
   *
   * <p>拥有最高优先级，如果设置了此项，它将覆盖 spring.cache.redis.key-prefix。
   *
   * <p>前缀会自动添加冒号分隔符，无需手动添加。
   *
   * <p>示例值：demo:
   */
  private String keyPrefix;

  /**
   * @Cacheable 的全局默认过期时间
   *
   * <p>默认值：1 小时
   *
   * <p>支持的时间单位：ms（毫秒）、s（秒）、m（分钟）、h（小时）、d（天）
   *
   * <p>示例值：30m, 1h, 2d, 7d
   */
  private Duration defaultTtl = Duration.ofHours(1);

  /**
   * 存放每个 cacheName 的专属 TTL 配置
   *
   * <p>可以为不同的缓存名称设置不同的过期时间。
   *
   * <p>Map 的 key 是缓存名称，value 是过期时间。
   *
   * <p>示例：users=30m, products=2h, sessions=24h
   */
  private Map<String, Duration> specs = new HashMap<>();

  /**
   * 获取是否启用 XinJian Redis Starter
   *
   * @return 是否启用
   */
  public boolean isEnabled() {
    return enabled;
  }

  /**
   * 设置是否启用 XinJian Redis Starter
   *
   * @param enabled 是否启用
   */
  public void setEnabled(boolean enabled) {
    this.enabled = enabled;
  }

  /**
   * 获取全局键前缀
   *
   * @return 全局键前缀
   */
  public String getKeyPrefix() {
    return keyPrefix;
  }

  /**
   * 设置全局键前缀
   *
   * @param keyPrefix 全局键前缀
   */
  public void setKeyPrefix(String keyPrefix) {
    this.keyPrefix = keyPrefix;
  }

  /**
   * 获取全局默认过期时间
   *
   * @return 全局默认过期时间
   */
  public Duration getDefaultTtl() {
    return defaultTtl;
  }

  /**
   * 设置全局默认过期时间
   *
   * @param defaultTtl 全局默认过期时间
   */
  public void setDefaultTtl(Duration defaultTtl) {
    this.defaultTtl = defaultTtl;
  }

  /**
   * 获取缓存名称的专属 TTL 配置
   *
   * @return 缓存名称与 TTL 的映射关系
   */
  public Map<String, Duration> getSpecs() {
    return specs;
  }

  /**
   * 设置缓存名称的专属 TTL 配置
   *
   * @param specs 缓存名称与 TTL 的映射关系
   */
  public void setSpecs(Map<String, Duration> specs) {
    this.specs = specs;
  }
}
