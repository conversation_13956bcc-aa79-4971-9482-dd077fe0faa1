package com.xinjian.common.enums;

/** 组件标识枚举 */
public enum ComponentType {
  /** Layout 组件 */
  LAYOUT("Layout", "布局组件"),

  /** ParentView 组件 */
  PARENT_VIEW("ParentView", "父视图组件"),

  /** InnerLink 组件 */
  INNER_LINK("InnerLink", "内链组件");

  private final String value;
  private final String description;

  ComponentType(String value, String description) {
    this.value = value;
    this.description = description;
  }

  public String getValue() {
    return value;
  }

  public String getDescription() {
    return description;
  }

  public static ComponentType fromValue(String value) {
    for (ComponentType type : values()) {
      if (type.value.equals(value)) {
        return type;
      }
    }
    throw new IllegalArgumentException("Unknown component type: " + value);
  }
}
