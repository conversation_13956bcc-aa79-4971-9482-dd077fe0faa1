package com.xinjian.common.exception.status400;

import com.xinjian.common.exception.ErrorConstants;
import org.zalando.problem.AbstractThrowableProblem;
import org.zalando.problem.Status;

/**
 * 400 Bad Request - 无效的输入数据
 *
 * <p>当客户端提交的数据格式不正确、类型不匹配、或不符合业务规则定义的约束时，应抛出此异常。例如，一个期望为数字的字段收到了字符串，或者一个必填字段为空。
 */
public class InvalidInputDataException extends AbstractThrowableProblem {
  public InvalidInputDataException(final String message) {
    super(ErrorConstants.INVALID_INPUT_DATA, "Bad Request", Status.BAD_REQUEST, message);
  }
}
