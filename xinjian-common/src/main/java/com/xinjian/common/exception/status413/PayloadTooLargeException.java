package com.xinjian.common.exception.status413;

import com.xinjian.common.exception.ErrorConstants;
import org.zalando.problem.AbstractThrowableProblem;
import org.zalando.problem.Status;

/**
 * 413 Payload Too Large - 请求体过大
 *
 * <p>当服务器拒绝处理请求，因为请求体的大小超过了服务器愿意或能够处理的限制时，将抛出此异常。这是一个通用的请求体过大异常。在可能的情况下，应优先使用更具体的异常类型，例如 {@link
 * FileUploadTooLargeException}。
 */
public class PayloadTooLargeException extends AbstractThrowableProblem {

  public PayloadTooLargeException(String message) {
    super(
        ErrorConstants.PAYLOAD_TOO_LARGE,
        "Payload Too Large",
        Status.REQUEST_ENTITY_TOO_LARGE,
        message);
  }
}
