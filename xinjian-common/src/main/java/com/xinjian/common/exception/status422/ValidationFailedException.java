package com.xinjian.common.exception.status422;

import static com.xinjian.common.exception.ErrorConstants.VALIDATION_FAILED;

import org.zalando.problem.AbstractThrowableProblem;
import org.zalando.problem.Status;

/**
 * 422 Unprocessable Entity - 数据校验失败
 *
 * <p>当请求实体通过了基本的语法检查，但未能通过更复杂的业务规则或数据完整性校验时，应抛出此异常。这通常发生在使用 Java Bean Validation (JSR 380)
 * 等框架进行校验时。它与 {@link InvalidInputDataException} (400) 的区别在于，400 通常是语法级别的错误，而 422 是语义或业务逻辑级别的错误。
 */
public class ValidationFailedException extends AbstractThrowableProblem {

  public ValidationFailedException(String message) {
    super(VALIDATION_FAILED, "Validation Failed", Status.UNPROCESSABLE_ENTITY, message);
  }
}
