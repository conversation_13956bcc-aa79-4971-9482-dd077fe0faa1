package com.xinjian.common.exception.status413;

import com.xinjian.common.exception.ErrorConstants;
import org.zalando.problem.AbstractThrowableProblem;
import org.zalando.problem.Status;

/**
 * 413 Payload Too Large - 文件上传过大
 *
 * <p>当用户尝试上传一个文件，但其大小超过了系统配置的最大文件上传限制时，应抛出此异常。
 */
public class FileUploadTooLargeException extends AbstractThrowableProblem {
  public FileUploadTooLargeException(final String message) {
    super(
        ErrorConstants.FILE_UPLOAD_TOO_LARGE,
        "File Upload Too Large",
        Status.REQUEST_ENTITY_TOO_LARGE,
        message);
  }
}
