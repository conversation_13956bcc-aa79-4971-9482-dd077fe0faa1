package com.xinjian.common.exception.status400;

import com.xinjian.common.exception.ErrorConstants;
import org.zalando.problem.AbstractThrowableProblem;
import org.zalando.problem.Status;

/**
 * 400 Bad Request - 密码强度不足
 *
 * <p>当用户尝试设置或更新密码，但新密码不符合系统定义的密码强度策略时，应抛出此异常。策略可能包括：最小长度、必须包含大写字母、小写字母、数字和特殊字符等。
 */
public class PasswordTooWeakException extends AbstractThrowableProblem {
  public PasswordTooWeakException(final String message) {
    super(ErrorConstants.PASSWORD_TOO_WEAK, "Bad Request", Status.BAD_REQUEST, message);
  }
}
