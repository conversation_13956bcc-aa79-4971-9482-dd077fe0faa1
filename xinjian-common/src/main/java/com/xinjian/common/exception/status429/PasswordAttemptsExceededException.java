package com.xinjian.common.exception.status429;

import static com.xinjian.common.exception.ErrorConstants.PASSWORD_ATTEMPTS_EXCEEDED;

import org.zalando.problem.AbstractThrowableProblem;
import org.zalando.problem.Status;

/**
 * 429 Too Many Requests - 密码尝试次数超限
 *
 * <p>当用户在短时间内连续输入错误密码的次数超过系统设定的阈值时，应抛出此异常。这是一种安全措施，旨在防止暴力破解密码攻击。
 */
public class PasswordAttemptsExceededException extends AbstractThrowableProblem {

  public PasswordAttemptsExceededException(String message) {
    super(
        PASSWORD_ATTEMPTS_EXCEEDED,
        "Password Attempts Exceeded",
        Status.TOO_MANY_REQUESTS,
        message);
  }
}
