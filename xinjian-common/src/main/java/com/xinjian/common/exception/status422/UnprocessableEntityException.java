package com.xinjian.common.exception.status422;

import com.xinjian.common.exception.ErrorConstants;
import org.zalando.problem.AbstractThrowableProblem;
import org.zalando.problem.Status;

/**
 * 422 Unprocessable Entity - 无法处理的实体
 *
 * <p>当服务器理解请求实体的内容类型（例如，JSON），并且请求实体的语法是正确的，但服务器无法处理所包含的指令时，将抛出此异常。这是一个通用的 422
 * 异常。在可能的情况下，应优先使用更具体的异常类型，例如 {@link ValidationFailedException} 或 {@link
 * BusinessRuleViolationException}。
 */
public class UnprocessableEntityException extends AbstractThrowableProblem {

  public UnprocessableEntityException(String message) {
    super(
        ErrorConstants.UNPROCESSABLE_ENTITY,
        "Unprocessable Entity",
        Status.UNPROCESSABLE_ENTITY,
        message);
  }
}
