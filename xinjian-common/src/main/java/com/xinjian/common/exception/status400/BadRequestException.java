package com.xinjian.common.exception.status400;

import com.xinjian.common.exception.ErrorConstants;
import org.zalando.problem.AbstractThrowableProblem;
import org.zalando.problem.Status;

/**
 * 400 Bad Request - 通用错误请求
 *
 * <p>当服务器由于被认为是客户端错误（例如，畸形的请求语法、无效的请求消息帧或欺骗性请求路由）而无法处理请求时，将抛出此异常。这是一个通用的、捕获所有类型的错误请求的异常。
 * 在可能的情况下，应优先使用更具体的异常类型，例如 {@link InvalidInputDataException} 或 {@link PasswordTooWeakException}。
 */
public class BadRequestException extends AbstractThrowableProblem {
  public BadRequestException(final String message) {
    super(ErrorConstants.BAD_REQUEST, "Bad Request", Status.BAD_REQUEST, message);
  }
}
