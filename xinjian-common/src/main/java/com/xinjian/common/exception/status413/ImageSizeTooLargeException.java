package com.xinjian.common.exception.status413;

import com.xinjian.common.exception.ErrorConstants;
import org.zalando.problem.AbstractThrowableProblem;
import org.zalando.problem.Status;

/**
 * 413 Payload Too Large - 图片尺寸过大
 *
 * <p>当用户尝试上传一个图片文件，但其尺寸（宽度和高度）超过了系统定义的最大限制时，应抛出此异常。这与文件大小（字节）不同，关注的是图片的像素尺寸。
 */
public class ImageSizeTooLargeException extends AbstractThrowableProblem {

  public ImageSizeTooLargeException(String message) {
    super(
        ErrorConstants.IMAGE_SIZE_TOO_LARGE,
        "Image Size Too Large",
        Status.REQUEST_ENTITY_TOO_LARGE,
        message);
  }
}
