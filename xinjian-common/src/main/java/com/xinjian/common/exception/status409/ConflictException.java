package com.xinjian.common.exception.status409;

import com.xinjian.common.exception.ErrorConstants;
import org.zalando.problem.AbstractThrowableProblem;
import org.zalando.problem.Status;

/**
 * 409 Conflict - 请求冲突
 *
 * <p>当请求无法完成，因为它与资源的当前状态发生冲突时，将抛出此异常。这是一个通用的冲突异常。在可能的情况下，应优先使用更具体的异常类型，例如 {@link
 * UsernameAlreadyExistsException} 或 {@link EmailAlreadyInUseException}。
 */
public class ConflictException extends AbstractThrowableProblem {

  public ConflictException(String message) {
    super(ErrorConstants.CONFLICT, "Conflict", Status.CONFLICT, message);
  }
}
