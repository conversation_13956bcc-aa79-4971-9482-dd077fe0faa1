package com.xinjian.common.exception.status404;

import com.xinjian.common.exception.ErrorConstants;
import org.zalando.problem.AbstractThrowableProblem;
import org.zalando.problem.Status;

/**
 * 404 Not Found - 资源未找到
 *
 * <p>当服务器无法根据请求的 URI 找到对应的资源时，应抛出此异常。这是一个通用的“资源未找到”异常，适用于任何类型的资源（例如，文件、数据记录、配置项等），
 * 而不仅仅是用户。如果需要特指用户未找到，应使用 {@link UserNotFoundException}。
 */
public class ResourceNotFoundException extends AbstractThrowableProblem {
  public ResourceNotFoundException(final String message) {
    super(ErrorConstants.RESOURCE_NOT_FOUND, "Not Found", Status.NOT_FOUND, message);
  }
}
