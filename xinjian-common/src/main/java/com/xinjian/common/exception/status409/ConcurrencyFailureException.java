package com.xinjian.common.exception.status409;

import com.xinjian.common.exception.ErrorConstants;
import org.zalando.problem.AbstractThrowableProblem;
import org.zalando.problem.Status;

/**
 * 409 Conflict - 并发修改冲突
 *
 * <p>当尝试更新一个资源时，发现该资源在读取后已被其他事务修改，导致乐观锁失败时，应抛出此异常。这通常发生在多个用户或进程同时尝试编辑同一份数据时。
 */
public class ConcurrencyFailureException extends AbstractThrowableProblem {
  public ConcurrencyFailureException(final String message) {
    super(ErrorConstants.CONCURRENCY_FAILURE, "Conflict", Status.CONFLICT, message);
  }
}
