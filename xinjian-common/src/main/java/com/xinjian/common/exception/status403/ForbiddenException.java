package com.xinjian.common.exception.status403;

import com.xinjian.common.exception.ErrorConstants;
import org.zalando.problem.AbstractThrowableProblem;
import org.zalando.problem.Status;

/**
 * 403 Forbidden - 禁止访问
 *
 * <p>当服务器理解请求但拒绝授权时，将抛出此异常。这通常意味着用户没有权限访问所请求的资源。与 401 Unauthorized 不同，403 表示用户身份是已知的，但服务器拒绝其访问。
 * 在可能的情况下，应优先使用更具体的异常类型，例如 {@link
 * com.xinjian.common.exception.status403.InsufficientPermissionsException}（如果存在）。
 */
public class ForbiddenException extends AbstractThrowableProblem {

  public ForbiddenException(String message) {
    super(ErrorConstants.FORBIDDEN, "Forbidden", Status.FORBIDDEN, message);
  }
}
