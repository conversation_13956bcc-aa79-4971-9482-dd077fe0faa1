package com.xinjian.common.exception.status400;

import com.xinjian.common.exception.ErrorConstants;
import org.zalando.problem.AbstractThrowableProblem;
import org.zalando.problem.Status;

/**
 * 400 Bad Request - 微信账号未绑定
 *
 * <p>当用户尝试执行一个需要绑定微信账号才能进行的操作，但其当前账户并未绑定任何微信账号时，应抛出此异常。这通常用于需要微信授权登录或微信支付等场景。
 */
public class WeChatAccountNotLinkedException extends AbstractThrowableProblem {
  public WeChatAccountNotLinkedException(final String message) {
    super(ErrorConstants.WECHAT_ACCOUNT_NOT_LINKED, "Bad Request", Status.BAD_REQUEST, message);
  }
}
