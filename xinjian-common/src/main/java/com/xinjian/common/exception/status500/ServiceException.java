package com.xinjian.common.exception.status500;

import com.xinjian.common.exception.ErrorConstants;
import org.zalando.problem.AbstractThrowableProblem;
import org.zalando.problem.Status;

/**
 * 500 Internal Server Error - 服务器内部错误
 *
 * <p>当服务器遇到无法处理的情况时，将抛出此异常。这是一个通用的服务器内部错误异常。适用于服务层业务逻辑处理失败的情况，如数据库操作失败、业务规则处理失败等。
 */
public class ServiceException extends AbstractThrowableProblem {

  public ServiceException(String message) {
    super(
        ErrorConstants.SERVICE_ERROR,
        "Internal Server Error",
        Status.INTERNAL_SERVER_ERROR,
        message);
  }
}
