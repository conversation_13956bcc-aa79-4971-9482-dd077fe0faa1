package com.xinjian.common.exception.status409;

import com.xinjian.common.exception.ErrorConstants;
import org.zalando.problem.AbstractThrowableProblem;
import org.zalando.problem.Status;

/**
 * 409 Conflict - 手机号码已被注册
 *
 * <p>当用户尝试注册或更新账户信息时，如果提供的手机号码已被系统中其他用户账户绑定，应抛出此异常，以确保手机号码的唯一性。
 */
public class MobileAlreadyRegisteredException extends AbstractThrowableProblem {
  public MobileAlreadyRegisteredException(final String message) {
    super(ErrorConstants.PHONE_NUMBER_ALREADY_REGISTERED, "Conflict", Status.CONFLICT, message);
  }
}
