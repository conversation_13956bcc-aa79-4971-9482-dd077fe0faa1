package com.xinjian.common.exception.status422;

import static com.xinjian.common.exception.ErrorConstants.BUSINESS_RULE_VIOLATION;

import org.zalando.problem.AbstractThrowableProblem;
import org.zalando.problem.Status;

/**
 * 422 Unprocessable Entity - 业务规则违反
 *
 * <p>当请求本身格式正确，且包含所有必需的字段，但由于业务逻辑的限制，服务器无法处理该请求时，应抛出此异常。例如，尝试删除一个仍有关联数据的实体，或在非工作时间执行某些操作。
 */
public class BusinessRuleViolationException extends AbstractThrowableProblem {

  public BusinessRuleViolationException(String message) {
    super(BUSINESS_RULE_VIOLATION, "Business Rule Violation", Status.UNPROCESSABLE_ENTITY, message);
  }
}
