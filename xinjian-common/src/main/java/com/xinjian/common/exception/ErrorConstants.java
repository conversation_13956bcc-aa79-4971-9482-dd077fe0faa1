package com.xinjian.common.exception;

import java.net.URI;
import lombok.experimental.UtilityClass;

/**
 * 精确的错误类型定义，遵循 RFC 7807 Problem Details 标准。每个错误类型都对应一个具体的业务场景，而不是通用的 HTTP 状态码。
 * 每个自定义异常类都应在此处拥有一个独一无二的错误类型常量。
 */
@UtilityClass
public class ErrorConstants {

  private static final String BASE_URL = "urn:error:";

  // 400 Bad Request
  public static final URI BAD_REQUEST = URI.create(BASE_URL + "bad-request");
  public static final URI INVALID_INPUT_DATA = URI.create(BASE_URL + "invalid-input-data");
  public static final URI PASSWORD_TOO_WEAK = URI.create(BASE_URL + "password-too-weak");
  public static final URI WECHAT_ACCOUNT_NOT_LINKED =
      URI.create(BASE_URL + "wechat-account-not-linked");

  // 403 Forbidden
  public static final URI FORBIDDEN = URI.create(BASE_URL + "forbidden");
  public static final URI INSUFFICIENT_PERMISSIONS =
      URI.create(BASE_URL + "insufficient-permissions");

  // 404 Not Found
  public static final URI NOT_FOUND = URI.create(BASE_URL + "not-found");
  public static final URI RESOURCE_NOT_FOUND = URI.create(BASE_URL + "resource-not-found");
  public static final URI USER_NOT_FOUND = URI.create(BASE_URL + "user-not-found");

  // 409 Conflict
  public static final URI CONFLICT = URI.create(BASE_URL + "conflict");
  public static final URI USERNAME_ALREADY_EXISTS =
      URI.create(BASE_URL + "username-already-exists");
  public static final URI EMAIL_ALREADY_IN_USE = URI.create(BASE_URL + "email-already-in-use");
  public static final URI PHONE_NUMBER_ALREADY_REGISTERED =
      URI.create(BASE_URL + "mobile-number-already-registered");
  public static final URI LOGIN_ALREADY_USED = URI.create(BASE_URL + "login-already-used");
  public static final URI CONCURRENCY_FAILURE = URI.create(BASE_URL + "concurrency-failure");

  // 413 Payload Too Large
  public static final URI PAYLOAD_TOO_LARGE = URI.create(BASE_URL + "payload-too-large");
  public static final URI FILE_UPLOAD_TOO_LARGE = URI.create(BASE_URL + "file-upload-too-large");
  public static final URI IMAGE_SIZE_TOO_LARGE = URI.create(BASE_URL + "image-size-too-large");

  // 422 Unprocessable Entity
  public static final URI UNPROCESSABLE_ENTITY = URI.create(BASE_URL + "unprocessable-entity");
  public static final URI VALIDATION_FAILED = URI.create(BASE_URL + "validation-failed");
  public static final URI BUSINESS_RULE_VIOLATION =
      URI.create(BASE_URL + "business-rule-violation");

  // 429 Too Many Requests
  public static final URI TOO_MANY_REQUESTS = URI.create(BASE_URL + "too-many-requests");
  public static final URI RATE_LIMIT_EXCEEDED = URI.create(BASE_URL + "rate-limit-exceeded");
  public static final URI PASSWORD_ATTEMPTS_EXCEEDED =
      URI.create(BASE_URL + "password-attempts-exceeded");

  // 500 Internal Server Error
  public static final URI SERVICE_ERROR = URI.create(BASE_URL + "service-error");
}
