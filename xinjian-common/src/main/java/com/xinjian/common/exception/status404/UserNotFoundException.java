package com.xinjian.common.exception.status404;

import com.xinjian.common.exception.ErrorConstants;
import org.zalando.problem.AbstractThrowableProblem;
import org.zalando.problem.Status;

/**
 * 404 Not Found - 用户未找到
 *
 * <p>当系统根据提供的标识符（如用户名、ID、邮箱等）无法找到对应的用户记录时，应抛出此异常。这通常发生在用户登录、查询用户信息或对不存在的用户进行操作时。
 */
public class UserNotFoundException extends AbstractThrowableProblem {
  public UserNotFoundException(final String message) {
    super(ErrorConstants.USER_NOT_FOUND, "Not Found", Status.NOT_FOUND, message);
  }
}
