package com.xinjian.common.exception.status429;

import com.xinjian.common.exception.ErrorConstants;
import org.zalando.problem.AbstractThrowableProblem;
import org.zalando.problem.Status;

/**
 * 429 Too Many Requests - 请求过于频繁
 *
 * <p>当用户在给定的时间内发送了太多请求，超出了速率限制策略时，将抛出此异常。这是一个通用的速率限制异常。在可能的情况下，应优先使用更具体的异常类型，例如 {@link
 * PasswordAttemptsExceededException}。
 */
public class TooManyRequestsException extends AbstractThrowableProblem {

  public TooManyRequestsException(String message) {
    super(ErrorConstants.TOO_MANY_REQUESTS, "Too Many Requests", Status.TOO_MANY_REQUESTS, message);
  }
}
