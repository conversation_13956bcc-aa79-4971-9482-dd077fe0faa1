package com.xinjian.common.exception.status409;

import com.xinjian.common.exception.ErrorConstants;
import org.zalando.problem.AbstractThrowableProblem;
import org.zalando.problem.Status;

/**
 * 409 Conflict - 登录名已被使用
 *
 * <p>当用户尝试注册新账户或更改现有账户的登录名时，如果所选登录名已被系统中其他用户占用，应抛出此异常，以确保登录名的唯一性。
 */
public class LoginAlreadyUsedException extends AbstractThrowableProblem {
  public LoginAlreadyUsedException(final String message) {
    super(ErrorConstants.LOGIN_ALREADY_USED, "Conflict", Status.CONFLICT, message);
  }
}
