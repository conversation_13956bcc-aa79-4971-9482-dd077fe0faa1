package com.xinjian.common.xss;

import java.util.regex.Matcher;
import java.util.regex.Pattern;
import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;
import org.apache.commons.lang3.StringUtils;

/** 自定义 xss 校验注解实现 */
public class XssValidator implements ConstraintValidator<Xss, String> {
  private static final String HTML_PATTERN = "<(\\S*?)[^>]*>.*?|<.*? />";

  @Override
  public boolean isValid(String value, ConstraintValidatorContext constraintValidatorContext) {
    if (StringUtils.isBlank(value)) {
      return true;
    }
    return !containsHtml(value);
  }

  public static boolean containsHtml(String value) {
    StringBuilder sHtml = new StringBuilder();
    Pattern pattern = Pattern.compile(HTML_PATTERN);
    Matcher matcher = pattern.matcher(value);
    while (matcher.find()) {
      sHtml.append(matcher.group());
    }
    return pattern.matcher(sHtml).matches();
  }
}
