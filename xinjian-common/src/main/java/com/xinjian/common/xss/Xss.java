package com.xinjian.common.xss;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;
import javax.validation.Constraint;
import javax.validation.Payload;

/** 自定义 xss 校验注解 */
@Retention(RetentionPolicy.RUNTIME)
@Target(
    value = {ElementType.METHOD, ElementType.FIELD, ElementType.CONSTRUCTOR, ElementType.PARAMETER})
@Constraint(validatedBy = {XssValidator.class})
public @interface Xss {
  String message() default "不允许任何脚本运行";

  Class<?>[] groups() default {};

  Class<? extends Payload>[] payload() default {};
}
