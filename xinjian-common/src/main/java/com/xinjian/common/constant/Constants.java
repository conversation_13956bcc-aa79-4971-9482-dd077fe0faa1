package com.xinjian.common.constant;

/** 核心安全常量信息 */
public class Constants {

  /** 所有权限标识 */
  public static final String ALL_PERMISSION = "*:*:*";

  /** 管理员角色权限标识 */
  public static final String SUPER_ADMIN = "ADMIN";

  /** RMI 远程方法调用 */
  public static final String LOOKUP_RMI = "rmi:";

  /** LDAP 远程方法调用 */
  public static final String LOOKUP_LDAP = "ldap:";

  /** LDAPS 远程方法调用 */
  public static final String LOOKUP_LDAPS = "ldaps:";

  /** 自动识别 json 对象白名单配置（仅允许解析的包名，范围越小越安全） */
  public static final String[] JSON_WHITELIST_STR = {"org.springframework", "com.xinjian"};

  /** 定时任务白名单配置（仅允许访问的包名，如其他需要可以自行添加） */
  public static final String[] JOB_WHITELIST_STR = {"com.xinjian.quartz.task"};

  /** 定时任务违规的字符 */
  public static final String[] JOB_ERROR_STR = {
    "java.net.URL",
    "javax.naming.InitialContext",
    "org.yaml.snakeyaml",
    "org.springframework",
    "org.apache",
    "com.xinjian.common.utils.file",
    "com.xinjian.common.config",
    "com.xinjian.generator"
  };
}
