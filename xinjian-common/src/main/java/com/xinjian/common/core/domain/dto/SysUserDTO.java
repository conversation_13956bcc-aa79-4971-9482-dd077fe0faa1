package com.xinjian.common.core.domain.dto;

import com.xinjian.common.core.domain.BaseDTO;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;

/** 用户信息 DTO 对象 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SysUserDTO extends BaseDTO implements Serializable {
  private static final long serialVersionUID = 1L;

  /** 用户 ID */
  private Long userId;

  /** 用户账号 */
  private String userName;

  /** 用户昵称 */
  private String nickName;

  /** 用户类型（0 系统用户 1 第三方用户 2 其他用户） */
  private Integer userType;

  /** 用户邮箱 */
  private String email;

  /** 手机号码 */
  private String mobile;

  /** 用户性别（1 男 2 女 3 未知） */
  private Integer sex;

  /** 用户头像 */
  private String avatar;

  /** 帐号状态（true 正常 false 停用） */
  private Boolean status;

  /** 最后登录 IP */
  private String loginIp;

  /** 最后登录时间 */
  private LocalDateTime loginTime;

  /** 部门对象 */
  private SysDeptDTO dept;

  /** 角色对象列表 */
  private List<SysRoleDTO> roles;
}
