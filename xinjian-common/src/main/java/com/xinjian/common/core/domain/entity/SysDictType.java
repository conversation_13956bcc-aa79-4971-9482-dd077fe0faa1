package com.xinjian.common.core.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.xinjian.common.annotation.Excel;
import com.xinjian.common.annotation.Excel.ColumnType;
import com.xinjian.common.core.domain.BaseEntity;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
/** 字典类型表 sys_dict_type */
public class SysDictType extends BaseEntity {

  /** 字典主键 */
  @Excel(name = "字典主键", cellType = ColumnType.NUMERIC)
  @TableId(type = IdType.AUTO)
  private Long dictId;

  /** 字典名称 */
  @Excel(name = "字典名称")
  @NotBlank(message = "字典名称不能为空")
  @Size(min = 0, max = 100, message = "字典类型名称长度不能超过 100 个字符")
  private String dictName;

  /** 字典类型 */
  @Excel(name = "字典类型")
  @NotBlank(message = "字典类型不能为空")
  @Size(min = 0, max = 100, message = "字典类型类型长度不能超过 100 个字符")
  @Pattern(regexp = "^[a-z][a-z0-9_]*$", message = "字典类型必须以字母开头，且只能为（小写字母，数字，下滑线）")
  private String dictType;

  /** 状态（true 正常 false 停用） */
  @Excel(name = "状态", readConverterExp = "true=正常，false=停用")
  private Boolean status;
}
