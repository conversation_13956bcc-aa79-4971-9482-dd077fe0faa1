package com.xinjian.common.core.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Data;

/** DTO 基类，只包含需要对外暴露的公共审计字段 */
@Data
public class BaseDTO implements Serializable {
  private static final long serialVersionUID = 1L;

  private Long createBy;

  @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
  private LocalDateTime createTime;

  private Long updateBy;

  @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
  private LocalDateTime updateTime;
}
