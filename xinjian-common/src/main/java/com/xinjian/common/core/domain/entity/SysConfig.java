package com.xinjian.common.core.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.xinjian.common.annotation.Excel;
import com.xinjian.common.annotation.Excel.ColumnType;
import com.xinjian.common.core.domain.BaseEntity;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
/** 参数配置表 sys_config */
public class SysConfig extends BaseEntity {

  /** 参数主键 */
  @Excel(name = "参数主键", cellType = ColumnType.NUMERIC)
  @TableId(type = IdType.AUTO)
  private Long configId;

  /** 参数名称 */
  @Excel(name = "参数名称")
  @NotBlank(message = "参数名称不能为空")
  @Size(min = 0, max = 100, message = "参数名称不能超过 100 个字符")
  private String configName;

  /** 参数键名 */
  @Excel(name = "参数键名")
  @NotBlank(message = "参数键名长度不能为空")
  @Size(min = 0, max = 100, message = "参数键名长度不能超过 100 个字符")
  private String configKey;

  /** 参数键值 */
  @Excel(name = "参数键值")
  @NotBlank(message = "参数键值不能为空")
  @Size(min = 0, max = 500, message = "参数键值长度不能超过 500 个字符")
  private String configValue;

  /** 系统内置（Y 是 N 否） */
  @Excel(name = "系统内置", readConverterExp = "Y=是，N=否")
  private String configType;
}
