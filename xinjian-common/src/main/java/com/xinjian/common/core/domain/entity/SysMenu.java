package com.xinjian.common.core.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.xinjian.common.core.domain.BaseEntity;
import java.util.ArrayList;
import java.util.List;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
/** 菜单权限表 sys_menu */
public class SysMenu extends BaseEntity {
  private static final long serialVersionUID = 1L;

  /** 菜单 ID */
  @TableId(type = IdType.AUTO)
  private Long menuId;

  /** 菜单名称 */
  @NotBlank(message = "菜单名称不能为空")
  @Size(min = 0, max = 50, message = "菜单名称长度不能超过 50 个字符")
  private String menuName;

  /** 父菜单名称 */
  private String parentName;

  /** 父菜单 ID */
  private Long parentId;

  /** 显示顺序 */
  @NotNull(message = "显示顺序不能为空")
  private Integer orderNum;

  /** 路由地址 */
  @Size(min = 0, max = 200, message = "路由地址不能超过 200 个字符")
  private String path;

  /** 组件路径 */
  @Size(min = 0, max = 255, message = "组件路径不能超过 255 个字符")
  private String component;

  /** 路由参数 */
  private String query;

  /** 是否为外链（true 是外链 false 不是外链） */
  private Boolean isFrame;

  /** 是否缓存（true 缓存 false 不缓存） */
  private Boolean isCache;

  /** 类型（M 目录 C 菜单 F 按钮） */
  @NotBlank(message = "菜单类型不能为空")
  private String menuType;

  /** 显示状态（true 显示 false 隐藏） */
  private Boolean visible;

  /** 菜单状态（true 正常 false 停用） */
  private Boolean status;

  /** 权限字符串 */
  @Size(min = 0, max = 100, message = "权限标识长度不能超过 100 个字符")
  private String perms;

  /** 菜单图标 */
  private String icon;

  /** 子菜单 */
  private List<SysMenu> children = new ArrayList<SysMenu>();
}
