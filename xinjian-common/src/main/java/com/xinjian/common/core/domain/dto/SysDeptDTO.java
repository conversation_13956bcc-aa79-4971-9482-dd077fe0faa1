package com.xinjian.common.core.domain.dto;

import com.xinjian.common.core.domain.BaseDTO;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;

/** 部门 DTO 对象 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SysDeptDTO extends BaseDTO implements Serializable {
  private static final long serialVersionUID = 1L;

  /** 部门 ID */
  private Long deptId;

  /** 父部门 ID */
  private Long parentId;

  /** 祖级列表 */
  private String ancestors;

  /** 部门名称 */
  private String deptName;

  /** 显示顺序 */
  private Integer orderNum;

  /** 负责人 ID */
  private Long leader;

  /** 联系电话 */
  private String mobile;

  /** 邮箱 */
  private String email;

  /** 部门状态（true 正常 false 停用） */
  private Boolean status;

  /** 父部门名称 */
  private String parentName;
}
