package com.xinjian.common.core.domain;

import java.util.ArrayList;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
/** Tree 基类 */
public class TreeEntity extends BaseEntity {
  private static final long serialVersionUID = 1L;

  /** 父菜单名称 */
  private String parentName;

  /** 父菜单 ID */
  private Long parentId;

  /** 显示顺序 */
  private Integer orderNum;

  /** 祖级列表 */
  private String ancestors;

  /** 子部门 */
  private List<?> children = new ArrayList<>();
}
