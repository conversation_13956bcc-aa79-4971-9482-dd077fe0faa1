package com.xinjian.common.core.domain.query;

import com.xinjian.common.core.domain.BaseQuery;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class SysPostQuery extends BaseQuery<SysPostQuery> {

  // 业务查询字段
  private String postCode;
  private String postName;
  private Boolean status;

  @Override
  public SysPostQuery getQueryParams() {
    return this;
  }
}
