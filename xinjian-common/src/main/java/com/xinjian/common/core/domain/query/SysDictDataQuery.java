package com.xinjian.common.core.domain.query;

import com.xinjian.common.core.domain.BaseQuery;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class SysDictDataQuery extends BaseQuery<SysDictDataQuery> {

  // 业务查询字段
  private String dictType;
  private String dictLabel;
  private String dictValue;
  private Boolean status;

  @Override
  public SysDictDataQuery getQueryParams() {
    return this;
  }
}
