package com.xinjian.common.core.domain.query;

import com.xinjian.common.core.domain.BaseQuery;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class SysLogininforQuery extends BaseQuery<SysLogininforQuery> {

  private String userName;
  private Boolean status;
  private String ipaddr;

  @Override
  public SysLogininforQuery getQueryParams() {
    return this;
  }
}
