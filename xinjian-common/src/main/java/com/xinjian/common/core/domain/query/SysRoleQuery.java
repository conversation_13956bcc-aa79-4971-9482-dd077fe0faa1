package com.xinjian.common.core.domain.query;

import com.xinjian.common.core.domain.BaseQuery;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class SysRoleQuery extends BaseQuery<SysRoleQuery> {

  // 业务查询字段
  private Long roleId;
  private String roleName;
  private String roleKey;
  private Boolean status;
  private Long userId;

  @Override
  public SysRoleQuery getQueryParams() {
    return this;
  }
}
