package com.xinjian.common.core.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.xinjian.common.annotation.Excel;
import com.xinjian.common.annotation.Excel.ColumnType;
import com.xinjian.common.core.domain.BaseEntity;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
/** 岗位表 sys_post */
public class SysPost extends BaseEntity {

  /** 岗位序号 */
  @Excel(name = "岗位序号", cellType = ColumnType.NUMERIC)
  @TableId(type = IdType.AUTO)
  private Long postId;

  /** 岗位编码 */
  @Excel(name = "岗位编码")
  @NotBlank(message = "岗位编码不能为空")
  @Size(min = 0, max = 64, message = "岗位编码长度不能超过 64 个字符")
  private String postCode;

  /** 岗位名称 */
  @Excel(name = "岗位名称")
  @NotBlank(message = "岗位名称不能为空")
  @Size(min = 0, max = 50, message = "岗位名称长度不能超过 50 个字符")
  private String postName;

  /** 岗位排序 */
  @Excel(name = "岗位排序")
  @NotNull(message = "显示顺序不能为空")
  private Integer postSort;

  /** 状态（true 正常 false 停用） */
  @Excel(name = "状态", readConverterExp = "true=正常，false=停用")
  private Boolean status;

  /** 用户是否存在此岗位标识 默认不存在 */
  private boolean flag = false;
}
