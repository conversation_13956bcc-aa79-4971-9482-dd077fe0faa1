package com.xinjian.common.core.domain.query;

import com.xinjian.common.core.domain.BaseQuery;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class SysDictTypeQuery extends BaseQuery<SysDictTypeQuery> {

  // 业务查询字段
  private Long dictId;
  private String dictName;
  private String dictType;
  private Boolean status;

  @Override
  public SysDictTypeQuery getQueryParams() {
    return this;
  }
}
