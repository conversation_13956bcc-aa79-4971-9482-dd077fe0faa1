package com.xinjian.common.core.domain.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import java.util.Collection;
import java.util.Collections;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.userdetails.UserDetails;

/**
 * 登录用户身份权限
 *
 * <p>只包含认证和授权所需的最少信息
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class LoginUser implements UserDetails {

  /** 用户 ID */
  private Long userId;

  /** 部门 ID */
  private Long deptId;

  /** 用户名 */
  private String username;

  /** 密码 */
  private String password;

  /** 账户是否启用 */
  private boolean enabled;

  /** 权限集合 */
  private Set<String> authoritiesSet;

  /**
   * 获取用户的权限集合
   *
   * @return GrantedAuthority 集合
   */
  @Override
  @JsonIgnore
  public Collection<? extends GrantedAuthority> getAuthorities() {
    // 权限集合为空时返回空集合
    if (authoritiesSet == null || authoritiesSet.isEmpty()) {
      return Collections.emptySet();
    }

    // 将权限字符串转换为 GrantedAuthority 对象
    return authoritiesSet.stream().map(SimpleGrantedAuthority::new).collect(Collectors.toSet());
  }

  /**
   * 获取用户密码
   *
   * @return 用户密码
   */
  @Override
  public String getPassword() {
    return password;
  }

  /**
   * 获取用户名
   *
   * @return 用户名
   */
  @Override
  public String getUsername() {
    return username;
  }

  /**
   * 账户是否未过期
   *
   * @return true 表示账户未过期
   */
  @Override
  public boolean isAccountNonExpired() {
    return true;
  }

  /**
   * 账户是否未锁定
   *
   * @return true 表示账户未锁定
   */
  @Override
  public boolean isAccountNonLocked() {
    return true;
  }

  /**
   * 凭证是否未过期
   *
   * @return true 表示凭证未过期
   */
  @Override
  public boolean isCredentialsNonExpired() {
    return true;
  }

  /**
   * 账户是否启用
   *
   * @return true 表示账户启用
   */
  @Override
  public boolean isEnabled() {
    return enabled;
  }
}
