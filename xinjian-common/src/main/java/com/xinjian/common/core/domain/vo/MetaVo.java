package com.xinjian.common.core.domain.vo;

import lombok.Data;
import org.apache.commons.lang3.StringUtils;

/** 路由显示信息 */
@Data
public class MetaVo {
  /** 设置该路由在侧边栏和面包屑中展示的名字 */
  private String title;

  /** 设置该路由的图标，对应路径 src/assets/icons/svg */
  private String icon;

  /** 设置为 true，则不会被 <keep-alive>缓存 */
  private boolean noCache;

  /** 内链地址（http(s)://开头） */
  private String link;

  public MetaVo() {}

  public MetaVo(String title, String icon) {
    this.title = title;
    this.icon = icon;
  }

  public MetaVo(String title, String icon, boolean noCache) {
    this.title = title;
    this.icon = icon;
    this.noCache = noCache;
  }

  public MetaVo(String title, String icon, String link) {
    this.title = title;
    this.icon = icon;
    this.link = link;
  }

  public MetaVo(String title, String icon, boolean noCache, String link) {
    this.title = title;
    this.icon = icon;
    this.noCache = noCache;
    if (StringUtils.startsWithAny(link, "http://", "https://")) {
      this.link = link;
    }
  }
}
