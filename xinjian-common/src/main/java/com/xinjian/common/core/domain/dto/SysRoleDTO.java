package com.xinjian.common.core.domain.dto;

import com.xinjian.common.core.domain.BaseDTO;
import java.io.Serializable;
import java.util.Set;
import lombok.Data;
import lombok.EqualsAndHashCode;

/** 角色 DTO 对象 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SysRoleDTO extends BaseDTO implements Serializable {
  private static final long serialVersionUID = 1L;

  /** 角色 ID */
  private Long roleId;

  /** 角色名称 */
  private String roleName;

  /** 角色权限 */
  private String roleKey;

  /** 角色排序 */
  private Integer roleSort;

  /** 数据范围（1：所有数据权限；2：自定义数据权限；3：本部门数据权限；4：本部门及以下数据权限；5：仅本人数据权限） */
  private String dataScope;

  /** 菜单树选择项是否关联显示（false：父子不互相关联显示 true：父子互相关联显示） */
  private boolean menuCheckStrictly;

  /** 部门树选择项是否关联显示（false：父子不互相关联显示 true：父子互相关联显示） */
  private boolean deptCheckStrictly;

  /** 角色状态（true 正常 false 停用） */
  private Boolean status;

  /** 角色菜单权限 */
  private Set<String> permissions;
}
