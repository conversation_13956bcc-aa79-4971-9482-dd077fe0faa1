package com.xinjian.common.core.domain.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.xinjian.common.annotation.Excel;
import com.xinjian.common.annotation.Excel.ColumnType;
import com.xinjian.common.core.domain.BaseEntity;
import java.util.Set;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import lombok.Data;
import lombok.EqualsAndHashCode;

/** 角色表 sys_role */
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@EqualsAndHashCode(callSuper = true)
public class SysRole extends BaseEntity {
  private static final long serialVersionUID = 1L;

  /** 角色 ID */
  @Excel(name = "角色序号", cellType = ColumnType.NUMERIC)
  @TableId(type = IdType.AUTO)
  private Long roleId;

  /** 角色名称 */
  @Excel(name = "角色名称")
  @NotBlank(message = "角色名称不能为空")
  @Size(min = 0, max = 30, message = "角色名称长度不能超过 30 个字符")
  private String roleName;

  /** 角色权限 */
  @Excel(name = "角色权限")
  @NotBlank(message = "权限字符不能为空")
  @Size(min = 0, max = 100, message = "权限字符长度不能超过 100 个字符")
  private String roleKey;

  /** 角色排序 */
  @Excel(name = "角色排序")
  @NotNull(message = "显示顺序不能为空")
  private Integer roleSort;

  /** 数据范围（1：所有数据权限；2：自定义数据权限；3：本部门数据权限；4：本部门及以下数据权限；5：仅本人数据权限） */
  @Excel(name = "数据范围", readConverterExp = "1=所有数据权限，2=自定义数据权限，3=本部门数据权限，4=本部门及以下数据权限，5=仅本人数据权限")
  private String dataScope;

  /** 菜单树选择项是否关联显示（0：父子不互相关联显示 1：父子互相关联显示） */
  private boolean menuCheckStrictly;

  /** 部门树选择项是否关联显示（0：父子不互相关联显示 1：父子互相关联显示） */
  private boolean deptCheckStrictly;

  /** 角色状态（true 正常 false 停用） */
  @Excel(name = "角色状态", readConverterExp = "true=正常，false=停用")
  private Boolean status;

  /** 删除标志（0 未删除 1 已删除） */
  @TableLogic
  @TableField(fill = FieldFill.INSERT)
  private Boolean isDeleted;

  /** 用户是否存在此角色标识 默认不存在 */
  private boolean flag = false;

  /** 菜单组 */
  private Long[] menuIds;

  /** 部门组（数据权限） */
  private Long[] deptIds;

  /** 角色菜单权限 */
  private Set<String> permissions;

  public SysRole() {}

  public SysRole(Long roleId) {
    this.roleId = roleId;
  }
}
