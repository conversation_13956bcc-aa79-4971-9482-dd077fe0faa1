package com.xinjian.common.core.domain.dto;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

/** 用户注册对象 */
public class RegisterRequest extends LoginRequest {

  @NotBlank(message = "用户名不能为空")
  @Size(min = 2, max = 20, message = "用户名长度必须在 2 到 20 个字符之间")
  @Override
  public String getUsername() {
    return super.getUsername();
  }

  @NotBlank(message = "密码不能为空")
  @Size(min = 5, max = 20, message = "密码长度必须在 5 到 20 个字符之间")
  @Override
  public String getPassword() {
    return super.getPassword();
  }
}
