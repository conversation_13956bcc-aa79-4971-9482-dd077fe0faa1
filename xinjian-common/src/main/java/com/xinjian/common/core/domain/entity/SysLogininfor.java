package com.xinjian.common.core.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.xinjian.common.annotation.Excel;
import com.xinjian.common.annotation.Excel.ColumnType;
import com.xinjian.common.core.domain.BaseEntity;
import java.time.LocalDateTime;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
/** 系统访问记录表 sys_logininfor */
public class SysLogininfor extends BaseEntity {

  /** ID */
  @Excel(name = "序号", cellType = ColumnType.NUMERIC)
  @TableId(type = IdType.AUTO)
  private Long infoId;

  /** 用户账号 */
  @Excel(name = "用户账号")
  private String userName;

  /** 登录状态 true 成功 false 失败 */
  @Excel(name = "登录状态", readConverterExp = "true=成功，false=失败")
  private Boolean status;

  /** 登录 IP 地址 */
  @Excel(name = "登录地址")
  private String ipaddr;

  /** 登录地点 */
  @Excel(name = "登录地点")
  private String loginLocation;

  /** 浏览器类型 */
  @Excel(name = "浏览器")
  private String browser;

  /** 操作系统 */
  @Excel(name = "操作系统")
  private String os;

  /** 提示消息 */
  @Excel(name = "提示消息")
  private String msg;

  /** 访问时间 */
  @Excel(name = "访问时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
  @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
  private LocalDateTime loginTime;
}
