package com.xinjian.common.core.domain.query;

import com.xinjian.common.core.domain.BaseQuery;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class SysUserQuery extends BaseQuery<SysUserQuery> {

  // 业务查询字段
  private Long userId;
  private String userName;
  private Boolean status;
  private String mobile;
  private Long deptId;
  private Long roleId;

  @Override
  public SysUserQuery getQueryParams() {
    return this;
  }
}
