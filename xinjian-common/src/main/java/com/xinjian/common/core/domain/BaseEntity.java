package com.xinjian.common.core.domain;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Data;

/** Entity 基类 */
@Data
public class BaseEntity implements Serializable {
  private static final long serialVersionUID = 1L;

  /** 创建者 ID */
  @TableField(fill = FieldFill.INSERT)
  private Long createBy;

  /** 创建时间 */
  @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
  @TableField(fill = FieldFill.INSERT)
  private LocalDateTime createTime;

  /** 更新者 ID */
  @TableField(fill = FieldFill.INSERT_UPDATE)
  private Long updateBy;

  /** 更新时间 */
  @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
  @TableField(fill = FieldFill.INSERT_UPDATE)
  private LocalDateTime updateTime;

  /** 备注 */
  private String remark;

  /** 搜索值（非数据库字段） */
  @TableField(exist = false)
  @JsonIgnore
  private String searchValue;
}
