package com.xinjian.common.core.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.time.LocalDateTime;
import lombok.Data;

/** 当前在线会话 */
@Data
public class SysUserOnline {
  /** 会话编号 */
  @TableId(type = IdType.AUTO)
  private String tokenId;

  /** 部门名称 */
  private String deptName;

  /** 用户名称 */
  private String userName;

  /** 登录 IP 地址 */
  private String ipaddr;

  /** 登录地址 */
  private String loginLocation;

  /** 浏览器类型 */
  private String browser;

  /** 操作系统 */
  private String os;

  /** 登录时间 */
  @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
  private LocalDateTime loginTime;
}
