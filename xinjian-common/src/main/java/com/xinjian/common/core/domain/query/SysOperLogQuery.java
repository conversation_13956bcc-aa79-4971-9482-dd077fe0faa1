package com.xinjian.common.core.domain.query;

import com.xinjian.common.core.domain.BaseQuery;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class SysOperLogQuery extends BaseQuery<SysOperLogQuery> {

  // 业务查询字段
  private String title;
  private String operName;
  private Integer businessType;
  private Integer[] businessTypes;
  private Boolean status;
  private String operIp;

  @Override
  public SysOperLogQuery getQueryParams() {
    return this;
  }
}
