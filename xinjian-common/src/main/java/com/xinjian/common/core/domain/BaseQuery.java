package com.xinjian.common.core.domain;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.xinjian.common.properties.PaginationProperties;
import com.xinjian.common.utils.sql.SqlUtil;
import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * 分页查询基类
 *
 * <p>统一处理分页和排序参数，提供智能的 buildPage 方法 实现了配置驱动的分页策略和自动化的排序处理
 *
 * @param <T> 查询参数类型
 */
@Data
public abstract class BaseQuery<T> implements Serializable {

  private static final long serialVersionUID = 1L;

  // 分页参数
  private Integer pageNum;
  private Integer pageSize;

  // 排序参数
  private String orderByColumn;
  private String isAsc;

  /**
   * 将查询参数转换为 MyBatis Plus 的 IPage 对象
   *
   * <p>这是分页方案的核心方法，它：
   *
   * <ul>
   *   <li>自动处理分页参数的默认值和验证
   *   <li>应用配置化的分页策略
   *   <li>自动处理排序参数的 SQL 注入防护
   *   <li>提供最大分页大小的安全限制
   * </ul>
   *
   * @param properties 分页策略配置
   * @param <E> 实体类型
   * @return 配置完善的 IPage 对象
   */
  @JsonIgnore // 确保这个方法不会被序列化
  public <E> IPage<E> buildPage(PaginationProperties properties) {
    // 处理页码默认值
    Integer finalPageNum =
        (this.pageNum == null || this.pageNum <= 0) ? properties.getDefaultPageNum() : this.pageNum;

    // 处理每页数量默认值和安全限制
    Integer finalPageSize =
        (this.pageSize == null || this.pageSize <= 0)
            ? properties.getDefaultPageSize()
            : this.pageSize;

    // 应用最大分页大小的安全限制
    if (finalPageSize > properties.getMaxPageSize()) {
      finalPageSize = properties.getMaxPageSize();
    }

    // 创建分页对象
    IPage<E> page = new Page<>(finalPageNum, finalPageSize);

    // 处理排序
    if (orderByColumn != null && !orderByColumn.trim().isEmpty()) {
      String orderBySql = SqlUtil.escapeOrderBySql(orderByColumn);
      OrderItem orderItem =
          "ascending".equalsIgnoreCase(this.isAsc) || "asc".equalsIgnoreCase(this.isAsc)
              ? OrderItem.asc(orderBySql)
              : OrderItem.desc(orderBySql);
      page.orders().add(orderItem);
    }

    return page;
  }

  /**
   * 获取页码（保持向后兼容）
   *
   * @deprecated 建议使用 buildPage() 方法
   */
  public Integer getPageNum() {
    return pageNum;
  }

  /**
   * 获取每页大小（保持向后兼容）
   *
   * @deprecated 建议使用 buildPage() 方法
   */
  public Integer getPageSize() {
    return pageSize;
  }

  /**
   * 是否需要分页（保持向后兼容）
   *
   * @deprecated 建议使用 buildPage() 方法
   */
  public boolean isPaged() {
    return pageNum != null && pageNum > 0 && pageSize != null && pageSize > 0;
  }

  /**
   * 是否需要排序（保持向后兼容）
   *
   * @deprecated 建议使用 buildPage() 方法
   */
  @Deprecated
  public boolean isSorted() {
    return orderByColumn != null && !orderByColumn.trim().isEmpty();
  }

  /** 获取查询参数对象（由子类实现） */
  public abstract T getQueryParams();

  // --- 用于【按天】范围查询 ---（还未实现）
  @DateTimeFormat(pattern = "yyyy-MM-dd")
  private LocalDate beginDate;

  @DateTimeFormat(pattern = "yyyy-MM-dd")
  private LocalDate endDate;

  // --- 用于【精确到时分秒】的范围查询 ---（目前采用）
  @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
  private LocalDateTime beginTime;

  @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
  private LocalDateTime endTime;
}
