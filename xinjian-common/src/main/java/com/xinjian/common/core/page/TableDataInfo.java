package com.xinjian.common.core.page;

import com.baomidou.mybatisplus.core.metadata.IPage;
import java.io.Serializable;
import java.util.List;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 表格分页数据对象 - 适配 MyBatis Plus
 *
 * @param <T> 单行数据实体对象
 */
@Data
@NoArgsConstructor
public class TableDataInfo<T> implements Serializable {
  private static final long serialVersionUID = 1L;
  private List<T> rows;
  private long total;
  private int pageNum;
  private int pageSize;

  public TableDataInfo(List<T> rows) {
    this.rows = rows;
    this.total = rows != null ? rows.size() : 0;
    this.pageNum = 1;
    this.pageSize = rows != null ? rows.size() : 0;
  }

  public TableDataInfo(IPage<T> page) {
    this.rows = page.getRecords();
    this.total = page.getTotal();
    this.pageNum = (int) page.getCurrent();
    this.pageSize = (int) page.getSize();
  }

  /**
   * 封装非分页列表
   *
   * @param list 列表
   * @param <T> 泛型
   * @return 分页数据
   */
  public static <T> TableDataInfo<T> of(List<T> list) {
    TableDataInfo<T> dataInfo = new TableDataInfo<>();
    dataInfo.setRows(list);
    if (list != null) {
      dataInfo.setTotal(list.size());
    } else {
      dataInfo.setTotal(0);
    }
    return dataInfo;
  }
}
