package com.xinjian.common.core.domain.query;

import com.xinjian.common.core.domain.BaseQuery;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class SysConfigQuery extends BaseQuery<SysConfigQuery> {

  // 业务查询字段
  private String configName;
  private String configKey;
  private String configType;

  @Override
  public SysConfigQuery getQueryParams() {
    return this;
  }
}
