package com.xinjian.common.core.domain.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.xinjian.common.core.domain.BaseEntity;
import java.util.ArrayList;
import java.util.List;
import javax.validation.constraints.Email;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
/** 部门表 sys_dept */
public class SysDept extends BaseEntity {

  /** 部门 ID */
  @TableId(type = IdType.AUTO)
  private Long deptId;

  /** 父部门 ID */
  private Long parentId;

  /** 祖级列表 */
  private String ancestors;

  /** 部门名称 */
  @NotBlank(message = "部门名称不能为空")
  @Size(min = 0, max = 30, message = "部门名称长度不能超过 30 个字符")
  private String deptName;

  /** 显示顺序 */
  @NotNull(message = "显示顺序不能为空")
  private Integer orderNum;

  /** 负责人 ID */
  private Long leader;

  /** 联系电话 */
  @Size(min = 0, max = 11, message = "联系电话长度不能超过 11 个字符")
  private String mobile;

  /** 邮箱 */
  @Email(message = "邮箱格式不正确")
  @Size(min = 0, max = 50, message = "邮箱长度不能超过 50 个字符")
  private String email;

  /** 部门状态（true 正常 false 停用） */
  private Boolean status;

  /** 删除标志（false 未删除 true 已删除） */
  @TableLogic
  @TableField(fill = FieldFill.INSERT)
  private Boolean isDeleted;

  /** 父部门名称 */
  private String parentName;

  /** 子部门 */
  private List<SysDept> children = new ArrayList<SysDept>();
}
