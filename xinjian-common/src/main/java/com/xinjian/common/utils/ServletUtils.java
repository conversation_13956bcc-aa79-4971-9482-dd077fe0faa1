package com.xinjian.common.utils;

import com.xinjian.common.core.text.Convert;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import javax.servlet.ServletRequest;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

/** 客户端工具类 */
public class ServletUtils {
  /** 获取 String 参数 */
  public static String getParameter(String name) {
    return getRequest().getParameter(name);
  }

  /** 获取 String 参数 */
  public static String getParameter(String name, String defaultValue) {
    return Convert.toStr(getRequest().getParameter(name), defaultValue);
  }

  /** 获取 Integer 参数 */
  public static Integer getParameterToInt(String name) {
    return Convert.toInt(getRequest().getParameter(name));
  }

  /** 获取 Integer 参数 */
  public static Integer getParameterToInt(String name, Integer defaultValue) {
    return Convert.toInt(getRequest().getParameter(name), defaultValue);
  }

  /** 获取 Boolean 参数 */
  public static Boolean getParameterToBool(String name) {
    return Convert.toBool(getRequest().getParameter(name));
  }

  /** 获取 Boolean 参数 */
  public static Boolean getParameterToBool(String name, Boolean defaultValue) {
    return Convert.toBool(getRequest().getParameter(name), defaultValue);
  }

  /**
   * 获得所有请求参数
   *
   * @param request 请求对象{@link ServletRequest}
   * @return Map
   */
  public static Map<String, String[]> getParams(ServletRequest request) {
    final Map<String, String[]> map = request.getParameterMap();
    return Collections.unmodifiableMap(map);
  }

  /**
   * 获得所有请求参数
   *
   * @param request 请求对象{@link ServletRequest}
   * @return Map
   */
  public static Map<String, String> getParamMap(ServletRequest request) {
    Map<String, String> params = new HashMap<>();
    for (Map.Entry<String, String[]> entry : getParams(request).entrySet()) {
      params.put(entry.getKey(), StringUtils.join(entry.getValue(), ","));
    }
    return params;
  }

  /** 获取 request */
  public static HttpServletRequest getRequest() {
    return getRequestAttributes().getRequest();
  }

  /**
   * 获取完整的请求路径，包括：域名，端口，上下文访问路径
   *
   * @param request 请求对象
   * @return 服务地址
   */
  public static String getDomain(HttpServletRequest request) {
    StringBuffer url = request.getRequestURL();
    String contextPath = request.getServletContext().getContextPath();
    return url.delete(url.length() - request.getRequestURI().length(), url.length())
        .append(contextPath)
        .toString();
  }

  /** 获取 response */
  public static HttpServletResponse getResponse() {
    return getRequestAttributes().getResponse();
  }

  /** 获取 session */
  public static HttpSession getSession() {
    return getRequest().getSession();
  }

  public static ServletRequestAttributes getRequestAttributes() {
    RequestAttributes attributes = RequestContextHolder.getRequestAttributes();
    return (ServletRequestAttributes) attributes;
  }

  /**
   * 将字符串渲染到客户端
   *
   * @param response 渲染对象
   * @param string 待渲染的字符串
   */
  public static void renderString(HttpServletResponse response, String string) {
    try {
      response.setStatus(200);
      response.setContentType("application/json");
      response.setCharacterEncoding(String.valueOf(StandardCharsets.UTF_8));
      response.getWriter().print(string);
    } catch (IOException e) {
      e.printStackTrace();
    }
  }

  /**
   * 是否是 Ajax 异步请求
   *
   * @param request
   */
  public static boolean isAjaxRequest(HttpServletRequest request) {
    String accept = request.getHeader("accept");
    if (accept != null && accept.contains("application/json")) {
      return true;
    }

    String xRequestedWith = request.getHeader("X-Requested-With");
    if (xRequestedWith != null && xRequestedWith.contains("XMLHttpRequest")) {
      return true;
    }

    String uri = request.getRequestURI();
    if (StringUtils.endsWithAny(uri.toLowerCase(), ".json", ".xml")) {
      return true;
    }

    String ajax = request.getParameter("__ajax");
    return StringUtils.endsWithAny(ajax.toLowerCase(), "json", "xml");
  }

  /**
   * 内容编码
   *
   * @param str 内容
   * @return 编码后的内容
   */
  public static String urlEncode(String str) {
    try {
      return URLEncoder.encode(str, StandardCharsets.UTF_8.name());
    } catch (UnsupportedEncodingException e) {
      return StringUtils.EMPTY;
    }
  }

  /**
   * 内容解码
   *
   * @param str 内容
   * @return 解码后的内容
   */
  public static String urlDecode(String str) {
    try {
      return URLDecoder.decode(str, StandardCharsets.UTF_8.name());
    } catch (UnsupportedEncodingException e) {
      return StringUtils.EMPTY;
    }
  }
}
