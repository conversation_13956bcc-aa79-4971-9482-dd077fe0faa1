package com.xinjian.common.utils;

import java.lang.management.ManagementFactory;
import java.time.Duration;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;

/**
 * 现代时间工具类 (基于 java.time)
 *
 * <p>特点：线程安全、不可变、API 清晰
 */
public final class TimeUtils {

  // 预定义常用的格式化器，它们是线程安全的，可以作为常量复用
  public static final DateTimeFormatter YYYY_MM_DD_FORMATTER =
      DateTimeFormatter.ofPattern("yyyy-MM-dd");
  public static final DateTimeFormatter YYYY_MM_DD_HH_MM_SS_FORMATTER =
      DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
  public static final DateTimeFormatter YYYYMMDDHHMMSS_FORMATTER =
      DateTimeFormatter.ofPattern("yyyyMMddHHmmss");

  /** 私有构造函数，防止该工具类被实例化 */
  private TimeUtils() {}

  /**
   * 获取当前的日期和时间对象
   *
   * @return LocalDateTime
   */
  public static LocalDateTime now() {
    return LocalDateTime.now();
  }

  /**
   * 获取当前的日期对象
   *
   * @return LocalDate
   */
  public static LocalDate nowDate() {
    return LocalDate.now();
  }

  /**
   * 使用默认格式 "yyyy-MM-dd HH:mm:ss" 格式化日期时间
   *
   * @param dateTime 要格式化的日期时间
   * @return 格式化后的字符串
   */
  public static String formatDateTime(LocalDateTime dateTime) {
    return dateTime.format(YYYY_MM_DD_HH_MM_SS_FORMATTER);
  }

  /**
   * 使用默认格式 "yyyy-MM-dd" 格式化日期
   *
   * @param date 要格式化的日期
   * @return 格式化后的字符串
   */
  public static String formatDate(LocalDate date) {
    return date.format(YYYY_MM_DD_FORMATTER);
  }

  /**
   * 解析 "yyyy-MM-dd HH:mm:ss" 格式的字符串
   *
   * @param dateTimeStr 日期时间字符串
   * @return LocalDateTime 对象
   */
  public static LocalDateTime parseDateTime(String dateTimeStr) {
    return LocalDateTime.parse(dateTimeStr, YYYY_MM_DD_HH_MM_SS_FORMATTER);
  }

  /**
   * 解析 "yyyy-MM-dd" 格式的字符串
   *
   * @param dateStr 日期字符串
   * @return LocalDate 对象
   */
  public static LocalDate parseDateToLocalDate(String dateStr) {
    return LocalDate.parse(dateStr, YYYY_MM_DD_FORMATTER);
  }

  /**
   * 计算两个日期之间相差的天数
   *
   * @param startDate 开始日期
   * @param endDate 结束日期
   * @return 相差天数
   */
  public static long betweenDays(LocalDate startDate, LocalDate endDate) {
    return ChronoUnit.DAYS.between(startDate, endDate);
  }

  /** 格式化两个时间点之间的时长，例如 "X 天 Y 小时 Z 分钟" */
  public static String formatDuration(LocalDateTime startTime, LocalDateTime endTime) {
    if (startTime == null || endTime == null) {
      return "";
    }
    Duration duration = Duration.between(startTime, endTime);
    long days = duration.toDays();
    long hours = duration.toHours() % 24;
    long minutes = duration.toMinutes() % 60;
    return String.format("%d天%d小时%d分钟", days, hours, minutes);
  }

  /** 获取当前日期时间字符串（格式：yyyy-MM-dd） */
  public static String getCurrentDateStr() {
    return now().format(YYYY_MM_DD_FORMATTER);
  }

  /** 获取当前日期时间字符串（格式：yyyy-MM-dd HH:mm:ss） */
  public static String getCurrentDateTimeStr() {
    return now().format(YYYY_MM_DD_HH_MM_SS_FORMATTER);
  }

  /** 获取服务器启动时间（从系统属性获取） */
  public static LocalDateTime getServerStartDate() {
    long startTime = ManagementFactory.getRuntimeMXBean().getStartTime();
    return LocalDateTime.ofInstant(Instant.ofEpochMilli(startTime), ZoneId.systemDefault());
  }

  /** 日期路径 即年/月/日 如 2018/08/08 */
  public static String datePath() {
    return now().format(DateTimeFormatter.ofPattern("yyyy/MM/dd"));
  }

  /** 日期路径 即年/月/日 如 20180808 */
  public static String dateTime() {
    return now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
  }

  /** 使用指定格式格式化日期时间为字符串 */
  public static String format(String pattern, LocalDateTime dateTime) {
    if (dateTime == null) {
      return "";
    }
    return dateTime.format(DateTimeFormatter.ofPattern(pattern));
  }

  /** 解析多种格式的日期字符串 */
  public static LocalDateTime parseDate(String dateStr) {
    if (dateStr == null || dateStr.trim().isEmpty()) {
      return null;
    }

    // 支持的日期格式
    String[] patterns = {
      "yyyy-MM-dd", "yyyy-MM-dd HH:mm:ss", "yyyy-MM-dd HH:mm", "yyyy-MM",
      "yyyy/MM/dd", "yyyy/MM/dd HH:mm:ss", "yyyy/MM/dd HH:mm", "yyyy/MM",
      "yyyy.MM.dd", "yyyy.MM.dd HH:mm:ss", "yyyy.MM.dd HH:mm", "yyyy.MM"
    };

    for (String pattern : patterns) {
      try {
        return LocalDateTime.parse(dateStr, DateTimeFormatter.ofPattern(pattern));
      } catch (Exception e) {
        // 尝试下一个格式
      }
    }

    // 如果所有格式都失败，尝试作为纯日期解析
    try {
      return LocalDate.parse(dateStr, DateTimeFormatter.ofPattern("yyyy-MM-dd")).atStartOfDay();
    } catch (Exception e) {
      return null;
    }
  }
}
