package com.xinjian.common.utils;

public class CaseUtils {
  /**
   * 转为大驼峰 eg. camelCase -> CamelCase eg. kebab-case -> KebabCase eg. snake_case -> SnakeCase
   *
   * @param str any case string
   * @return PascalCase string
   */
  public static String toPascalCase(String str) {
    str = str.substring(0, 1).toUpperCase() + str.substring(1);

    StringBuilder builder = new StringBuilder(str);

    for (int i = 0; i < builder.length(); i++) {

      if (builder.charAt(i) == '-' || builder.charAt(i) == '_') {
        builder.deleteCharAt(i);
        builder.replace(i, i + 1, String.valueOf(Character.toUpperCase(builder.charAt(i))));
      }
    }

    return builder.toString();
  }
}
