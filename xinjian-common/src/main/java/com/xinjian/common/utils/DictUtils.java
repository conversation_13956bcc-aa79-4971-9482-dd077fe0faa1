package com.xinjian.common.utils;

import com.xinjian.common.core.domain.entity.SysDictData;
import com.xinjian.common.service.DictService;
import java.util.List;
import java.util.Objects;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

/** 字典服务类 */
@Component
@RequiredArgsConstructor
public class DictUtils {
  /** 分隔符 */
  public static final String SEPARATOR = ",";

  private final DictService dictService;

  /**
   * 获取字典数据
   *
   * @param dictType 字典类型
   * @return 字典数据列表
   */
  public List<SysDictData> getDictData(String dictType) {
    return dictService.selectDictDataByType(dictType);
  }

  /**
   * 根据字典类型和字典值获取字典标签
   *
   * @param dictType 字典类型
   * @param dictValue 字典值
   * @param separator 分隔符
   * @return 字典标签
   */
  public String getDictLabel(String dictType, String dictValue, String separator) {
    StringBuilder propertyString = new StringBuilder();
    List<SysDictData> datas = getDictData(dictType);
    if (Objects.isNull(datas)) {
      return StringUtils.EMPTY;
    }
    if (StringUtils.containsAny(separator, dictValue)) {
      for (SysDictData dict : datas) {
        for (String value : dictValue.split(separator)) {
          if (value.equals(dict.getDictValue())) {
            propertyString.append(dict.getDictLabel()).append(separator);
            break;
          }
        }
      }
    } else {
      for (SysDictData dict : datas) {
        if (dictValue.equals(dict.getDictValue())) {
          return dict.getDictLabel();
        }
      }
    }
    return StringUtils.stripEnd(propertyString.toString(), separator);
  }

  /**
   * 根据字典类型和字典标签获取字典值
   *
   * @param dictType 字典类型
   * @param dictLabel 字典标签
   * @param separator 分隔符
   * @return 字典值
   */
  public String getDictValue(String dictType, String dictLabel, String separator) {
    StringBuilder propertyString = new StringBuilder();
    List<SysDictData> datas = getDictData(dictType);
    if (Objects.isNull(datas)) {
      return StringUtils.EMPTY;
    }
    if (StringUtils.containsAny(separator, dictLabel)) {
      for (SysDictData dict : datas) {
        for (String label : dictLabel.split(separator)) {
          if (label.equals(dict.getDictLabel())) {
            propertyString.append(dict.getDictValue()).append(separator);
            break;
          }
        }
      }
    } else {
      for (SysDictData dict : datas) {
        if (dictLabel.equals(dict.getDictLabel())) {
          return dict.getDictValue();
        }
      }
    }
    return StringUtils.stripEnd(propertyString.toString(), separator);
  }

  /**
   * 根据字典类型获取字典所有标签
   *
   * @param dictType 字典类型
   * @return 字典值
   */
  public String getDictLabels(String dictType) {
    StringBuilder propertyString = new StringBuilder();
    List<SysDictData> datas = getDictData(dictType);
    if (Objects.isNull(datas)) {
      return StringUtils.EMPTY;
    }
    for (SysDictData dict : datas) {
      propertyString.append(dict.getDictLabel()).append(SEPARATOR);
    }
    return StringUtils.stripEnd(propertyString.toString(), SEPARATOR);
  }
}
