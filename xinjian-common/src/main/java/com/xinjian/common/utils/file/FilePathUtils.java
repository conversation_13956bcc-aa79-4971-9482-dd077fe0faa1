package com.xinjian.common.utils.file;

import com.xinjian.common.properties.StorageProperties;
import java.io.File;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

/** 文件路径工具类 */
@Component
@RequiredArgsConstructor
public class FilePathUtils {

  private final StorageProperties storageProperties;

  /**
   * 获取下载路径
   *
   * @param filename 文件名称
   * @return 完整的下载路径
   */
  public String getDownloadPath(String filename) {
    String downloadPath = storageProperties.getPath() + "/download/" + filename;
    File desc = new File(downloadPath);
    if (!desc.getParentFile().exists()) {
      desc.getParentFile().mkdirs();
    }
    return downloadPath;
  }

  /**
   * 获取导入路径
   *
   * @return 导入路径
   */
  public String getImportPath() {
    return storageProperties.getPath() + "/import";
  }

  /**
   * 获取绝对文件
   *
   * @param uploadDir 上传目录
   * @param pathName 路径名称
   * @return 文件对象
   */
  public File getAbsoluteFile(String uploadDir, String pathName) {
    File desc = new File(uploadDir + File.separator + pathName);

    if (!desc.exists()) {
      if (!desc.getParentFile().exists()) {
        desc.getParentFile().mkdirs();
      }
    }
    return desc;
  }

  /**
   * 获取路径文件名
   *
   * @param uploadDir 上传目录
   * @param pathName 路径名称
   * @return 相对路径
   */
  public String getPathFileName(String uploadDir, String pathName) {
    String storagePath = storageProperties.getPath();

    // 使用 Paths API 来安全地获取相对路径
    java.nio.file.Path base = java.nio.file.Paths.get(storagePath);
    java.nio.file.Path target = java.nio.file.Paths.get(uploadDir);
    String relativeDir = base.relativize(target).toString();

    // 拼接文件名并统一路径分隔符
    String finalPath = java.nio.file.Paths.get(relativeDir, pathName).toString();
    return finalPath.replace("\\", "/");
  }

  /**
   * 获取默认允许的文件扩展名
   *
   * @return 默认允许的文件扩展名数组
   */
  public String[] getDefaultAllowedExtensions() {
    return storageProperties.getDefaultAllowedExtensions();
  }
}
