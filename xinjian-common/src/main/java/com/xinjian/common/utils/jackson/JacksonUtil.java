package com.xinjian.common.utils.jackson;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.json.JsonReadFeature;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.*;
import com.fasterxml.jackson.databind.json.JsonMapper;
import com.fasterxml.jackson.databind.type.CollectionType;
import com.fasterxml.jackson.datatype.jdk8.Jdk8Module;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import com.fasterxml.jackson.module.paramnames.ParameterNamesModule;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

/**
 * Jackson 工具类，用于 JSON 序列化和反序列化。
 *
 * <p>该工具类旨在提供一个与 fastjson2 API 相似的接口，以便在项目中统一 JSON 操作。封装了 Jackson 的核心功能，并提供了对常见场景的简化支持。
 */
@Slf4j
public class JacksonUtil {

  /** 启用的一系列 Jackson 读取特性，以增强兼容性。 */
  private static final Set<JsonReadFeature> JSON_READ_FEATURES_ENABLED =
      new HashSet<>(
          Arrays.asList(
              // 允许在 JSON 中使用 Java/C++ 风格的注释 (//, /* ... */)
              JsonReadFeature.ALLOW_JAVA_COMMENTS,
              // 允许使用非双引号引起来的字段名
              JsonReadFeature.ALLOW_UNQUOTED_FIELD_NAMES,
              // 允许使用单引号来包住字段名和字符串值
              JsonReadFeature.ALLOW_SINGLE_QUOTES,
              // 允许 JSON 字符串包含未转义的控制字符（ASCII < 32，包括制表符和换行符）
              JsonReadFeature.ALLOW_UNESCAPED_CONTROL_CHARS,
              // 允许数字有前导零（例如，0001）
              JsonReadFeature.ALLOW_LEADING_ZEROS_FOR_NUMBERS,
              // 允许将 NaN, INF, -INF 识别为有效的数字类型
              JsonReadFeature.ALLOW_NON_NUMERIC_NUMBERS,
              // 允许在 JSON 对象中出现缺失的值（例如，[ "a",, "c" ] 被视为 [ "a", null, "c" ]）
              JsonReadFeature.ALLOW_MISSING_VALUES,
              // 允许在数组或对象的末尾有额外的逗号
              JsonReadFeature.ALLOW_TRAILING_COMMA));

  private static ObjectMapper mapper;
  private static ObjectMapper redisMapper;

  static {
    try {
      // 初始化 ObjectMapper
      mapper = initMapper();
      redisMapper = initRedisMapper();
    } catch (Exception e) {
      log.error("Jackson ObjectMapper 初始化失败", e);
    }
  }

  /**
   * 初始化一个新的 ObjectMapper 实例，并启用预设的读取特性。
   *
   * @return 配置好的 ObjectMapper 实例
   */
  public static ObjectMapper initMapper() {
    JsonMapper.Builder builder =
        JsonMapper.builder().enable(JSON_READ_FEATURES_ENABLED.toArray(new JsonReadFeature[0]));
    return initMapperConfig(builder.build());
  }

  public static ObjectMapper initRedisMapper() {
    JsonMapper.Builder builder =
        JsonMapper.builder().enable(JSON_READ_FEATURES_ENABLED.toArray(new JsonReadFeature[0]));
    ObjectMapper objectMapper = initMapperConfig(builder.build());
    objectMapper.activateDefaultTyping(
        objectMapper.getPolymorphicTypeValidator(),
        ObjectMapper.DefaultTyping.NON_FINAL,
        com.fasterxml.jackson.annotation.JsonTypeInfo.As.PROPERTY);
    return objectMapper;
  }

  /**
   * 对 ObjectMapper 进行详细配置，以满足项目的通用需求。
   *
   * @param objectMapper 待配置的 ObjectMapper 实例
   * @return 配置完成的 ObjectMapper 实例
   */
  public static ObjectMapper initMapperConfig(ObjectMapper objectMapper) {
    String dateTimeFormat = "yyyy-MM-dd HH:mm:ss";
    // 设置日期格式
    objectMapper.setDateFormat(new SimpleDateFormat(dateTimeFormat));
    // 序列化时忽略 null 值的字段
    objectMapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
    // 关闭美化输出，以提高性能
    objectMapper.configure(SerializationFeature.INDENT_OUTPUT, false);
    // 允许将单个值作为数组处理
    objectMapper.enable(DeserializationFeature.ACCEPT_SINGLE_VALUE_AS_ARRAY);
    // 禁止在遇到重复的属性时失败
    objectMapper.enable(DeserializationFeature.FAIL_ON_READING_DUP_TREE_KEY);
    // 禁止将数字作为枚举的序数来反序列化
    objectMapper.enable(DeserializationFeature.FAIL_ON_NUMBERS_FOR_ENUMS);
    // 在遇到未知属性时，禁止反序列化失败
    objectMapper.disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES);
    // 在序列化空对象时（无属性），禁止失败
    objectMapper.disable(SerializationFeature.FAIL_ON_EMPTY_BEANS);
    // 禁止将日期序列化为时间戳
    objectMapper.disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS);
    // 允许忽略未知字段
    objectMapper.enable(JsonGenerator.Feature.IGNORE_UNKNOWN);
    // 在序列化 BigDecimal 时，以普通字符串形式输出，而不是科学计数法
    objectMapper.enable(JsonGenerator.Feature.WRITE_BIGDECIMAL_AS_PLAIN);
    // 注册模块以支持 Java 8 的特性
    objectMapper.registerModule(new ParameterNamesModule());
    objectMapper.registerModule(new Jdk8Module());
    // 配置 Java 8 的日期时间模块
    JavaTimeModule javaTimeModule = new JavaTimeModule();
    javaTimeModule
        .addSerializer(
            LocalDateTime.class,
            new LocalDateTimeSerializer(DateTimeFormatter.ofPattern(dateTimeFormat)))
        .addDeserializer(
            LocalDateTime.class,
            new LocalDateTimeDeserializer(DateTimeFormatter.ofPattern(dateTimeFormat)));
    objectMapper.registerModule(javaTimeModule);
    return objectMapper;
  }

  /**
   * 获取预配置的静态 ObjectMapper 实例。
   *
   * @return ObjectMapper 实例
   */
  public static ObjectMapper getObjectMapper() {
    return mapper;
  }

  public static ObjectMapper getRedisObjectMapper() {
    return redisMapper;
  }

  /**
   * 将 Java 对象序列化为 JSON 字符串。
   *
   * @param object 要序列化的对象
   * @return JSON 字符串，如果对象为 null，则返回 null
   */
  public static String toJSONString(Object object) {
    if (object == null) {
      return null;
    }
    try {
      return mapper.writeValueAsString(object);
    } catch (JsonProcessingException e) {
      log.error("对象序列化为 JSON 字符串时出错", e);
      throw new JacksonException(e.getMessage(), e);
    }
  }

  /**
   * 将 JSON 字符串反序列化为指定类型的 Java 对象。
   *
   * @param json JSON 字符串
   * @param clazz 目标对象的 Class 类型
   * @param <T> 目标对象的类型
   * @return 反序列化后的对象，如果 JSON 为空或 null，则返回 null
   */
  public static <T> T parseObject(String json, Class<T> clazz) {
    if (StringUtils.isEmpty(json)) {
      return null;
    }
    try {
      return mapper.readValue(json, clazz);
    } catch (IOException e) {
      log.warn("JSON 字符串反序列化为对象时出错，json: {}", json, e);
      throw new JacksonException(e.getMessage(), e);
    }
  }

  /**
   * 将 JSON 字符串反序列化为复杂的泛型类型（如 List<User> 或 Map<String, Object>）。
   *
   * @param json JSON 字符串
   * @param typeReference 包含泛型信息的 TypeReference
   * @param <T> 目标对象的类型
   * @return 反序列化后的对象，如果 JSON 为空或 null，则返回 null
   */
  public static <T> T parseObject(String json, TypeReference<T> typeReference) {
    if (StringUtils.isEmpty(json)) {
      return null;
    }
    try {
      return mapper.readValue(json, typeReference);
    } catch (IOException e) {
      log.warn("JSON 字符串反序列化为泛型类型时出错，json: {}", json, e);
      throw new JacksonException(e.getMessage(), e);
    }
  }

  /**
   * 将 JSON 数组字符串反序列化为指定类型的 List。
   *
   * @param json JSON 数组字符串
   * @param clazz 列表中元素的 Class 类型
   * @param <T> 列表中元素的类型
   * @return 反序列化后的 List，如果 JSON 为空或 null，则返回空列表
   */
  public static <T> List<T> parseArray(String json, Class<T> clazz) {
    if (StringUtils.isEmpty(json)) {
      return Collections.emptyList();
    }
    try {
      CollectionType collectionType =
          mapper.getTypeFactory().constructCollectionType(ArrayList.class, clazz);
      return mapper.readValue(json, collectionType);
    } catch (IOException e) {
      log.warn("JSON 数组字符串反序列化为 List 时出错，json: {}", json, e);
      throw new JacksonException(e.getMessage(), e);
    }
  }
}
