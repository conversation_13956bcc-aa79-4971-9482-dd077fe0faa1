package com.xinjian.common.utils.reflect;

import com.xinjian.common.core.text.Convert;
import com.xinjian.common.utils.TimeUtils;
import java.lang.reflect.*;
import java.time.LocalDateTime;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import org.apache.poi.ss.usermodel.DateUtil;

/** 反射工具类。提供调用 getter/setter 方法，访问私有变量，调用私有方法，获取泛型类型 Class, 被 AOP 过的真实类等工具函数。 */
@SuppressWarnings("rawtypes")
@Slf4j
public class ReflectUtils {
  private static final String SETTER_PREFIX = "set";

  private static final String GETTER_PREFIX = "get";

  private static final String CGLIB_CLASS_SEPARATOR = "$$";

  /** 调用 Getter 方法。支持多级，如：对象名。对象名。方法 */
  @SuppressWarnings("unchecked")
  public static <E> E invokeGetter(Object obj, String propertyName) {
    Object object = obj;
    for (String name : StringUtils.split(propertyName, ".")) {
      String getterMethodName = GETTER_PREFIX + StringUtils.capitalize(name);
      object = invokeMethod(object, getterMethodName, new Class[] {}, new Object[] {});
    }
    return (E) object;
  }

  /** 调用 Setter 方法，仅匹配方法名。支持多级，如：对象名。对象名。方法 */
  public static <E> void invokeSetter(Object obj, String propertyName, E value) {
    Object object = obj;
    String[] names = StringUtils.split(propertyName, ".");
    for (int i = 0; i < names.length; i++) {
      if (i < names.length - 1) {
        String getterMethodName = GETTER_PREFIX + StringUtils.capitalize(names[i]);
        object = invokeMethod(object, getterMethodName, new Class[] {}, new Object[] {});
      } else {
        String setterMethodName = SETTER_PREFIX + StringUtils.capitalize(names[i]);
        invokeMethodByName(object, setterMethodName, new Object[] {value});
      }
    }
  }

  /** 直接读取对象属性值，无视 private/protected 修饰符，不经过 getter 函数。 */
  @SuppressWarnings("unchecked")
  public static <E> E getFieldValue(final Object obj, final String fieldName) {
    Field field = getAccessibleField(obj, fieldName);
    if (field == null) {
      log.debug("在 [" + obj.getClass() + "] 中，没有找到 [" + fieldName + "] 字段 ");
      return null;
    }
    E result = null;
    try {
      result = (E) field.get(obj);
    } catch (IllegalAccessException e) {
      log.error("不可能抛出的异常{}", e.getMessage());
    }
    return result;
  }

  /** 直接设置对象属性值，无视 private/protected 修饰符，不经过 setter 函数。 */
  public static <E> void setFieldValue(final Object obj, final String fieldName, final E value) {
    Field field = getAccessibleField(obj, fieldName);
    if (field == null) {
      // throw new IllegalArgumentException("在 [" + obj.getClass() + "] 中，没有找到 [" + fieldName + "]
      // 字段 ");
      log.debug("在 [" + obj.getClass() + "] 中，没有找到 [" + fieldName + "] 字段 ");
      return;
    }
    try {
      field.set(obj, value);
    } catch (IllegalAccessException e) {
      log.error("不可能抛出的异常：{}", e.getMessage());
    }
  }

  /**
   * 直接调用对象方法，无视 private/protected 修饰符。用于一次性调用的情况，否则应使用 getAccessibleMethod() 函数获得 Method 后反复调用。
   * 同时匹配方法名 + 参数类型，
   */
  @SuppressWarnings("unchecked")
  public static <E> E invokeMethod(
      final Object obj,
      final String methodName,
      final Class<?>[] parameterTypes,
      final Object[] args) {
    if (obj == null || methodName == null) {
      return null;
    }
    Method method = getAccessibleMethod(obj, methodName, parameterTypes);
    if (method == null) {
      log.debug("在 [" + obj.getClass() + "] 中，没有找到 [" + methodName + "] 方法 ");
      return null;
    }
    try {
      return (E) method.invoke(obj, args);
    } catch (Exception e) {
      String msg = "method: " + method + ", obj: " + obj + ", args: " + args + "";
      throw convertReflectionExceptionToUnchecked(msg, e);
    }
  }

  /**
   * 直接调用对象方法，无视 private/protected 修饰符，用于一次性调用的情况，否则应使用 getAccessibleMethodByName() 函数获得 Method
   * 后反复调用。只匹配函数名，如果有多个同名函数调用第一个。
   */
  @SuppressWarnings("unchecked")
  public static <E> E invokeMethodByName(
      final Object obj, final String methodName, final Object[] args) {
    Method method = getAccessibleMethodByName(obj, methodName, args.length);
    if (method == null) {
      // 如果为空不报错，直接返回空。
      log.debug("在 [" + obj.getClass() + "] 中，没有找到 [" + methodName + "] 方法 ");
      return null;
    }
    try {
      // 类型转换（将参数数据类型转换为目标方法参数类型）
      Class<?>[] cs = method.getParameterTypes();
      for (int i = 0; i < cs.length; i++) {
        if (args[i] != null && !args[i].getClass().equals(cs[i])) {
          if (cs[i] == String.class) {
            args[i] = Convert.toStr(args[i]);
            if (StringUtils.endsWith((String) args[i], ".0")) {
              args[i] = StringUtils.substringBefore((String) args[i], ".0");
            }
          } else if (cs[i] == Integer.class) {
            args[i] = Convert.toInt(args[i]);
          } else if (cs[i] == Long.class) {
            args[i] = Convert.toLong(args[i]);
          } else if (cs[i] == Double.class) {
            args[i] = Convert.toDouble(args[i]);
          } else if (cs[i] == Float.class) {
            args[i] = Convert.toFloat(args[i]);
          } else if (cs[i] == LocalDateTime.class) {
            if (args[i] instanceof String) {
              args[i] = TimeUtils.parseDate((String) args[i]);
            } else {
              args[i] =
                  LocalDateTime.ofInstant(
                      DateUtil.getJavaDate((Double) args[i]).toInstant(),
                      java.time.ZoneId.systemDefault());
            }
          } else if (cs[i] == boolean.class || cs[i] == Boolean.class) {
            args[i] = Convert.toBool(args[i]);
          }
        }
      }
      return (E) method.invoke(obj, args);
    } catch (Exception e) {
      String msg = "method: " + method + ", obj: " + obj + ", args: " + args + "";
      throw convertReflectionExceptionToUnchecked(msg, e);
    }
  }

  /** 循环向上转型，获取对象的 DeclaredField, 并强制设置为可访问。如向上转型到 Object 仍无法找到，返回 null. */
  public static Field getAccessibleField(final Object obj, final String fieldName) {
    // 为空不报错。直接返回 null
    if (obj == null) {
      return null;
    }
    Validate.notBlank(fieldName, "fieldName can't be blank");
    for (Class<?> superClass = obj.getClass();
        superClass != Object.class;
        superClass = superClass.getSuperclass()) {
      try {
        Field field = superClass.getDeclaredField(fieldName);
        makeAccessible(field);
        return field;
      } catch (NoSuchFieldException e) {
        continue;
      }
    }
    return null;
  }

  /**
   * 循环向上转型，获取对象的 DeclaredMethod，并强制设置为可访问。如向上转型到 Object 仍无法找到，返回 null. 匹配函数名 + 参数类型。
   * 用于方法需要被多次调用的情况。先使用本函数先取得 Method，然后调用 Method.invoke(Object obj, Object... args)
   */
  public static Method getAccessibleMethod(
      final Object obj, final String methodName, final Class<?>... parameterTypes) {
    // 为空不报错。直接返回 null
    if (obj == null) {
      return null;
    }
    Validate.notBlank(methodName, "methodName can't be blank");
    for (Class<?> searchType = obj.getClass();
        searchType != Object.class;
        searchType = searchType.getSuperclass()) {
      try {
        Method method = searchType.getDeclaredMethod(methodName, parameterTypes);
        makeAccessible(method);
        return method;
      } catch (NoSuchMethodException e) {
        continue;
      }
    }
    return null;
  }

  /**
   * 循环向上转型，获取对象的 DeclaredMethod，并强制设置为可访问。如向上转型到 Object 仍无法找到，返回 null. 只匹配函数名。
   * 用于方法需要被多次调用的情况。先使用本函数先取得 Method，然后调用 Method.invoke(Object obj, Object... args)
   */
  public static Method getAccessibleMethodByName(
      final Object obj, final String methodName, int argsNum) {
    // 为空不报错。直接返回 null
    if (obj == null) {
      return null;
    }
    Validate.notBlank(methodName, "methodName can't be blank");
    for (Class<?> searchType = obj.getClass();
        searchType != Object.class;
        searchType = searchType.getSuperclass()) {
      Method[] methods = searchType.getDeclaredMethods();
      for (Method method : methods) {
        if (method.getName().equals(methodName) && method.getParameterTypes().length == argsNum) {
          makeAccessible(method);
          return method;
        }
      }
    }
    return null;
  }

  /** 改变 private/protected 的方法为 public，尽量不调用实际改动的语句，避免 JDK 的 SecurityManager 抱怨。 */
  public static void makeAccessible(Method method) {
    if ((!Modifier.isPublic(method.getModifiers())
            || !Modifier.isPublic(method.getDeclaringClass().getModifiers()))
        && !method.isAccessible()) {
      method.setAccessible(true);
    }
  }

  /** 改变 private/protected 的成员变量为 public，尽量不调用实际改动的语句，避免 JDK 的 SecurityManager 抱怨。 */
  public static void makeAccessible(Field field) {
    if ((!Modifier.isPublic(field.getModifiers())
            || !Modifier.isPublic(field.getDeclaringClass().getModifiers())
            || Modifier.isFinal(field.getModifiers()))
        && !field.isAccessible()) {
      field.setAccessible(true);
    }
  }

  /** 通过反射，获得 Class 定义中声明的泛型参数的类型，注意泛型必须定义在父类处 如无法找到，返回 Object.class. */
  @SuppressWarnings("unchecked")
  public static <T> Class<T> getClassGenricType(final Class clazz) {
    return getClassGenricType(clazz, 0);
  }

  /** 通过反射，获得 Class 定义中声明的父类的泛型参数的类型。如无法找到，返回 Object.class. */
  public static Class getClassGenricType(final Class clazz, final int index) {
    Type genType = clazz.getGenericSuperclass();

    if (!(genType instanceof ParameterizedType)) {
      log.debug(clazz.getSimpleName() + "'s superclass not ParameterizedType");
      return Object.class;
    }

    Type[] params = ((ParameterizedType) genType).getActualTypeArguments();

    if (index >= params.length || index < 0) {
      log.debug(
          "Index: "
              + index
              + ", Size of "
              + clazz.getSimpleName()
              + "'s Parameterized Type: "
              + params.length);
      return Object.class;
    }
    if (!(params[index] instanceof Class)) {
      log.debug(
          clazz.getSimpleName() + " not set the actual class on superclass generic parameter");
      return Object.class;
    }

    return (Class) params[index];
  }

  public static Class<?> getUserClass(Object instance) {
    if (instance == null) {
      throw new RuntimeException("Instance must not be null");
    }
    Class clazz = instance.getClass();
    if (clazz != null && clazz.getName().contains(CGLIB_CLASS_SEPARATOR)) {
      Class<?> superClass = clazz.getSuperclass();
      if (superClass != null && !Object.class.equals(superClass)) {
        return superClass;
      }
    }
    return clazz;
  }

  /** 将反射时的 checked exception 转换为 unchecked exception. */
  public static RuntimeException convertReflectionExceptionToUnchecked(String msg, Exception e) {
    if (e instanceof IllegalAccessException
        || e instanceof IllegalArgumentException
        || e instanceof NoSuchMethodException) {
      return new IllegalArgumentException(msg, e);
    } else if (e instanceof InvocationTargetException) {
      return new RuntimeException(
          msg + ": " + ((InvocationTargetException) e).getTargetException().getMessage());
    }
    return new RuntimeException(msg + ": " + e.getMessage());
  }
}
