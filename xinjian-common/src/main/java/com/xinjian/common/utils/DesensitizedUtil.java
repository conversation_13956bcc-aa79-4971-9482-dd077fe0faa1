package com.xinjian.common.utils;

import org.apache.commons.lang3.StringUtils;

/** 脱敏工具类 */
public class DesensitizedUtil {
  /**
   * 密码的全部字符都用*代替，比如：******
   *
   * @param password 密码
   * @return 脱敏后的密码
   */
  public static String password(String password) {
    if (StringUtils.isBlank(password)) {
      return StringUtils.EMPTY;
    }
    return StringUtils.repeat('*', password.length());
  }

  /**
   * 对车牌号进行脱敏处理。保留前 3 位和最后 1 位，中间用*号替换。例如： - "粤 B12345" -> "粤 B1***5" - "粤 B123456" -> "粤 B1****6"
   * (新能源)
   *
   * @param carLicense 车牌号
   * @return 脱敏后的车牌号
   */
  public static String carLicense(String carLicense) {
    if (StringUtils.isBlank(carLicense)) {
      return StringUtils.EMPTY;
    }

    // 普通车牌 (7 位)
    if (carLicense.length() == 7) {
      // 3. 使用 StringUtils.overlay 进行替换
      // 从索引 3 开始，到索引 6 结束（不包含），用"***"进行覆盖
      return StringUtils.overlay(carLicense, "***", 3, 6);
    }
    // 新能源车牌 (8 位)
    else if (carLicense.length() == 8) {
      // 4. 使用 StringUtils.overlay 进行替换
      // 从索引 3 开始，到索引 7 结束（不包含），用"****"进行覆盖
      return StringUtils.overlay(carLicense, "****", 3, 7);
    }

    // 如果长度不是 7 或 8，按原样返回
    return carLicense;
  }
}
