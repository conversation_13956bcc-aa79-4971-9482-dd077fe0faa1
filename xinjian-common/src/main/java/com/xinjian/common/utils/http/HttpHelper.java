package com.xinjian.common.utils.http;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import javax.servlet.ServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.exception.ExceptionUtils;

/** 通用 http 工具封装 */
@Slf4j
public class HttpHelper {

  public static String getBodyString(ServletRequest request) {
    StringBuilder sb = new StringBuilder();
    BufferedReader reader = null;
    try (InputStream inputStream = request.getInputStream()) {
      reader = new BufferedReader(new InputStreamReader(inputStream, StandardCharsets.UTF_8));
      String line = "";
      while ((line = reader.readLine()) != null) {
        sb.append(line);
      }
    } catch (IOException e) {
      log.warn("getBodyString 出现问题！");
    } finally {
      if (reader != null) {
        try {
          reader.close();
        } catch (IOException e) {
          log.error(ExceptionUtils.getMessage(e));
        }
      }
    }
    return sb.toString();
  }
}
