package com.xinjian.common.utils;

import com.xinjian.common.core.domain.dto.LoginUser;
import com.xinjian.common.exception.status400.BadRequestException;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;

/** 安全服务工具类 */
public class SecurityUtils {
  /** 用户 ID */
  public static Long getUserId() {
    try {
      return getLoginUser().getUserId();
    } catch (Exception e) {
      throw new BadRequestException("获取用户 ID 异常");
    }
  }

  /** 获取用户 */
  public static LoginUser getLoginUser() {
    try {
      return (LoginUser) getAuthentication().getPrincipal();
    } catch (Exception e) {
      throw new BadRequestException("获取用户信息异常");
    }
  }

  /** 获取 Authentication */
  public static Authentication getAuthentication() {
    return SecurityContextHolder.getContext().getAuthentication();
  }

  /** 是否为管理员用户（限定了 user = 1） */
  public static boolean isAdminUser(Long userId) {
    return userId != null && 1L == userId;
  }

  /** 是否为管理员角色（限定了 role = 1） */
  public static boolean isAdminRole(Long roleId) {
    return roleId != null && 1L == roleId;
  }
}
