package com.xinjian.common.service;

import com.xinjian.common.properties.StorageProperties;
import java.io.ByteArrayInputStream;
import java.io.FileInputStream;
import java.io.InputStream;
import java.net.URL;
import java.net.URLConnection;
import java.nio.file.Paths;
import java.util.Arrays;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.util.IOUtils;
import org.springframework.stereotype.Service;

/** 图片处理服务 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ImageService {

  private final StorageProperties storageProperties;

  /**
   * 获取图片字节流
   *
   * @param imagePath 图片路径 (可以是网络 http 地址或本地相对路径)
   * @return 字节数组
   */
  public byte[] getImage(String imagePath) {
    try (InputStream is = getFileStream(imagePath)) {
      if (is == null) {
        return null;
      }
      return IOUtils.toByteArray(is);
    } catch (Exception e) {
      log.error("图片加载异常 {}", e.getMessage(), e);
      return null;
    }
  }

  /**
   * 获取文件输入流
   *
   * @param imagePath 图片路径
   * @return 输入流
   */
  public InputStream getFileStream(String imagePath) {
    try {
      if (imagePath.startsWith("http")) {
        // 网络地址
        URL url = new URL(imagePath);
        URLConnection connection = url.openConnection();
        connection.setConnectTimeout(30 * 1000);
        connection.setReadTimeout(60 * 1000);
        connection.setDoInput(true);
        return connection.getInputStream();
      } else {
        // 本机地址
        String localPath = storageProperties.getPath();
        String absolutePath = Paths.get(localPath, imagePath).toString();
        return new FileInputStream(absolutePath);
      }
    } catch (Exception e) {
      log.error("获取图片输入流异常 {}", e.getMessage(), e);
      return null;
    }
  }

  /**
   * 读取文件为字节数据（兼容旧方法）
   *
   * @param url 地址
   * @return 字节数据
   * @deprecated 请使用 {@link #getImage(String)} 方法
   */
  @Deprecated
  public byte[] readFile(String url) {
    try (InputStream in = getFileStream(url)) {
      if (in == null) {
        return null;
      }
      return IOUtils.toByteArray(in);
    } catch (Exception e) {
      log.error("获取文件路径异常 {}", e.getMessage(), e);
      return null;
    }
  }

  /**
   * 获取文件输入流（兼容旧方法）
   *
   * @param imagePath 图片路径
   * @return 输入流
   * @deprecated 请使用 {@link #getFileStream(String)} 方法
   */
  @Deprecated
  public InputStream getFile(String imagePath) {
    try {
      byte[] result = readFile(imagePath);
      if (result == null) {
        return null;
      }
      result = Arrays.copyOf(result, result.length);
      return new ByteArrayInputStream(result);
    } catch (Exception e) {
      log.error("获取图片异常 {}", e.getMessage(), e);
    }
    return null;
  }
}
