package com.xinjian.common.service;

import com.xinjian.common.core.domain.entity.SysDept;
import java.util.List;

/**
 * 系统部门服务接口
 *
 * <p>定义部门相关的核心业务操作，主要用于框架层的部门管理需求
 */
public interface ISysDeptService {

  /**
   * 根据部门ID查询部门信息
   *
   * @param deptId 部门ID
   * @return 部门信息
   */
  SysDept selectDeptById(Long deptId);

  /**
   * 根据部门名称查询部门信息
   *
   * @param deptName 部门名称
   * @return 部门信息
   */
  SysDept selectDeptByName(String deptName);

  /**
   * 查询部门列表
   *
   * @param dept 部门信息
   * @return 部门列表
   */
  List<SysDept> selectDeptList(SysDept dept);

  /**
   * 查询部门树结构信息
   *
   * @param dept 部门信息
   * @return 部门列表
   */
  List<SysDept> selectDeptTreeList(SysDept dept);

  /**
   * 构建前端所需要树结构
   *
   * @param depts 部门列表
   * @return 树结构列表
   */
  List<SysDept> buildDeptTree(List<SysDept> depts);

  /**
   * 根据角色ID查询部门树信息
   *
   * @param roleId 角色ID
   * @param deptCheckStrictly 部门树选择是否关联显示
   * @return 部门树信息列表
   */
  List<Long> selectDeptListByRoleId(Long roleId, boolean deptCheckStrictly);

  /**
   * 根据用户ID查询部门
   *
   * @param userId 用户ID
   * @return 部门信息
   */
  SysDept selectDeptByUserId(Long userId);

  /**
   * 校验部门名称是否唯一
   *
   * @param dept 部门信息
   * @return 结果
   */
  boolean checkDeptNameUnique(SysDept dept);

  /**
   * 校验部门是否有数据权限
   *
   * @param deptId 部门id
   */
  void checkDeptDataScope(Long deptId);

  /**
   * 校验部门是否有数据权限
   *
   * @param deptId 部门id
   * @param currentUserId 当前用户id
   */
  void checkDeptDataScope(Long deptId, Long currentUserId);

  /**
   * 新增保存部门信息
   *
   * @param dept 部门信息
   * @return 结果
   */
  int insertDept(SysDept dept);

  /**
   * 修改保存部门信息
   *
   * @param dept 部门信息
   * @return 结果
   */
  int updateDept(SysDept dept);

  /**
   * 删除部门管理信息
   *
   * @param deptId 部门ID
   * @return 结果
   */
  int deleteDeptById(Long deptId);

  /**
   * 修改子元素关系
   *
   * @param dept 被修改的部门
   * @param oldDeptId 修改前的父部门ID
   */
  void updateDeptChildren(SysDept dept, Long oldDeptId);

  /**
   * 修改所在部门的父级部门状态
   *
   * @param dept 当前部门
   */
  void updateParentDeptStatus(SysDept dept);

  /**
   * 校验部门是否存在用户
   *
   * @param deptId 部门ID
   * @return 结果 true 存在 false 不存在
   */
  boolean hasDeptUser(Long deptId);

  /**
   * 查询部门是否存在用户
   *
   * @param deptIds 部门ID组
   * @return 结果 true 存在 false 不存在
   */
  boolean hasDeptUser(Long[] deptIds);

  /**
   * 查询正常状态的子部门
   *
   * @param deptId 部门ID
   * @return 子部门数量
   */
  int selectNormalChildrenDeptById(Long deptId);

  /**
   * 是否存在子部门
   *
   * @param deptId 部门ID
   * @return 结果 true 存在 false 不存在
   */
  boolean hasChildByDeptId(Long deptId);

  /**
   * 查询部门是否存在用户
   *
   * @param deptId 部门ID
   * @return 结果 true 存在 false 不存在
   */
  boolean checkDeptExistUser(Long deptId);
}
