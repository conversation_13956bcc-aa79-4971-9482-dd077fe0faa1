package com.xinjian.common.service;

import com.xinjian.common.core.domain.entity.SysDictType;
import com.xinjian.common.core.domain.query.SysDictTypeQuery;
import java.util.List;

/**
 * 系统字典类型服务接口
 *
 * <p>定义字典类型相关的核心业务操作，主要用于框架层的字典数据初始化需求
 */
public interface ISysDictTypeService {

  /**
   * 根据条件查询字典类型列表
   *
   * @param query 查询参数
   * @return 字典类型列表
   */
  List<SysDictType> selectDictTypeList(SysDictTypeQuery query);

  /**
   * 根据字典类型查询字典数据
   *
   * @param dictType 字典类型
   * @return 字典数据
   */
  List<?> selectDictDataByType(String dictType);

  /** 加载字典缓存数据 */
  void loadingDictCache();

  /** 清空字典缓存数据 */
  void clearDictCache();
}
