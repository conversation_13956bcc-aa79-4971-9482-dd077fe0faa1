package com.xinjian.common.service;

import com.xinjian.common.core.domain.entity.SysUser;
import java.util.Set;
import javax.validation.constraints.NotNull;

/**
 * 系统权限服务接口
 *
 * <p>定义用户权限相关的核心业务操作，主要用于框架层的安全认证等场景
 */
public interface ISysPermissionService {

  /**
   * 获取用户的角色权限标识集合
   *
   * @param user 用户信息，不能为 null
   * @return 角色权限标识集合
   */
  Set<String> getRolePermission(@NotNull(message = "用户信息不能为空") SysUser user);

  /**
   * 获取用户的菜单权限标识集合
   *
   * @param user 用户信息，不能为 null
   * @return 菜单权限标识集合
   */
  Set<String> getMenuPermission(@NotNull(message = "用户信息不能为空") SysUser user);
}
