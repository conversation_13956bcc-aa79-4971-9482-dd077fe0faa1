package com.xinjian.common.service;

import com.xinjian.common.core.domain.entity.SysLogininfor;
import com.xinjian.common.core.domain.query.SysLogininforQuery;
import java.util.List;

/**
 * 系统登录日志服务接口
 *
 * <p>定义登录日志相关的核心业务操作，主要用于框架层的日志记录需求
 */
public interface ISysLogininforService {

  /**
   * 新增登录日志
   *
   * @param logininfor 登录日志信息
   */
  void insertLogininfor(SysLogininfor logininfor);

  /**
   * 查询系统登录日志集合
   *
   * @param query 查询参数
   * @return 登录日志集合
   */
  List<SysLogininfor> selectLogininforList(SysLogininforQuery query);

  /**
   * 批量删除系统登录日志
   *
   * @param infoIds 需要删除的登录日志ID
   */
  void deleteLogininforByIds(Long[] infoIds);

  /** 清空系统登录日志 */
  void cleanLogininfor();
}
