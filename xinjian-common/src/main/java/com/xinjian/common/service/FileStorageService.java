package com.xinjian.common.service;

import com.xinjian.common.exception.status400.BadRequestException;
import com.xinjian.common.properties.StorageProperties;
import java.io.File;
import java.io.IOException;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.Objects;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import org.apache.commons.io.FilenameUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.MimeType;
import org.springframework.util.MimeTypeUtils;
import org.springframework.web.multipart.MultipartFile;

/** 文件存储服务 */
@Service
@RequiredArgsConstructor
public class FileStorageService {

  private final StorageProperties properties;
  private static final DateTimeFormatter FILENAME_DATETIME_FORMATTER =
      DateTimeFormatter.ofPattern("yyyyMMddHHmmss");

  /**
   * 以默认配置进行文件上传
   *
   * @param file 上传的文件
   * @return 文件访问的相对路径
   * @throws IOException
   */
  public String upload(MultipartFile file) throws IOException {
    try {
      return upload(properties.getPath(), file, properties.getDefaultAllowedExtensions());
    } catch (Exception e) {
      throw new IOException(e.getMessage(), e);
    }
  }

  /**
   * 根据文件路径上传
   *
   * @param baseDir 相对应用的基目录
   * @param file 上传的文件
   * @return 文件访问的相对路径
   * @throws IOException
   */
  public String upload(String baseDir, MultipartFile file) throws IOException {
    try {
      return upload(baseDir, file, properties.getDefaultAllowedExtensions());
    } catch (Exception e) {
      throw new IOException(e.getMessage(), e);
    }
  }

  /**
   * 文件上传
   *
   * @param baseDir 相对应用的基目录
   * @param file 上传的文件
   * @param allowedExtension 上传文件类型
   * @return 返回上传成功的文件访问相对路径
   * @throws BadRequestException 如果文件校验失败
   * @throws IOException 文件读写异常
   */
  public String upload(String baseDir, MultipartFile file, String[] allowedExtension)
      throws IOException {
    int fileNameLength = Objects.requireNonNull(file.getOriginalFilename()).length();
    if (fileNameLength > properties.getDefaultFileNameLength()) {
      throw new BadRequestException(
          String.format("文件名长度超过限制，最大允许 %d 个字符", properties.getDefaultFileNameLength()));
    }

    assertAllowed(file, allowedExtension);

    String fileName = extractFilename(file);
    File absoluteFile = getAbsoluteFile(baseDir, fileName);
    file.transferTo(Paths.get(absoluteFile.getAbsolutePath()));

    return getPathFileName(baseDir, fileName);
  }

  /** 获取默认上传的地址 */
  public String getDefaultBaseDir() {
    return properties.getPath();
  }

  /** 获取导入上传路径 */
  public String getImportPath() {
    return properties.getPath() + "/import";
  }

  /** 获取头像上传路径 */
  public String getAvatarPath() {
    return properties.getPath() + "/avatar";
  }

  /** 获取下载路径 */
  public String getDownloadPath() {
    return properties.getPath() + "/download/";
  }

  /** 获取上传路径 */
  public String getUploadPath() {
    return properties.getPath() + "/upload";
  }

  /** 获取存储配置属性 */
  public StorageProperties getStorageProperties() {
    return properties;
  }

  /**
   * 获取文件名的后缀
   *
   * @param file 表单文件
   * @return 后缀名
   */
  private String getFileExtension(MultipartFile file) {
    String extension = FilenameUtils.getExtension(file.getOriginalFilename());
    if (extension == null || extension.isEmpty()) {
      MimeType mimeType =
          MimeTypeUtils.parseMimeType(Objects.requireNonNull(file.getContentType()));
      extension = mimeType.getSubtype();
    }
    return extension;
  }

  /** 编码文件名 */
  private String extractFilename(MultipartFile file) {
    String timestamp = LocalDateTime.now().format(FILENAME_DATETIME_FORMATTER);
    String baseName = FilenameUtils.getBaseName(file.getOriginalFilename());
    String uuid = UUID.randomUUID().toString().replace("-", "");
    String extension = getFileExtension(file);

    return String.format("%s_%s_%s.%s", timestamp, baseName, uuid, extension);
  }

  /** 获取绝对文件 */
  private File getAbsoluteFile(String uploadDir, String fileName) throws IOException {
    File desc = new File(uploadDir + File.separator + fileName);

    if (!desc.exists()) {
      if (!desc.getParentFile().exists()) {
        desc.getParentFile().mkdirs();
      }
    }
    return desc;
  }

  /** 获取路径文件名 */
  private String getPathFileName(String uploadDir, String fileName) throws IOException {
    String storagePath = properties.getPath();

    // 使用 Paths API 来安全地获取相对路径
    java.nio.file.Path base = Paths.get(storagePath);
    java.nio.file.Path target = Paths.get(uploadDir);
    String relativeDir = base.relativize(target).toString();

    // 拼接文件名并统一路径分隔符
    String finalPath = Paths.get(relativeDir, fileName).toString();
    return finalPath.replace("\\", "/");
  }

  /**
   * 文件大小校验
   *
   * @param file 上传的文件
   * @param allowedExtension 允许的扩展名
   * @throws BadRequestException 如果文件校验失败
   */
  private void assertAllowed(MultipartFile file, String[] allowedExtension)
      throws BadRequestException {
    long size = file.getSize();
    long maxSizeInBytes = properties.getDefaultMaxSize() * 1024 * 1024;
    if (size > maxSizeInBytes) {
      throw new BadRequestException(
          String.format("文件大小超过限制，最大允许 %d MB", properties.getDefaultMaxSize()));
    }

    String fileName = file.getOriginalFilename();
    String extension = getExtension(file);

    if (allowedExtension != null && !isAllowedExtension(extension, allowedExtension)) {
      if (Arrays.equals(allowedExtension, properties.getImageExtensions())) {
        throw new BadRequestException("无效的图片扩展名");
      } else if (Arrays.equals(allowedExtension, properties.getFlashExtensions())) {
        throw new BadRequestException("无效的 Flash 扩展名");
      } else if (Arrays.equals(allowedExtension, properties.getMediaExtensions())) {
        throw new BadRequestException("无效的媒体扩展名");
      } else if (Arrays.equals(allowedExtension, properties.getVideoExtensions())) {
        throw new BadRequestException("无效的视频扩展名");
      } else {
        throw new BadRequestException("无效的扩展名");
      }
    }
  }

  /**
   * 判断 MIME 类型是否是允许的 MIME 类型
   *
   * @param extension 扩展名
   * @param allowedExtension 允许的扩展名数组
   * @return 是否允许
   */
  private boolean isAllowedExtension(String extension, String[] allowedExtension) {
    if (extension == null) {
      return false;
    }
    for (String str : allowedExtension) {
      if (str.equalsIgnoreCase(extension)) {
        return true;
      }
    }
    return false;
  }

  /**
   * 获取文件名的后缀
   *
   * @param file 表单文件
   * @return 后缀名
   */
  private String getExtension(MultipartFile file) {
    String extension = FilenameUtils.getExtension(file.getOriginalFilename());
    if (extension == null || extension.isEmpty()) {
      MimeType mimeType =
          MimeTypeUtils.parseMimeType(Objects.requireNonNull(file.getContentType()));
      extension = mimeType.getSubtype();
    }
    return extension;
  }
}
