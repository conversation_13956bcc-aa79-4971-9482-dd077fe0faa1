package com.xinjian.common.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xinjian.common.core.domain.entity.SysUser;
import com.xinjian.common.core.domain.query.SysUserQuery;
import java.util.List;

/**
 * 系统用户服务接口
 *
 * <p>定义用户相关的核心业务操作，主要用于框架层的安全认证等场景
 */
public interface ISysUserService {

  /**
   * 通过用户名查询用户
   *
   * @param userName 用户名
   * @return 用户对象信息
   */
  SysUser selectUserByUserName(String userName);

  /**
   * 通过用户 ID 查询用户
   *
   * @param userId 用户 ID
   * @return 用户对象信息
   */
  SysUser selectUserById(Long userId);

  /**
   * 校验用户名称是否唯一
   *
   * @param userName 用户名称
   * @return 结果
   */
  boolean checkUserNameUnique(String userName);

  /**
   * 校验用户名称是否唯一
   *
   * @param user 用户信息
   * @return 结果
   */
  boolean checkUserNameUnique(SysUser user);

  /**
   * 校验手机号码是否唯一
   *
   * @param user 用户信息
   * @return 结果
   */
  boolean checkMobileUnique(SysUser user);

  /**
   * 校验email是否唯一
   *
   * @param user 用户信息
   * @return 结果
   */
  boolean checkEmailUnique(SysUser user);

  /**
   * 注册用户信息
   *
   * @param user 用户信息
   * @return 结果
   */
  boolean registerUser(SysUser user);

  /**
   * 修改用户基本信息
   *
   * @param user 用户信息
   * @return 结果
   */
  int updateUserProfile(SysUser user);

  /**
   * 修改用户头像
   *
   * @param userName 用户名
   * @param avatar 头像地址
   * @return 结果
   */
  boolean updateUserAvatar(String userName, String avatar);

  /**
   * 重置用户密码
   *
   * @param userName 用户名
   * @param password 密码
   * @return 结果
   */
  int resetUserPwd(String userName, String password);

  /**
   * 查询用户所属角色组
   *
   * @param userName 用户名
   * @return 结果
   */
  String selectUserRoleGroup(String userName);

  /**
   * 查询用户所属岗位组
   *
   * @param userName 用户名
   * @return 结果
   */
  String selectUserPostGroup(String userName);

  /**
   * 校验用户是否允许操作
   *
   * @param user 用户信息
   */
  void checkUserAllowed(SysUser user);

  /**
   * 校验用户是否有数据权限
   *
   * @param userId 用户 id
   * @param currentUserId 当前用户 id
   */
  void checkUserDataScope(Long userId, Long currentUserId);

  /**
   * 新增保存用户信息
   *
   * @param user 用户信息
   * @return 结果
   */
  int insertUser(SysUser user);

  /**
   * 修改保存用户信息
   *
   * @param user 用户信息
   * @return 结果
   */
  int updateUser(SysUser user);

  /**
   * 修改用户状态
   *
   * @param user 用户信息
   * @return 结果
   */
  int updateUserStatus(SysUser user);

  /**
   * 重置用户密码
   *
   * @param user 用户信息
   * @return 结果
   */
  int resetPwd(SysUser user);

  /**
   * 删除用户信息
   *
   * @param userId 用户ID
   * @return 结果
   */
  int deleteUserById(Long userId);

  /**
   * 批量删除用户信息
   *
   * @param userIds 需要删除的用户ID
   */
  void deleteUserByIds(Long[] userIds);

  /**
   * 用户授权角色
   *
   * @param userId 用户 ID
   * @param roleIds 角色组
   */
  void insertUserAuth(Long userId, Long[] roleIds);

  /**
   * 根据条件分页查询用户列表
   *
   * @param query 查询参数对象
   * @return 分页用户信息集合信息
   */
  IPage<SysUser> selectUserListByPage(SysUserQuery query);

  /**
   * 根据条件查询用户列表
   *
   * @param query 查询参数对象
   * @return 用户信息集合信息
   */
  List<SysUser> selectUserList(SysUserQuery query);

  /**
   * 导入用户数据
   *
   * @param userList 用户数据列表
   * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
   * @param operName 操作用户
   * @return 结果
   */
  String importUser(List<SysUser> userList, Boolean isUpdateSupport, String operName);

  /**
   * 根据条件分页查询已分配用户角色列表
   *
   * @param query 查询参数对象
   * @return 分页用户信息集合信息
   */
  IPage<SysUser> selectAllocatedListByPage(SysUserQuery query);

  /**
   * 根据条件分页查询未分配用户角色列表
   *
   * @param query 查询参数对象
   * @return 分页用户信息集合信息
   */
  IPage<SysUser> selectUnallocatedListByPage(SysUserQuery query);
}
