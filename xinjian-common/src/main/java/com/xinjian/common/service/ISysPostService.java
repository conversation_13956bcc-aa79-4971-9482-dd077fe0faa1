package com.xinjian.common.service;

import com.xinjian.common.core.domain.entity.SysPost;
import java.util.List;

/**
 * 系统岗位服务接口
 *
 * <p>定义岗位相关的核心业务操作，主要用于框架层的岗位管理需求
 */
public interface ISysPostService {

  /**
   * 根据岗位ID查询岗位信息
   *
   * @param postId 岗位ID
   * @return 岗位信息
   */
  SysPost selectPostById(Long postId);

  /**
   * 根据岗位编码查询岗位信息
   *
   * @param postCode 岗位编码
   * @return 岗位信息
   */
  SysPost selectPostByCode(String postCode);

  /**
   * 根据用户ID查询岗位列表
   *
   * @param userId 用户ID
   * @return 岗位列表
   */
  List<SysPost> selectPostsByUserId(Long userId);

  /**
   * 根据用户名查询岗位列表
   *
   * @param userName 用户名
   * @return 岗位列表
   */
  List<SysPost> selectPostsByUserName(String userName);

  /**
   * 校验岗位名称是否唯一
   *
   * @param post 岗位信息
   * @return 结果
   */
  boolean checkPostNameUnique(SysPost post);

  /**
   * 校验岗位编码是否唯一
   *
   * @param post 岗位信息
   * @return 结果
   */
  boolean checkPostCodeUnique(SysPost post);

  /**
   * 新增保存岗位信息
   *
   * @param post 岗位信息
   * @return 结果
   */
  int insertPost(SysPost post);

  /**
   * 修改保存岗位信息
   *
   * @param post 岗位信息
   * @return 结果
   */
  int updatePost(SysPost post);

  /**
   * 删除岗位信息
   *
   * @param postId 岗位ID
   * @return 结果
   */
  int deletePostById(Long postId);

  /**
   * 批量删除岗位信息
   *
   * @param postIds 需要删除的岗位ID
   * @return 结果
   */
  int deletePostByIds(Long[] postIds);
}
