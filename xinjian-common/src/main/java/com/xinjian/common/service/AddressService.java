package com.xinjian.common.service;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.xinjian.common.properties.AppProperties;
import java.nio.charset.StandardCharsets;
import java.util.Map;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.converter.StringHttpMessageConverter;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

/** 地址查询服务 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AddressService {

  // IP 地址查询 API (可以考虑也放入配置中)
  private static final String IP_URL = "http://whois.pconline.com.cn/ipJson.jsp";
  // 未知地址
  private static final String UNKNOWN = "XX XX";

  // 1. 通过构造函数注入所有依赖
  private final AppProperties appProperties;
  private final RestTemplate restTemplate;
  private final ObjectMapper objectMapper;

  /**
   * 根据 IP 地址获取真实地理位置
   *
   * @param ip IP 地址
   * @return 地理位置字符串，格式： "省份 城市"
   */
  public String getRealAddressByIP(String ip) {
    // 2. 所有方法都变成实例方法
    // 内网不查询
    if (isInnerIP(ip)) {
      return "内网 IP";
    }
    // 使用注入的 appProperties
    if (appProperties.isAddressEnabled()) {
      try {
        // 3. 使用 RestTemplate 替代 HttpUtils
        //    太平洋这个接口需要 GBK 编码，我们需要特殊处理
        restTemplate
            .getMessageConverters()
            .set(1, new StringHttpMessageConverter(StandardCharsets.ISO_8859_1));
        String url = IP_URL + "?ip=" + ip + "&json=true";
        String gbkResponse = new String(restTemplate.getForObject(url, byte[].class), "GBK");

        if (gbkResponse == null || gbkResponse.trim().isEmpty()) {
          log.error("获取地理位置异常，响应为空。IP: {}", ip);
          return UNKNOWN;
        }

        // 4. 使用注入的 ObjectMapper 替代 JacksonUtil
        Map<String, String> obj =
            objectMapper.readValue(gbkResponse, new TypeReference<Map<String, String>>() {});
        String region = obj.get("pro");
        String city = obj.get("city");
        return String.format("%s %s", region, city);
      } catch (Exception e) {
        log.error("获取地理位置异常，IP: {}", ip, e);
      }
    }
    return UNKNOWN;
  }

  /**
   * 判断是否为内网 IP
   *
   * @param ip IP 地址
   * @return 是否为内网 IP
   */
  private boolean isInnerIP(String ip) {
    if (ip == null || ip.isEmpty()) {
      return false;
    }
    // 简单的内网 IP 段判断，可以根据需要扩展
    return ip.startsWith("10.")
        || ip.startsWith("192.168.")
        || "127.0.0.1".equals(ip)
        || ip.startsWith("172.");
  }

  /**
   * 获取真实地址（兼容旧方法）
   *
   * @param ip IP 地址
   * @return 地理位置字符串
   * @deprecated 请使用 {@link #getRealAddressByIP(String)} 方法
   */
  @Deprecated
  public String getRealAddress(String ip) {
    return getRealAddressByIP(ip);
  }
}
