package com.xinjian.common.service;

import com.xinjian.common.core.domain.entity.SysOperLog;
import com.xinjian.common.core.domain.query.SysOperLogQuery;
import java.util.List;

/**
 * 系统操作日志服务接口
 *
 * <p>定义操作日志相关的核心业务操作，主要用于框架层的日志记录需求
 */
public interface ISysOperLogService {

  /**
   * 新增操作日志
   *
   * @param operLog 操作日志信息
   */
  void insertOperlog(SysOperLog operLog);

  /**
   * 查询系统操作日志集合
   *
   * @param query 查询参数
   * @return 操作日志集合
   */
  List<SysOperLog> selectOperLogList(SysOperLogQuery query);

  /**
   * 批量删除系统操作日志
   *
   * @param operIds 需要删除的操作日志ID
   */
  void deleteOperLogByIds(Long[] operIds);

  /** 清空系统操作日志 */
  void cleanOperLog();
}
