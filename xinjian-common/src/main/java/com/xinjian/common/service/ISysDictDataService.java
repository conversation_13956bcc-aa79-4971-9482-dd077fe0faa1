package com.xinjian.common.service;

import com.xinjian.common.core.domain.entity.SysDictData;
import com.xinjian.common.core.domain.query.SysDictDataQuery;
import java.util.List;

/**
 * 系统字典数据服务接口
 *
 * <p>定义字典数据相关的核心业务操作，主要用于框架层的字典数据管理需求
 */
public interface ISysDictDataService {

  /**
   * 根据字典数据ID查询字典数据信息
   *
   * @param dictCode 字典数据ID
   * @return 字典数据信息
   */
  SysDictData selectDictDataById(Long dictCode);

  /**
   * 根据字典类型和字典键值查询字典数据信息
   *
   * @param dictType 字典类型
   * @param dictValue 字典键值
   * @return 字典数据信息
   */
  SysDictData selectDictDataByTypeAndValue(String dictType, String dictValue);

  /**
   * 根据字典类型查询字典数据信息
   *
   * @param dictType 字典类型
   * @return 字典数据信息
   */
  List<SysDictData> selectDictDataByType(String dictType);

  /**
   * 根据字典类型查询字典数据信息
   *
   * @param dictType 字典类型
   * @param dictStatus 字典状态
   * @return 字典数据信息
   */
  List<SysDictData> selectDictDataByType(String dictType, String dictStatus);

  /**
   * 根据条件查询字典数据列表
   *
   * @param query 查询参数
   * @return 字典数据列表
   */
  List<SysDictData> selectDictDataList(SysDictDataQuery query);

  /**
   * 新增保存字典数据信息
   *
   * @param dictData 字典数据信息
   * @return 结果
   */
  int insertDictData(SysDictData dictData);

  /**
   * 修改保存字典数据信息
   *
   * @param dictData 字典数据信息
   * @return 结果
   */
  int updateDictData(SysDictData dictData);

  /**
   * 批量删除字典数据信息
   *
   * @param dictCodes 需要删除的字典数据ID
   * @return 结果
   */
  int deleteDictDataByIds(Long[] dictCodes);

  /**
   * 删除字典数据信息
   *
   * @param dictCode 字典数据ID
   * @return 结果
   */
  int deleteDictDataById(Long dictCode);

  /**
   * 校验字典键值是否唯一
   *
   * @param dictData 字典数据信息
   * @return 结果
   */
  boolean checkDictDataUnique(SysDictData dictData);
}
