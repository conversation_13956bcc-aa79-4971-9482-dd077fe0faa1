package com.xinjian.common.service;

import com.xinjian.common.core.domain.TreeSelect;
import com.xinjian.common.core.domain.entity.SysMenu;
import com.xinjian.common.core.domain.vo.RouterVo;
import java.util.List;
import java.util.Set;

/**
 * 系统菜单服务接口
 *
 * <p>定义菜单相关的核心业务操作，主要用于权限管理场景
 */
public interface ISysMenuService {

  /**
   * 根据用户ID查询权限
   *
   * @param userId 用户ID
   * @return 权限列表
   */
  Set<String> selectMenuPermsByUserId(Long userId);

  /**
   * 根据角色ID列表查询权限
   *
   * @param roleIds 角色ID列表
   * @return 权限列表
   */
  Set<String> selectMenuPermsByRoleIds(List<Long> roleIds);

  /**
   * 根据用户ID查询菜单树信息
   *
   * @param userId 用户ID
   * @return 菜单列表
   */
  List<SysMenu> selectMenuTreeByUserId(Long userId);

  /**
   * 构建前端所需要菜单
   *
   * @param menus 菜单列表
   * @param permissions 权限字符串
   * @return 路由列表
   */
  List<RouterVo> buildMenus(List<SysMenu> menus, String permissions);

  /**
   * 根据用户查询系统菜单列表
   *
   * @param menu 菜单信息
   * @param userId 用户ID
   * @return 菜单列表
   */
  List<SysMenu> selectMenuList(SysMenu menu, Long userId);

  /**
   * 根据菜单ID查询菜单信息
   *
   * @param menuId 菜单ID
   * @return 菜单信息
   */
  SysMenu selectMenuById(Long menuId);

  /**
   * 构建前端所需要下拉树结构
   *
   * @param menus 菜单列表
   * @return 下拉树结构列表
   */
  List<TreeSelect> buildMenuTreeSelect(List<SysMenu> menus);

  /**
   * 查询系统菜单列表
   *
   * @param userId 用户ID
   * @return 菜单列表
   */
  List<SysMenu> selectMenuList(Long userId);

  /**
   * 根据角色ID查询菜单树信息
   *
   * @param roleId 角色ID
   * @return 菜单ID列表
   */
  List<Long> selectMenuListByRoleId(Long roleId);

  /**
   * 校验菜单名称是否唯一
   *
   * @param menu 菜单信息
   * @return 结果
   */
  boolean checkMenuNameUnique(SysMenu menu);

  /**
   * 新增保存菜单信息
   *
   * @param menu 菜单信息
   * @return 结果
   */
  int insertMenu(SysMenu menu);

  /**
   * 修改保存菜单信息
   *
   * @param menu 菜单信息
   * @return 结果
   */
  int updateMenu(SysMenu menu);

  /**
   * 是否存在子菜单
   *
   * @param menuId 菜单ID
   * @return 结果 true 存在 false 不存在
   */
  boolean hasChildByMenuId(Long menuId);

  /**
   * 查询菜单是否存在角色
   *
   * @param menuId 菜单ID
   * @return 结果 true 存在 false 不存在
   */
  boolean checkMenuExistRole(Long menuId);

  /**
   * 删除菜单管理信息
   *
   * @param menuId 菜单ID
   * @return 结果
   */
  int deleteMenuById(Long menuId);

  /**
   * 查询所有有效的菜单权限标识符
   *
   * @return 所有有效的权限标识符集合
   */
  Set<String> selectAllMenuPerms();
}
