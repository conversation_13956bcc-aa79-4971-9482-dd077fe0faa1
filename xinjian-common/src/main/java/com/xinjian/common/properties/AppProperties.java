package com.xinjian.common.properties;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

/** 应用基础配置属性 */
@Data
@ConfigurationProperties(prefix = "app")
public class AppProperties {

  /** 应用标识符 */
  private String namespace = "demo";

  /** 版本 */
  private String version = "1.0.0";

  /** 版权年份 */
  private String copyrightYear = "2025";

  /** 获取 IP 地址开关 */
  private boolean addressEnabled = false;
}
