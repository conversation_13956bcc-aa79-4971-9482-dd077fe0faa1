package com.xinjian.common.properties;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

/** 用户配置属性 */
@Data
@ConfigurationProperties(prefix = "app.user")
public class UserProperties {

  private Password password = new Password();

  @Data
  public static class Password {
    /** 密码最大错误次数 */
    private int maxRetryCount = 5;

    /** 密码锁定时间（分钟） */
    private int lockTime = 10;
  }
}
