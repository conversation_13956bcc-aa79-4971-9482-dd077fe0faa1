package com.xinjian.common.properties;

import com.xinjian.common.exception.status500.ServiceException;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import javax.annotation.PostConstruct;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.ConfigurationProperties;

/** 存储配置属性 */
@Data
@ConfigurationProperties(prefix = "app.storage")
@Slf4j
public class StorageProperties {

  /** 文件存储物理路径 */
  private String path = "./storage";

  /** 文件访问端点 */
  private String endpoint = "/api/demo/files";

  /** 默认最大文件大小 (单位：MB) */
  private long defaultMaxSize = 50;

  /** 默认的文件名最大长度 */
  private int defaultFileNameLength = 127;

  /** 默认允许上传的文件扩展名列表 */
  private String[] defaultAllowedExtensions = {
    "bmp", "gif", "jpg", "jpeg", "png", "doc", "docx", "xls", "xlsx",
    "ppt", "pptx", "html", "htm", "txt", "rar", "zip", "gz", "bz2",
    "mp4", "avi", "rmvb", "pdf"
  };

  /** 图片扩展名 */
  private String[] imageExtensions = {"bmp", "gif", "jpg", "jpeg", "png"};

  /** Flash 扩展名 */
  private String[] flashExtensions = {"swf", "flv"};

  /** 媒体扩展名 */
  private String[] mediaExtensions = {
    "swf", "flv", "mp3", "wav", "wma", "wmv", "mid", "avi", "mpg", "asf", "rm", "rmvb"
  };

  /** 视频扩展名 */
  private String[] videoExtensions = {"mp4", "avi", "rmvb"};

  /** 存储路径缓存 */
  private volatile String resolvedStoragePath;

  /** 初始化存储路径并创建目录 */
  @PostConstruct
  public void initializeStoragePath() {
    String storagePath = getPath();
    if (storagePath == null || storagePath.trim().isEmpty()) {
      log.warn("存储路径未配置 (app.storage.path)，跳过存储目录初始化");
      return;
    }

    try {
      Path path = Paths.get(storagePath).toAbsolutePath().normalize();
      if (!Files.exists(path)) {
        Files.createDirectories(path);
        log.info("成功创建存储目录：{}", path);
      } else {
        log.debug("存储目录已存在：{}", path);
      }
      this.resolvedStoragePath = path.toString();
      log.info("存储服务初始化完成，存储路径为：{}", this.resolvedStoragePath);
    } catch (IOException e) {
      log.error("创建存储目录失败：{}", e.getMessage(), e);
      throw new ServiceException("初始化存储路径失败：" + e.getMessage());
    }
  }

  /** 获取解析后的存储路径 */
  public String getResolvedStoragePath() {
    return resolvedStoragePath;
  }

  /** 判断存储路径是否为相对路径 */
  public boolean isStoragePathRelative() {
    String storagePath = getPath();
    return storagePath != null && !Paths.get(storagePath).isAbsolute();
  }
}
