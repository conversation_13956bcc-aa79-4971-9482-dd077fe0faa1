package com.xinjian.common.properties;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

/** 认证配置属性 */
@Data
@ConfigurationProperties(prefix = "app.auth")
public class AuthProperties {

  /** 令牌自定义标识 */
  private String tokenHeader = "Authorization";

  /** 令牌前缀 */
  private String tokenPrefix = "Bearer ";

  /** 登录用户 redis key */
  private String loginUserKey = "login_user_key";

  /** 令牌密钥 */
  private String tokenSecret = "V2Vha0tleUV4Y2VwdGlvblRlc3RTZWNyZXRLZXlGb3JIUzUxMkFsZ29yaXRobQ==";

  /** 令牌有效期（分钟） */
  private int tokenExpireTime = 1440;
}
