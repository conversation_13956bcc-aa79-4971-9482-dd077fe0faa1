package com.xinjian.common.properties;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

/** XSS 防护配置属性 */
@Data
@ConfigurationProperties(prefix = "app.xss")
public class XssProperties {

  /** 是否启用 XSS 防护 */
  private boolean enabled = true;

  /** 排除的 URL */
  private String excludes = "/system/notice";

  /** 拦截的 URL 模式 */
  private String urlPatterns = "/system/*,/monitor/*,/tool/*";
}
