package com.xinjian.common.serializer;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.xinjian.common.properties.StorageProperties;
import java.io.IOException;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/** 自动补全文件访问地址 */
@Component
public class FullPathSerializer extends JsonSerializer<String> {

  private StorageProperties storageProperties;

  @Override
  public void serialize(String value, JsonGenerator gen, SerializerProvider serializers)
      throws IOException {
    if (StringUtils.isNotEmpty(value)) {
      // 如果已经是 http 开头，则直接返回
      if (value.startsWith("http")) {
        gen.writeString(value);
        return;
      }
      String endpoint = storageProperties.getEndpoint();
      // 如果 value 已经是 http 开头，或者已经包含了 endpoint, 则直接返回
      if (value.startsWith("http") || value.startsWith(endpoint)) {
        gen.writeString(value);
        return;
      }
      // 健壮地拼接路径，避免双斜杠
      String fullPath =
          (endpoint.endsWith("/") ? endpoint.substring(0, endpoint.length() - 1) : endpoint)
              + "/"
              + (value.startsWith("/") ? value.substring(1) : value);
      gen.writeString(fullPath);
    } else {
      gen.writeString("");
    }
  }

  public FullPathSerializer() {
    // 默认构造函数供 Jackson 使用
  }

  @Autowired
  public void setStorageProperties(StorageProperties storageProperties) {
    this.storageProperties = storageProperties;
  }
}
