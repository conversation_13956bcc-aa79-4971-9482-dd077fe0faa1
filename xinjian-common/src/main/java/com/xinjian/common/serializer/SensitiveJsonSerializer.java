package com.xinjian.common.serializer;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.BeanProperty;
import com.fasterxml.jackson.databind.JsonMappingException;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.fasterxml.jackson.databind.ser.ContextualSerializer;
import com.xinjian.common.annotation.Sensitive;
import com.xinjian.common.core.domain.dto.LoginUser;
import com.xinjian.common.enums.DesensitizedType;
import com.xinjian.common.utils.SecurityUtils;
import java.io.IOException;
import java.util.Objects;

/** 数据脱敏序列化过滤 */
public class SensitiveJsonSerializer extends JsonSerializer<String>
    implements ContextualSerializer {
  private DesensitizedType desensitizedType;

  @Override
  public void serialize(String value, JsonGenerator gen, SerializerProvider serializers)
      throws IOException {
    if (desensitization()) {
      gen.writeString(desensitizedType.desensitizer().apply(value));
    } else {
      gen.writeString(value);
    }
  }

  @Override
  public JsonSerializer<?> createContextual(SerializerProvider prov, BeanProperty property)
      throws JsonMappingException {
    Sensitive annotation = property.getAnnotation(Sensitive.class);
    if (Objects.nonNull(annotation)
        && Objects.equals(String.class, property.getType().getRawClass())) {
      this.desensitizedType = annotation.desensitizedType();
      return this;
    }
    return prov.findValueSerializer(property.getType(), property);
  }

  /** 是否需要脱敏处理 */
  private boolean desensitization() {
    try {
      LoginUser securityUser = SecurityUtils.getLoginUser();
      // 管理员不脱敏
      return !SecurityUtils.isAdminUser(securityUser.getUserId());
    } catch (Exception e) {
      return true;
    }
  }
}
