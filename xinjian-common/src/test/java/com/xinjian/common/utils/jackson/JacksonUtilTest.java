package com.xinjian.common.utils.jackson;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertThrows;

import com.fasterxml.jackson.core.type.TypeReference;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import org.junit.jupiter.api.Test;

class JacksonUtilTest {

  @Test
  void toJSONString_shouldSerializeObject() {
    User user = new User("test", 25);
    String json = JacksonUtil.toJSONString(user);
    assertThat(json).isEqualTo("{\"name\":\"test\",\"age\":25}");
  }

  @Test
  void toJSONString_shouldReturnNullForNullObject() {
    String json = JacksonUtil.toJSONString(null);
    assertThat(json).isNull();
  }

  @Test
  void toJSONString_shouldHandleDateTime() {
    LocalDateTime now = LocalDateTime.of(2025, 7, 30, 10, 30, 0);
    DateTimeHolder holder = new DateTimeHolder(now);
    String json = JacksonUtil.toJSONString(holder);
    assertThat(json).isEqualTo("{\"dateTime\":\"2025-07-30 10:30:00\"}");
  }

  @Test
  void parseObject_shouldDeserializeJsonToClass() {
    String json = "{\"name\":\"test\",\"age\":25}";
    User user = JacksonUtil.parseObject(json, User.class);
    assertThat(user).isNotNull();
    assertThat(user.getName()).isEqualTo("test");
    assertThat(user.getAge()).isEqualTo(25);
  }

  @Test
  void parseObject_shouldReturnNullForNullOrEmptyJson() {
    assertThat(JacksonUtil.parseObject(null, User.class)).isNull();
    assertThat(JacksonUtil.parseObject("", User.class)).isNull();
  }

  @Test
  void parseObject_shouldThrowExceptionForInvalidJson() {
    String invalidJson = "{\"name\":\"test\", \"age\":}";
    assertThrows(JacksonException.class, () -> JacksonUtil.parseObject(invalidJson, User.class));
  }

  @Test
  void parseObject_shouldDeserializeJsonToTypeReference() {
    String json = "{\"key1\":\"value1\",\"key2\":123}";
    Map<String, Object> map =
        JacksonUtil.parseObject(json, new TypeReference<Map<String, Object>>() {});
    assertThat(map).isNotNull();
    assertThat(map).containsEntry("key1", "value1");
    assertThat(map).containsEntry("key2", 123);
  }

  @Test
  void parseArray_shouldDeserializeJsonArrayToList() {
    String json = "[{\"name\":\"user1\",\"age\":20},{\"name\":\"user2\",\"age\":30}]";
    List<User> users = JacksonUtil.parseArray(json, User.class);
    assertThat(users).isNotNull();
    assertThat(users).hasSize(2);
    assertThat(users.get(0).getName()).isEqualTo("user1");
    assertThat(users.get(1).getAge()).isEqualTo(30);
  }

  @Test
  void parseArray_shouldReturnEmptyListForNullOrEmptyJson() {
    assertThat(JacksonUtil.parseArray(null, User.class)).isEmpty();
    assertThat(JacksonUtil.parseArray("", User.class)).isEmpty();
  }

  @Test
  void parseArray_shouldReturnEmptyListForEmptyArray() {
    String json = "[]";
    List<User> users = JacksonUtil.parseArray(json, User.class);
    assertThat(users).isNotNull().isEmpty();
  }

  // Helper classes for testing
  private static class User {
    private String name;
    private int age;

    public User() {}

    public User(String name, int age) {
      this.name = name;
      this.age = age;
    }

    public String getName() {
      return name;
    }

    public int getAge() {
      return age;
    }
  }

  private static class DateTimeHolder {
    private LocalDateTime dateTime;

    public DateTimeHolder() {}

    public DateTimeHolder(LocalDateTime dateTime) {
      this.dateTime = dateTime;
    }

    public LocalDateTime getDateTime() {
      return dateTime;
    }
  }
}
