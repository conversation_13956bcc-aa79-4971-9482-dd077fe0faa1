# `xinjian-common` 模块

## 1. 模块简介

`xinjian-common` 是项目的通用工具模块，提供了被其他模块广泛复用的常量、枚举、工具类、异常处理和过滤器等。

## 2. 核心组件

- **常量定义 (`constant`):** 定义了系统中使用的各种静态常量，如缓存键、HTTP 状态码等。
- **通用异常 (`exception`):** 定义了业务相关的自定义异常，如 `ServiceException`、`UserException` 等，便于全局异常处理器捕获。
- **过滤器 (`filter`):** 包含 XSS 过滤、可重复读请求包装等 Web 过滤器。
- **核心工具类:** 提供了字符串、日期、加密等常用工具。

## 3. 使用方式

此模块通常作为依赖被其他业务模块引入，无需单独运行。在其他模块的 `pom.xml` 中添加 `xinjian-common` 依赖即可使用。
