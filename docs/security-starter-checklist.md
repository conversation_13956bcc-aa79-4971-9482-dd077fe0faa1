# XinJian-Starter-Security 实施检查清单

## 📋 总体进度跟踪

- [ ] **第1天**: 创建xinjian-platform-contract模块
- [ ] **第2-3天**: 创建xinjian-starter-security模块  
- [ ] **第4-5天**: 重构framework模块
- [ ] **第6-7天**: 重构system模块
- [ ] **第8天**: 瘦身common模块
- [ ] **第9-10天**: 集成测试和验证

---

## 🎯 第1天：创建xinjian-platform-contract模块

### ✅ 模块结构创建
- [ ] 创建模块目录结构
- [ ] 创建pom.xml文件
- [ ] 配置正确的依赖关系
- [ ] 更新父pom.xml的modules配置
- [ ] 更新父pom.xml的dependencyManagement配置

### ✅ 契约接口定义
- [ ] 创建UserDetailsServiceContract接口
- [ ] 创建PermissionServiceContract接口
- [ ] 创建AuthUserDTO类
- [ ] 创建其他必要的DTO类
- [ ] 添加必要的验证注解

### ✅ 编译验证
- [ ] 模块可以独立编译成功
- [ ] 无循环依赖警告
- [ ] 接口定义清晰完整

**验证命令**:
```bash
cd xinjian-platform-contract
mvn clean compile
```

---

## 🔧 第2-3天：创建xinjian-starter-security模块

### ✅ 模块结构创建
- [ ] 在xinjian-starters下创建security starter目录
- [ ] 创建完整的包结构
- [ ] 更新xinjian-starters/pom.xml
- [ ] 创建spring.factories文件

### ✅ 依赖配置
- [ ] 配置Spring Boot Security依赖
- [ ] 配置契约模块依赖
- [ ] 配置Redis Starter依赖
- [ ] 配置JWT相关依赖
- [ ] 配置Problem Spring Web依赖

### ✅ 核心类迁移
- [ ] 迁移SecurityConfiguration → XinJianSecurityAutoConfiguration
- [ ] 迁移JwtAuthenticationTokenFilter
- [ ] 迁移TokenService
- [ ] 迁移各种Handler类
- [ ] 迁移SecurityUtils
- [ ] 迁移JwtUtils

### ✅ 配置属性类
- [ ] 创建XinJianSecurityProperties
- [ ] 创建JwtProperties
- [ ] 配置@ConfigurationProperties注解
- [ ] 添加配置元数据

### ✅ 自动配置
- [ ] 创建自动配置类
- [ ] 配置条件注解
- [ ] 配置Bean定义
- [ ] 配置spring.factories

**验证命令**:
```bash
cd xinjian-starters/xinjian-starter-security
mvn clean compile
```

---

## 🔄 第4-5天：重构framework模块

### ✅ 依赖更新
- [ ] 移除直接的spring-boot-starter-security依赖
- [ ] 添加xinjian-starter-security依赖
- [ ] 添加xinjian-platform-contract依赖
- [ ] 验证依赖传递正确

### ✅ 类删除
- [ ] 删除SecurityConfiguration.java
- [ ] 删除JwtAuthenticationTokenFilter.java
- [ ] 删除TokenService.java
- [ ] 删除LogoutSuccessHandlerImpl.java
- [ ] 删除其他已迁移的Handler类

### ✅ UserDetailsServiceImpl重构
- [ ] 重写loadUserByUsername方法
- [ ] 使用UserDetailsServiceContract接口
- [ ] 移除对system模块的直接依赖
- [ ] 保持异常处理逻辑

### ✅ SysLoginService重构
- [ ] 简化登录逻辑
- [ ] 使用契约接口记录登录信息
- [ ] 移除直接的业务依赖
- [ ] 保持核心认证流程

### ✅ 其他服务类调整
- [ ] 调整SysPermissionService（如果需要）
- [ ] 调整其他相关服务类
- [ ] 更新import语句
- [ ] 修复编译错误

**验证命令**:
```bash
cd xinjian-framework
mvn clean compile
```

---

## 🏗️ 第6-7天：重构system模块

### ✅ 依赖添加
- [ ] 添加xinjian-platform-contract依赖
- [ ] 验证依赖版本正确

### ✅ 契约实现
- [ ] 创建UserDetailsServiceContractImpl类
- [ ] 实现loadUserByUsername方法
- [ ] 实现validateUserCredentials方法
- [ ] 实现recordLoginSuccess方法
- [ ] 实现recordLoginFailure方法

### ✅ 权限管理实现
- [ ] 实现PermissionServiceContractImpl类
- [ ] 实现角色权限获取方法
- [ ] 实现菜单权限获取方法
- [ ] 实现权限验证方法

### ✅ 业务逻辑保持
- [ ] 保持原有的用户查询逻辑
- [ ] 保持原有的权限计算逻辑
- [ ] 保持原有的状态验证逻辑
- [ ] 保持原有的登录记录逻辑

**验证命令**:
```bash
cd xinjian-system  # 或相应的system模块目录
mvn clean compile
```

---

## 🧹 第8天：瘦身common模块

### ✅ 依赖清理
- [ ] 移除spring-boot-starter-security依赖
- [ ] 移除其他不必要的框架依赖
- [ ] 保留必要的工具库依赖

### ✅ 类迁移
- [ ] 确认LoginUser已被AuthUserDTO替代
- [ ] 迁移SecurityUtils到starter-security
- [ ] 迁移JwtUtils到starter-security
- [ ] 迁移AuthProperties到starter-security
- [ ] 迁移其他Security相关类

### ✅ 引用更新
- [ ] 更新所有对已迁移类的引用
- [ ] 修复import语句
- [ ] 解决编译错误
- [ ] 验证功能正常

### ✅ 保留内容验证
- [ ] 确认保留的都是纯工具类
- [ ] 确认保留的都是基础组件
- [ ] 确认无业务逻辑耦合
- [ ] 确认模块职责清晰

**验证命令**:
```bash
cd xinjian-common
mvn clean compile
```

---

## 🧪 第9-10天：集成测试和验证

### ✅ 功能测试
- [ ] 用户登录功能正常
- [ ] 用户名/密码错误处理正常
- [ ] 验证码验证正常
- [ ] Token生成和验证正常
- [ ] 角色权限验证正常
- [ ] 菜单权限验证正常
- [ ] 管理员权限验证正常
- [ ] 用户登出功能正常
- [ ] Token失效处理正常

### ✅ 性能测试
- [ ] 登录接口响应时间 < 200ms
- [ ] 权限验证响应时间 < 50ms
- [ ] 支持100并发用户登录
- [ ] 内存使用无显著增加
- [ ] CPU使用无显著增加

### ✅ 架构验证
- [ ] 无循环依赖
- [ ] 模块可独立编译
- [ ] 依赖关系清晰
- [ ] 职责划分明确
- [ ] 自动配置正常生效

### ✅ 安全验证
- [ ] JWT Token安全性验证
- [ ] 权限控制有效性验证
- [ ] 认证流程安全性验证
- [ ] 无安全漏洞引入

**验证命令**:
```bash
# 整体编译验证
mvn clean compile

# 启动应用验证
mvn spring-boot:run

# 运行测试
mvn test
```

---

## 🚨 关键检查点

### 🔴 必须验证的功能
1. **登录流程**: 用户可以正常登录并获取Token
2. **权限验证**: 用户权限验证正常工作
3. **Token管理**: Token生成、验证、刷新正常
4. **登出流程**: 用户可以正常登出

### 🟡 重要验证的功能
1. **异常处理**: 各种异常情况处理正确
2. **配置加载**: 配置属性正确加载和使用
3. **自动配置**: Spring Boot自动配置正常生效
4. **性能表现**: 性能指标符合预期

### 🟢 建议验证的功能
1. **日志记录**: 关键操作日志记录完整
2. **监控指标**: 监控指标正常收集
3. **文档更新**: 相关文档及时更新
4. **代码质量**: 代码质量符合规范

---

## 📊 完成度统计

### 模块创建进度
- [ ] xinjian-platform-contract: 0/3 任务完成
- [ ] xinjian-starter-security: 0/4 任务完成

### 模块重构进度  
- [ ] xinjian-framework: 0/4 任务完成
- [ ] xinjian-system: 0/2 任务完成
- [ ] xinjian-common: 0/3 任务完成

### 测试验证进度
- [ ] 功能测试: 0/9 项完成
- [ ] 性能测试: 0/5 项完成
- [ ] 架构验证: 0/5 项完成
- [ ] 安全验证: 0/4 项完成

### 总体完成度
**进度**: 0% (0/40 项完成)

---

## 🎯 每日目标

### Day 1 目标
完成xinjian-platform-contract模块创建，验证编译通过

### Day 2-3 目标  
完成xinjian-starter-security模块创建，验证自动配置生效

### Day 4-5 目标
完成framework模块重构，验证无循环依赖

### Day 6-7 目标
完成system模块重构，验证契约接口实现正确

### Day 8 目标
完成common模块瘦身，验证模块职责清晰

### Day 9-10 目标
完成所有测试验证，确保功能正常

---

## 📞 问题上报

如果在实施过程中遇到以下情况，请立即上报：

1. **编译失败**: 模块无法编译通过
2. **功能异常**: 登录或权限验证功能异常
3. **性能下降**: 响应时间显著增加
4. **循环依赖**: 出现新的循环依赖问题
5. **配置冲突**: 配置加载或生效异常

**上报方式**: 记录详细错误信息和复现步骤，及时沟通解决方案。
