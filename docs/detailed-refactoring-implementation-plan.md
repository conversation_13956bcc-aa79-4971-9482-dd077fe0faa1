# XinJian项目详细重构实施计划

## 文档信息

- **文档版本**: v1.0
- **创建日期**: 2025-08-22
- **总工期**: 13周
- **团队规模**: 建议3-4人
- **实施策略**: 分阶段渐进式重构

## 1. 第一阶段：安全加固（1周，高优先级）

### 1.1 任务分解

#### 任务1.1：敏感信息安全管理（2天）

**目标**: 消除所有硬编码的敏感信息

**具体步骤**:
1. **识别敏感信息**
   - JWT密钥: `app.auth.token-secret`
   - 数据库密码: `spring.datasource.dynamic.datasource.master.password`
   - Redis密码: `spring.redis.password`
   - 其他第三方服务密钥

2. **环境变量化改造**
   ```yaml
   # application.yml 改造
   app:
     auth:
       token-secret: ${JWT_SECRET:WeakKeyExceptionTestSecretKeyForHS512Algorithm}
   
   spring:
     datasource:
       dynamic:
         datasource:
           master:
             password: ${DB_PASSWORD:default-password}
     redis:
       password: ${REDIS_PASSWORD:}
   ```

3. **配置加密实现**
   - 引入jasypt-spring-boot-starter
   - 配置加密算法和密钥
   - 加密敏感配置项

4. **部署脚本更新**
   ```bash
   # 更新up.sh脚本
   export JWT_SECRET="your-production-jwt-secret"
   export DB_PASSWORD="your-production-db-password"
   export REDIS_PASSWORD="your-production-redis-password"
   ```

**验收标准**:
- 所有配置文件中无硬编码敏感信息
- 环境变量正确加载
- 加密配置正常工作
- 部署脚本支持环境变量注入

#### 任务1.2：XSS防护完善（1天）

**目标**: 修复XSS过滤器只处理POST请求的问题

**具体步骤**:
1. **分析现有XSS过滤器**
   - 检查XssFilter的实现逻辑
   - 识别只处理POST请求的问题

2. **增强XSS过滤器**
   ```java
   @Component
   public class EnhancedXssFilter extends OncePerRequestFilter {
       
       @Override
       protected void doFilterInternal(HttpServletRequest request, 
                                      HttpServletResponse response, 
                                      FilterChain filterChain) {
           
           // 对所有HTTP方法进行XSS防护
           if (requiresXssProtection(request)) {
               request = new XssHttpServletRequestWrapper(request);
           }
           
           filterChain.doFilter(request, response);
       }
       
       private boolean requiresXssProtection(HttpServletRequest request) {
           String method = request.getMethod();
           String uri = request.getRequestURI();
           
           // 检查所有HTTP方法，不仅仅是POST
           return !isExcludedUrl(uri) && 
                  (HttpMethod.GET.matches(method) || 
                   HttpMethod.POST.matches(method) ||
                   HttpMethod.PUT.matches(method) ||
                   HttpMethod.PATCH.matches(method));
       }
   }
   ```

3. **完善XSS验证注解**
   - 增强@Xss注解的验证逻辑
   - 支持更多XSS攻击模式检测

**验收标准**:
- XSS过滤器覆盖所有HTTP方法
- 通过XSS攻击测试用例
- 不影响正常业务功能

#### 任务1.3：CSRF防护机制（1天）

**目标**: 重新评估并实现适合的CSRF防护

**具体步骤**:
1. **评估CSRF防护需求**
   - 分析API使用场景
   - 评估CSRF攻击风险

2. **实现CSRF防护方案**
   ```java
   @Configuration
   public class CsrfConfiguration {
       
       @Bean
       public CsrfTokenRepository csrfTokenRepository() {
           HttpSessionCsrfTokenRepository repository = 
               new HttpSessionCsrfTokenRepository();
           repository.setHeaderName("X-CSRF-TOKEN");
           return repository;
       }
   }
   ```

3. **前端适配**
   - 在前端请求中添加CSRF令牌
   - 处理CSRF验证失败的情况

**验收标准**:
- CSRF防护机制正常工作
- 不影响API正常调用
- 通过CSRF攻击测试

#### 任务1.4：输入验证加强（1天）

**目标**: 实现全面的输入验证机制

**具体步骤**:
1. **建立输入验证框架**
   ```java
   @Component
   public class InputValidationFilter extends OncePerRequestFilter {
       
       @Override
       protected void doFilterInternal(HttpServletRequest request, 
                                      HttpServletResponse response, 
                                      FilterChain filterChain) {
           
           // SQL注入防护
           if (containsSqlInjection(request)) {
               throw new SecurityException("检测到SQL注入攻击");
           }
           
           // 文件上传验证
           if (isFileUpload(request)) {
               validateFileUpload(request);
           }
           
           filterChain.doFilter(request, response);
       }
   }
   ```

2. **完善Bean Validation**
   - 为所有DTO添加验证注解
   - 实现自定义验证器

3. **SQL注入防护**
   - 检查请求参数中的SQL关键字
   - 实现参数化查询检查

**验收标准**:
- 输入验证覆盖所有接口
- 通过SQL注入测试
- 文件上传安全验证正常

### 1.2 第一阶段验收标准

- [ ] 所有敏感信息通过环境变量管理
- [ ] XSS防护覆盖所有HTTP方法
- [ ] CSRF防护机制正常工作
- [ ] 输入验证机制完善
- [ ] 通过安全扫描工具检测
- [ ] 建立安全监控机制

## 2. 第二阶段：架构解耦（3周，高优先级）

### 2.1 任务分解

#### 任务2.1：模块职责重新划分（1周）

**目标**: 解决xinjian-common职责过载问题

**具体步骤**:

**第1天：分析现有依赖**
1. 分析xinjian-common的所有依赖
2. 识别框架相关依赖
3. 制定迁移计划

**第2-3天：创建xinjian-starter-security**
```xml
<!-- xinjian-starter-security/pom.xml -->
<dependencies>
    <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-security</artifactId>
    </dependency>
    <dependency>
        <groupId>io.jsonwebtoken</groupId>
        <artifactId>jjwt-api</artifactId>
    </dependency>
</dependencies>
```

**第4-5天：迁移安全相关配置**
- 将SecurityConfiguration移至starter-security
- 迁移JWT相关工具类
- 迁移认证相关组件

**验收标准**:
- xinjian-starter-security模块创建完成
- 安全相关功能正常工作
- xinjian-common依赖减少

#### 任务2.2：打破循环依赖（1周）

**目标**: 解除framework与system模块间的循环依赖

**具体步骤**:

**第1-2天：创建契约模块**
```java
// xinjian-platform-contract模块
public interface UserDetailsServiceContract {
    AuthUserDTO loadUserByUsername(String username);
    boolean validateUser(String username, String password);
    void updateLastLoginTime(String username);
}

@Data
public class AuthUserDTO {
    private String username;
    private String password;
    private Collection<String> authorities;
    private boolean enabled;
    private boolean accountNonExpired;
    private boolean credentialsNonExpired;
    private boolean accountNonLocked;
}
```

**第3-4天：改造framework模块**
```java
// xinjian-framework模块
@Service
public class JwtUserDetailsService implements UserDetailsService {
    
    @Autowired
    private UserDetailsServiceContract userDetailsContract;
    
    @Override
    public UserDetails loadUserByUsername(String username) {
        AuthUserDTO authUser = userDetailsContract.loadUserByUsername(username);
        return new User(authUser.getUsername(), authUser.getPassword(), 
                       authUser.getAuthorities());
    }
}
```

**第5天：改造system模块**
- 实现UserDetailsServiceContract接口
- 移除对framework的直接依赖
- 调整依赖关系

**验收标准**:
- 消除framework与system的循环依赖
- 契约接口正常工作
- 认证功能不受影响

#### 任务2.3：完善模块化Starters（1周）

**目标**: 建立完整的Starter体系

**具体步骤**:

**第1-2天：完善xinjian-starter-mybatis**
- 优化MyBatis配置
- 添加分页插件配置
- 实现数据源切换

**第3-4天：完善xinjian-starter-redis**
- 优化Redis配置
- 实现多级缓存
- 添加缓存监控

**第5天：创建xinjian-starter-web**
- 迁移Web相关配置
- 统一异常处理
- CORS配置

**验收标准**:
- 所有Starter模块功能完善
- 配置灵活可定制
- 文档完整

### 2.2 第二阶段验收标准

- [ ] 消除所有循环依赖
- [ ] 模块职责清晰明确
- [ ] 依赖关系图简洁清晰
- [ ] 模块可独立测试和部署
- [ ] Starter体系完善

## 3. 第三阶段：性能优化（2周，中优先级）

### 3.1 任务分解

#### 任务3.1：数据库连接池优化（3天）

**目标**: 优化HikariCP配置，提高并发处理能力

**具体步骤**:

**第1天：性能基准测试**
- 建立性能测试环境
- 测试当前连接池性能
- 记录基准数据

**第2天：连接池配置优化**
```yaml
spring:
  datasource:
    dynamic:
      hikari:
        # 连接池大小优化
        minimum-idle: 20
        maximum-pool-size: 100
        # 超时配置优化
        connection-timeout: 10000
        idle-timeout: 300000
        max-lifetime: 1800000
        # 性能优化参数
        data-source-properties:
          cachePrepStmts: true
          prepStmtCacheSize: 500
          prepStmtCacheSqlLimit: 2048
          useServerPrepStmts: true
          rewriteBatchedStatements: true
```

**第3天：性能验证**
- 进行压力测试
- 对比优化前后性能
- 调整配置参数

**验收标准**:
- 并发处理能力提升3倍以上
- 连接池使用率合理
- 无连接泄漏问题

#### 任务3.2：Redis缓存优化（3天）

**目标**: 优化Redis配置，实现多级缓存

**具体步骤**:

**第1天：Redis连接池优化**
```yaml
spring:
  redis:
    lettuce:
      pool:
        min-idle: 10
        max-idle: 50
        max-active: 100
        max-wait: 5000ms
    timeout: 2000ms
```

**第2天：多级缓存实现**
```java
@Configuration
public class CacheConfiguration {
    
    @Bean
    public CacheManager cacheManager() {
        RedisCacheManager.Builder builder = RedisCacheManager
            .RedisCacheManagerBuilder
            .fromConnectionFactory(connectionFactory)
            .cacheDefaults(cacheConfiguration());
        
        return builder.build();
    }
    
    private RedisCacheConfiguration cacheConfiguration() {
        return RedisCacheConfiguration.defaultCacheConfig()
            .entryTtl(Duration.ofHours(1))
            .serializeKeysWith(RedisSerializationContext.SerializationPair
                .fromSerializer(new StringRedisSerializer()))
            .serializeValuesWith(RedisSerializationContext.SerializationPair
                .fromSerializer(new GenericJackson2JsonRedisSerializer()));
    }
}
```

**第3天：缓存策略优化**
- 实现缓存预热
- 优化缓存失效策略
- 添加缓存监控

**验收标准**:
- Redis性能提升明显
- 多级缓存正常工作
- 缓存命中率提升

#### 任务3.3：JVM参数调优（2天）

**目标**: 优化JVM配置，提升系统稳定性

**具体步骤**:

**第1天：JVM参数分析**
- 分析当前JVM配置
- 监控GC性能
- 识别优化点

**第2天：参数优化**
```bash
# 优化后的JVM参数
JAVA_OPTS="-Xms2g -Xmx4g \
  -XX:+UseG1GC \
  -XX:MaxGCPauseMillis=200 \
  -XX:+UnlockExperimentalVMOptions \
  -XX:+UseStringDeduplication \
  -XX:+PrintGCDetails \
  -XX:+PrintGCTimeStamps"
```

**验收标准**:
- GC性能优化明显
- 内存使用合理
- 系统稳定性提升

#### 任务3.4：SQL查询优化（4天）

**目标**: 优化数据库查询性能

**具体步骤**:

**第1天：慢查询分析**
- 开启慢查询日志
- 分析慢查询语句
- 识别优化点

**第2-3天：查询优化**
- 优化复杂查询语句
- 添加必要索引
- 重构N+1查询问题

**第4天：查询缓存实现**
```java
@Service
public class UserService {
    
    @Cacheable(value = "users", key = "#userId")
    public UserDTO getUserById(Long userId) {
        return userMapper.selectUserById(userId);
    }
    
    @CacheEvict(value = "users", key = "#user.id")
    public void updateUser(UserDTO user) {
        userMapper.updateUser(user);
    }
}
```

**验收标准**:
- 查询性能提升明显
- 索引使用合理
- 缓存命中率高

### 3.2 第三阶段验收标准

- [ ] 系统并发处理能力提升3-5倍
- [ ] 响应时间显著改善
- [ ] 建立完整的性能监控体系
- [ ] 资源利用率优化20%以上
- [ ] 系统稳定性明显提升

## 4. 实施注意事项

### 4.1 风险控制

1. **备份策略**
   - 每个阶段开始前进行完整备份
   - 建立代码版本控制分支策略
   - 准备快速回滚方案

2. **测试策略**
   - 每个任务完成后进行功能测试
   - 定期进行集成测试
   - 建立自动化测试流程

3. **监控告警**
   - 实时监控系统性能指标
   - 建立异常告警机制
   - 定期检查系统健康状态

### 4.2 团队协作

1. **角色分工**
   - 项目负责人：整体协调和进度控制
   - 开发工程师：具体功能开发和重构
   - 测试工程师：测试用例编写和执行
   - 运维工程师：环境部署和监控

2. **沟通机制**
   - 每日站会：同步进度和问题
   - 周例会：回顾总结和计划调整
   - 代码审查：保证代码质量
   - 技术分享：提升团队能力

### 4.3 质量保证

1. **代码质量**
   - 统一编码规范和标准
   - 使用SonarQube等静态分析工具
   - 建立代码审查流程
   - 保证核心逻辑测试覆盖

2. **文档管理**
   - 及时更新技术文档
   - 维护API文档
   - 记录重构过程和决策
   - 建立知识库

## 5. 总结

本实施计划详细规划了XinJian项目重构的前三个阶段，总计6周时间。通过系统性的安全加固、架构解耦和性能优化，项目将在安全性、可维护性和性能方面得到显著提升。

建议严格按照计划执行，确保每个阶段的质量目标达成，为后续的代码质量提升和技术栈现代化奠定坚实基础。
