# 项目模块化与解耦重构计划

## 总体目标

打破 `xinjian-common`, `xinjian-framework`, `xinjian-system` 之间的强耦合，基于目标架构重新建立清晰的模块边界和单向依赖关系，为项目的长期健康演进奠定基础。

---

## 1. 技术基础层 (Starters)

### 层次目标

此层次的目标是将所有与特定技术栈（如 Redis, MyBatis, Security, Web）相关的 Spring Boot 自动化配置和客户端工具类封装成独立的 `starter` 模块。应用模块可以按需引入，实现技术依赖的“即插即用”，并保持上层业务代码的纯净性。

### 核心改造步骤
### 步骤 1.1：创建 `xinjian-starter-redis` 模块

-   **操作**：
    1.  创建新的 Maven 模块 `xinjian-starter-redis`。
    2.  将 `xinjian-common` 中的 `RedisConfig` 类、`RedisClient` 工具类以及 `spring-boot-starter-data-redis` 依赖项，全部迁移至 `xinjian-starter-redis`。
    3.  在 `xinjian-starter-redis` 中创建 `spring.factories` 或 `@AutoConfiguration` 文件，使其成为一个标准的 Spring Boot Starter。
    4. 注意 `CacheConstants` 不要迁移到新模块中，任何涉及到业务的都不允许放入 starter。
    `xinjian-starter-redis` 的职责是提供一个通用的、强大的Redis客户端工具（RedisClient）。它关心的是“如何”与Redis交互（如何设置前缀、如何序列化、如何提供分布式锁），但它绝对不应该关心你往Redis里存的是“什么”。
-   **验证标准**：
    -   `xinjian-starter-redis` 模块可以独立编译成功。
    -   原 `xinjian-common` 中与 Redis 相关的内容已被完全移除。
    -   在 `xinjian-admin` 等应用模块中，移除对 `xinjian-common` 的 Redis 相关功能的依赖，转而直接依赖 `xinjian-starter-redis`，项目能够成功编译并运行。
### 步骤 1.2：创建 `xinjian-starter-web` 模块

-   **操作**：
    1.  创建新的 Maven 模块 `xinjian-starter-web`。
    2.  将 `xinjian-common` 中与 Web 相关的类，如 `PageDomain`, `TableDataInfo`, `TableSupport`，以及 `spring-boot-starter-web` 和 `pagehelper-spring-boot-starter` 的依赖迁移至此模块。
-   **验证标准**：
    -   `xinjian-starter-web` 模块可以独立编译成功。
    -   `xinjian-admin` 依赖 `xinjian-starter-web` 后，分页和 Web 相关功能可正常使用。
### 步骤 1.3：(以此类推) 创建其他 `starter` 模块

-   **操作**：
    -   参照上述步骤，继续创建 `xinjian-starter-security`, `xinjian-starter-mybatis` 等模块。
    -   将 `xinjian-common` 和 `xinjian-framework` 中对应的功能（如安全工具类、数据源配置等）逐步迁移。
-   **验证标准**：
    -   每个新的 `starter` 都能独立编译。
    -   `xinjian-common` 和 `xinjian-framework` 的体积和职责逐渐减少。
    -   应用模块按需引入新的 `starter` 后，项目可正常编译运行。

---

## 2. 框架通用层 (Common)

### 层次目标

此层次的目标是**精简 `xinjian-common` 模块**，使其成为一个纯粹的、不依赖任何特定业务框架（尤其是 Spring）的通用工具包。它将只保留整个项目共享的、**与框架无关的**核心定义，例如：常量、枚举、核心实体基类（`BaseEntity`）、通用异常（`BusinessException`）以及与业务无关的纯工具类（如字符串、日期、集合操作等）。所有与具体技术栈（如Web, Redis, Security）相关的功能都将被剥离到各自的 `starter` 模块中。

### 核心改造步骤
### 步骤 2.1：审视并清理 `xinjian-framework`

-   **操作**：
    1.  全面审查 `xinjian-framework` 中剩余的代码。
    2.  将所有与具体业务（如 `admin` 模块）相关的逻辑、配置或工具类，移动到对应的业务模块或 `starter` 中。
-   **验证标准**：
    -   `xinjian-framework` 中只保留真正通用的框架级功能，如全局异常处理、AOP 日志、通用的 Web 配置等。
    -   项目可正常编译运行。

---

## 3. 平台通用服务契约 (Platform Contract)

### 层次目标

此层次定义了平台级通用能力的接口契约，例如文件存储、消息通知、用户认证等。它作为上层业务模块与下层平台能力实现之间的桥梁，通过面向接口编程，解除业务逻辑与具体技术实现的耦合。

### 核心改造步骤
### 步骤 3.1：创建 `xinjian-platform-contract` 模块

-   **操作**：
    1.  创建新的 Maven 模块 `xinjian-platform-contract`。
    2.  在该模块中定义 `UserDetailsServiceContract` 接口，包含 `loadUserByUsername` 方法。
    3.  定义方法返回的 DTO 对象 `AuthUserDTO`，此 DTO 只包含认证授权所需的最小字段集（用户ID、用户名、密码、权限等）。
-   **验证标准**：
    -   `xinjian-platform-contract` 可独立编译，且不依赖项目中任何其他业务或框架模块。
### 步骤 3.2：改造 `xinjian-framework`，使其依赖契约

-   **操作**：
    1.  修改 `xinjian-framework` 的 `pom.xml`，移除对 `xinjian-system` 的依赖，添加对 `xinjian-platform-contract` 的依赖。
    2.  修改 `framework` 中的 `UserDetailsServiceImpl`（或类似实现），使其不再注入和调用 `ISysUserService`，而是注入 `UserDetailsServiceContract`。
    3.  代码中所有使用到 `SysUser` 实体的地方，全部改为使用 `AuthUserDTO`。
-   **验证标准**：
    -   `xinjian-framework` 模块可以成功编译。
    -   此时项目整体会因为 `xinjian-system` 缺少 `UserDetailsServiceContract` 的实现而无法启动，这是预期现象。
### 步骤 3.3：改造 `xinjian-system`，使其实现契约

-   **操作**：
    1.  修改 `xinjian-system` 的 `pom.xml`，添加对 `xinjian-platform-contract` 的依赖。
    2.  在 `xinjian-system` 模块中，创建一个新的类 `UserDetailsContractImpl`，实现 `UserDetailsServiceContract` 接口。
    3.  在该实现类中，注入 `ISysUserService`，调用其方法获取 `SysUser` 实体，然后将其转换为 `AuthUserDTO` 并返回。
-   **验证标准**：
    -   项目能够成功编译并启动。
    -   用户登录、权限验证等核心功能可正常使用。
    -   模块间的依赖关系变为单向：`system` -> `framework` -> `platform-contract`，循环依赖被彻底打破。

---

## 4. 业务与平台能力实现层 (Modules)

### 层次目标

此层次包含所有具体的业务逻辑实现和平台能力的实现。业务模块（如 `admin`, `portal`）应按照 `contract` 和 `service` 的模式进行划分，实现接口与实现的分离。平台能力模块（如 `storage`, `notification`）则负责实现 `Platform Contract` 中定义的接口。

### 核心改造步骤
### 步骤 4.1：拆分 `xinjian-system` 为 `contract` 与 `service`

-   **操作**：
    1.  创建新的 Maven 模块 `xinjian-module-admin-contract`。
    2.  将 `xinjian-system` 中所有的 Service 接口（如 `ISysUserService`）、DTO、VO 等移动到 `admin-contract` 模块。
    3.  将原 `xinjian-system` 模块重命名为 `xinjian-module-admin-service`。
    4.  调整 `admin-service` 模块的 `pom.xml`，使其依赖 `admin-contract`。
-   **验证标准**：
    -   `admin-contract` 和 `admin-service` 模块均可成功编译。
    -   `xinjian-admin` 应用模块调整对 `admin-service` 的依赖后，项目可正常编译运行。

---

## 5. 应用启动与网关层 (Apps & Gateway)

### 层次目标

此层次是项目的最终交付形态。每个 `app` 模块是一个可独立部署的 Spring Boot 应用程序（部署单元），它负责组装所有需要的业务模块和平台模块，并包含完整的配置文件。API 网关（`Gateway`）作为所有请求的统一入口，负责路由、安全认证、限流、熔断等全局策略。

### 核心改造步骤

（当前阶段主要关注核心库的解耦，应用层的组装和网关的配置将在核心重构完成后进行。）

---

## 最终状态

完成以上所有步骤后，项目将形成一个清晰的、自下而上单向依赖的分层结构，模块间依赖关系合理，为后续并行开发、独立部署和技术升级奠定坚实的基础。
