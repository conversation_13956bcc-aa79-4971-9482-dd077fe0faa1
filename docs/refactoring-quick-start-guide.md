# XinJian项目重构快速启动指南

## 🚀 立即行动清单

### 第一优先级：安全漏洞修复（立即执行）

#### 1. JWT密钥环境变量化（30分钟）

```bash
# 1. 生成新的JWT密钥
openssl rand -base64 64

# 2. 修改application.yml
# 将硬编码密钥改为：
app:
  auth:
    token-secret: ${JWT_SECRET:WeakKeyExceptionTestSecretKeyForHS512Algorithm}

# 3. 设置环境变量
export JWT_SECRET="your-new-generated-secret-key"
```

#### 2. 数据库密码环境变量化（15分钟）

```yaml
# application-dev.yml / application-production.yml
spring:
  datasource:
    dynamic:
      datasource:
        master:
          password: ${DB_PASSWORD:default-password}
```

```bash
# 设置环境变量
export DB_PASSWORD="your-production-db-password"
```

#### 3. XSS防护修复（1小时）

```java
// 修改 XssFilter.java
@Override
public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain) {
    HttpServletRequest req = (HttpServletRequest) request;
    
    // 修复：对所有HTTP方法进行XSS防护，不仅仅是POST
    if (!handleExcludeURL(req, (HttpServletResponse) response)) {
        XssHttpServletRequestWrapper xssRequest = 
            new XssHttpServletRequestWrapper(req);
        chain.doFilter(xssRequest, response);
    } else {
        chain.doFilter(request, response);
    }
}
```

### 第二优先级：性能配置优化（2小时）

#### 1. 数据库连接池优化

```yaml
# application.yml
spring:
  datasource:
    dynamic:
      hikari:
        minimum-idle: 20          # 从10提升到20
        maximum-pool-size: 50     # 从10提升到50
        connection-timeout: 10000 # 从30000降低到10000
        idle-timeout: 300000      # 5分钟
        max-lifetime: 1800000     # 30分钟
```

#### 2. Redis连接池优化

```yaml
# application.yml
spring:
  redis:
    lettuce:
      pool:
        min-idle: 5      # 从0提升到5
        max-idle: 20     # 从8提升到20
        max-active: 50   # 从8提升到50
        max-wait: 5000ms # 从-1ms改为5000ms
```

#### 3. JVM参数优化

```bash
# 修改up.sh
memorySize="2g"  # 从512m提升到2g

# 添加GC优化参数
JAVA_OPTS="-Xms${memorySize} -Xmx${memorySize} \
  -XX:+UseG1GC \
  -XX:MaxGCPauseMillis=200 \
  -XX:+UnlockExperimentalVMOptions \
  -XX:+UseStringDeduplication"

java $JAVA_OPTS -jar $newJar
```

## 📋 完整重构路线图

### 阶段1：安全加固（1周）
- [x] 敏感信息环境变量化
- [x] XSS防护完善
- [x] CSRF防护评估
- [x] 输入验证加强

### 阶段2：架构解耦（3周）
- [ ] xinjian-common模块瘦身
- [ ] 创建platform-contract模块
- [ ] 打破framework-system循环依赖
- [ ] 完善Starter模块体系

### 阶段3：性能优化（2周）
- [ ] 数据库连接池优化
- [ ] Redis缓存策略优化
- [ ] JVM参数调优
- [ ] SQL查询优化

### 阶段4：代码质量提升（3周）
- [ ] 建立测试体系（目标80%覆盖率）
- [ ] 统一异常处理机制
- [ ] 消除代码重复
- [ ] 完善代码规范

### 阶段5：技术栈现代化（4周）
- [ ] Java 8 → Java 17升级
- [ ] Spring Boot 2.7 → 3.x升级
- [ ] 依赖版本更新
- [ ] 现代化开发实践应用

## 🔧 关键技术决策

### 1. 模块依赖关系重构

**当前问题**:
```
xinjian-framework ←→ xinjian-system (循环依赖)
```

**目标架构**:
```
xinjian-system → xinjian-framework → xinjian-platform-contract
```

**实施方案**:
1. 创建`xinjian-platform-contract`模块
2. 定义`UserDetailsServiceContract`接口
3. framework依赖contract，system实现contract

### 2. 安全配置标准化

**JWT配置**:
```yaml
app:
  auth:
    token-secret: ${JWT_SECRET}
    token-expire-time: ${JWT_EXPIRE_TIME:1440}
    token-header: ${JWT_HEADER:Authorization}
    token-prefix: ${JWT_PREFIX:Bearer}
```

**密码策略**:
```yaml
app:
  user:
    password:
      max-retry-count: ${PASSWORD_MAX_RETRY:5}
      lock-time: ${PASSWORD_LOCK_TIME:10}
      min-length: ${PASSWORD_MIN_LENGTH:8}
```

### 3. 缓存策略设计

**多级缓存配置**:
```yaml
xinjian:
  redis:
    default-ttl: 1h
    specs:
      users: 30m        # 用户信息缓存30分钟
      permissions: 2h   # 权限信息缓存2小时
      dictionaries: 24h # 字典数据缓存24小时
      sessions: 1h      # 会话信息缓存1小时
```

## 📊 质量度量指标

### 安全指标
- [ ] 安全漏洞数量: 0个
- [ ] 敏感信息硬编码: 0个
- [ ] 安全扫描通过率: 100%

### 性能指标
- [ ] 平均响应时间: <200ms
- [ ] 95%响应时间: <500ms
- [ ] 并发用户数: 1000+
- [ ] 数据库连接池使用率: <80%

### 代码质量指标
- [ ] 测试覆盖率: >80%
- [ ] 代码重复率: <5%
- [ ] 代码复杂度: 平均<10
- [ ] SonarQube质量门禁: 通过

### 架构健康度指标
- [ ] 循环依赖数量: 0个
- [ ] 模块耦合度: <30%
- [ ] 接口稳定性: >95%
- [ ] 部署成功率: >99%

## 🛠️ 工具和环境准备

### 开发工具
```bash
# 代码质量检查
mvn sonar:sonar

# 安全扫描
mvn org.owasp:dependency-check-maven:check

# 代码格式化
mvn spotless:apply

# 测试覆盖率
mvn jacoco:report
```

### 监控工具
- **性能监控**: Micrometer + Prometheus + Grafana
- **日志监控**: ELK Stack
- **应用监控**: Spring Boot Actuator
- **数据库监控**: Druid监控页面

### 测试工具
- **单元测试**: JUnit 5 + Mockito
- **集成测试**: TestContainers
- **性能测试**: JMeter
- **安全测试**: OWASP ZAP

## 🚨 风险控制措施

### 1. 备份策略
```bash
# 数据库备份
mysqldump -u username -p database_name > backup_$(date +%Y%m%d).sql

# 代码备份
git tag -a v1.0-before-refactoring -m "重构前版本备份"
git push origin v1.0-before-refactoring
```

### 2. 回滚方案
```bash
# 快速回滚脚本
#!/bin/bash
echo "开始回滚到重构前版本..."
git checkout v1.0-before-refactoring
mvn clean package -DskipTests
./up.sh
echo "回滚完成"
```

### 3. 监控告警
```yaml
# application.yml - 监控配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: always
  metrics:
    export:
      prometheus:
        enabled: true
```

## 📚 参考文档

### 内部文档
- [完整分析报告](./comprehensive-code-quality-analysis-report.md)
- [详细实施计划](./detailed-refactoring-implementation-plan.md)
- [架构解耦方案](./core-modules-decoupling-plan.md)

### 外部参考
- [Spring Boot官方文档](https://docs.spring.io/spring-boot/docs/current/reference/html/)
- [Spring Security最佳实践](https://docs.spring.io/spring-security/reference/)
- [MyBatis Plus官方文档](https://baomidou.com/pages/24112f/)
- [Java 17新特性](https://docs.oracle.com/en/java/javase/17/language/)

## 🎯 下一步行动

1. **立即执行**安全漏洞修复（今天完成）
2. **本周内完成**性能配置优化
3. **制定详细计划**架构解耦实施方案
4. **建立监控体系**跟踪重构进度和效果
5. **组织团队培训**确保团队理解重构目标和方案

---

**重要提醒**: 
- 每个阶段完成后都要进行充分测试
- 保持与业务团队的密切沟通
- 及时记录重构过程中的问题和解决方案
- 建立代码审查机制确保重构质量
