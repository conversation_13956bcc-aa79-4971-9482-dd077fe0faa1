



# XinJian-Starter-Security 具体实施步骤

## 实施概览

- **总工期**: 10天
- **风险等级**: 高（涉及核心安全功能）
- **前置条件**: 完成安全漏洞修复
- **验证策略**: 每步完成后进行功能验证

## 第1天：创建xinjian-platform-contract模块

### 任务1.1：创建模块结构（2小时）

```bash
# 1. 创建模块目录
mkdir -p xinjian-platform-contract/src/main/java/com/xinjian/platform/contract
mkdir -p xinjian-platform-contract/src/main/resources

# 2. 创建pom.xml
```

```xml
<!-- xinjian-platform-contract/pom.xml -->
<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 
         http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>xinjian</artifactId>
        <groupId>com.xinjian</groupId>
        <version>1.0.0</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>xinjian-platform-contract</artifactId>
    <description>XinJian平台契约模块，定义模块间的接口契约</description>

    <dependencies>
        <!-- Spring Security Core -->
        <dependency>
            <groupId>org.springframework.security</groupId>
            <artifactId>spring-security-core</artifactId>
        </dependency>
        
        <!-- Jackson Annotations -->
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-annotations</artifactId>
        </dependency>
        
        <!-- Lombok -->
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <scope>provided</scope>
        </dependency>
        
        <!-- Validation API -->
        <dependency>
            <groupId>javax.validation</groupId>
            <artifactId>validation-api</artifactId>
        </dependency>
    </dependencies>
</project>
```

### 任务1.2：定义契约接口（4小时）

```java
// UserDetailsServiceContract.java
package com.xinjian.platform.contract.security;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 用户详情服务契约接口
 * 定义framework层与system层之间的用户认证契约
 */
public interface UserDetailsServiceContract {
    
    /**
     * 根据用户名加载用户信息
     */
    AuthUserDTO loadUserByUsername(@NotBlank String username);
    
    /**
     * 验证用户凭证
     */
    boolean validateUserCredentials(@NotBlank String username, @NotBlank String password);
    
    /**
     * 记录登录成功信息
     */
    void recordLoginSuccess(@NotNull Long userId, @NotBlank String ipAddress);
    
    /**
     * 记录登录失败信息
     */
    void recordLoginFailure(@NotBlank String username, @NotBlank String reason);
}
```

```java
// AuthUserDTO.java
package com.xinjian.platform.contract.security.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.userdetails.UserDetails;

import java.util.Collection;
import java.util.Collections;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 认证用户数据传输对象
 * 实现UserDetails接口，用于Spring Security认证
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AuthUserDTO implements UserDetails {
    
    /** 用户ID */
    private Long userId;
    
    /** 部门ID */
    private Long deptId;
    
    /** 用户名 */
    private String username;
    
    /** 密码 */
    private String password;
    
    /** 账户是否启用 */
    private boolean enabled;
    
    /** 账户是否未过期 */
    private boolean accountNonExpired;
    
    /** 账户是否未锁定 */
    private boolean accountNonLocked;
    
    /** 凭证是否未过期 */
    private boolean credentialsNonExpired;
    
    /** 权限集合 */
    private Set<String> authorities;
    
    @Override
    @JsonIgnore
    public Collection<? extends GrantedAuthority> getAuthorities() {
        if (authorities == null || authorities.isEmpty()) {
            return Collections.emptySet();
        }
        return authorities.stream()
                .map(SimpleGrantedAuthority::new)
                .collect(Collectors.toSet());
    }
    
    @Override
    public boolean isAccountNonExpired() {
        return accountNonExpired;
    }
    
    @Override
    public boolean isAccountNonLocked() {
        return accountNonLocked;
    }
    
    @Override
    public boolean isCredentialsNonExpired() {
        return credentialsNonExpired;
    }
    
    @Override
    public boolean isEnabled() {
        return enabled;
    }
}
```

### 任务1.3：更新父pom.xml（1小时）

```xml
<!-- 在根pom.xml的modules中添加 -->
<modules>
    <module>xinjian-admin</module>
    <module>xinjian-common</module>
    <module>xinjian-framework</module>
    <module>xinjian-generator</module>
    <module>xinjian-quartz</module>
    <module>xinjian-starters</module>
    <module>xinjian-modules</module>
    <module>xinjian-platform-contract</module> <!-- 新增 -->
</modules>

<!-- 在dependencyManagement中添加 -->
<dependency>
    <groupId>com.xinjian</groupId>
    <artifactId>xinjian-platform-contract</artifactId>
    <version>${xinjian.version}</version>
</dependency>
```

### 验收标准
- [x] xinjian-platform-contract模块可以独立编译成功
- [x] 契约接口定义清晰，无循环依赖
- [x] AuthUserDTO实现UserDetails接口正确

## 第2-3天：创建xinjian-starter-security模块

### 任务2.1：创建模块结构（1小时）

```bash
# 1. 在xinjian-starters下创建security starter
mkdir -p xinjian-starters/xinjian-starter-security/src/main/java/com/xinjian/starter/security
mkdir -p xinjian-starters/xinjian-starter-security/src/main/resources/META-INF

# 2. 更新xinjian-starters/pom.xml
```

```xml
<!-- xinjian-starters/pom.xml 添加模块 -->
<modules>
    <module>xinjian-starter-redis</module>
    <module>xinjian-starter-mybatis</module>
    <module>xinjian-starter-security</module> <!-- 新增 -->
</modules>
```

### 任务2.2：配置依赖（1小时）

```xml
<!-- xinjian-starter-security/pom.xml -->
<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 
         http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>xinjian-starters</artifactId>
        <groupId>com.xinjian</groupId>
        <version>1.0.0</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>xinjian-starter-security</artifactId>
    <description>XinJian Security Starter，提供安全认证的自动配置</description>

    <dependencies>
        <!-- Spring Boot -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-security</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-autoconfigure</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-configuration-processor</artifactId>
            <optional>true</optional>
        </dependency>
        
        <!-- 契约模块 -->
        <dependency>
            <groupId>com.xinjian</groupId>
            <artifactId>xinjian-platform-contract</artifactId>
        </dependency>
        
        <!-- Redis Starter -->
        <dependency>
            <groupId>com.xinjian</groupId>
            <artifactId>xinjian-starter-redis</artifactId>
        </dependency>
        
        <!-- JWT -->
        <dependency>
            <groupId>io.jsonwebtoken</groupId>
            <artifactId>jjwt-api</artifactId>
        </dependency>
        <dependency>
            <groupId>io.jsonwebtoken</groupId>
            <artifactId>jjwt-impl</artifactId>
            <scope>runtime</scope>
        </dependency>
        <dependency>
            <groupId>io.jsonwebtoken</groupId>
            <artifactId>jjwt-jackson</artifactId>
            <scope>runtime</scope>
        </dependency>
        
        <!-- Problem Spring Web -->
        <dependency>
            <groupId>org.zalando</groupId>
            <artifactId>problem-spring-web-starter</artifactId>
        </dependency>
        
        <!-- Hutool -->
        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-core</artifactId>
        </dependency>
        
        <!-- Lombok -->
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <scope>provided</scope>
        </dependency>
    </dependencies>
</project>
```

### 任务2.3：迁移核心配置类（6小时）

**从xinjian-framework迁移到xinjian-starter-security**：

1. **SecurityConfiguration.java** → **XinJianSecurityAutoConfiguration.java**
2. **JwtAuthenticationTokenFilter.java** → **filter/JwtAuthenticationTokenFilter.java**
3. **TokenService.java** → **service/TokenService.java**
4. **LogoutSuccessHandlerImpl.java** → **handler/LogoutSuccessHandlerImpl.java**

```java
// XinJianSecurityAutoConfiguration.java
package com.xinjian.starter.security.config;

import com.xinjian.platform.contract.security.UserDetailsServiceContract;
import com.xinjian.starter.security.filter.JwtAuthenticationTokenFilter;
import com.xinjian.starter.security.handler.AccessDeniedHandlerImpl;
import com.xinjian.starter.security.handler.AuthenticationEntryPointImpl;
import com.xinjian.starter.security.handler.LogoutSuccessHandlerImpl;
import com.xinjian.starter.security.properties.XinJianSecurityProperties;
import com.xinjian.starter.security.service.TokenService;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.config.annotation.authentication.configuration.AuthenticationConfiguration;
import org.springframework.security.config.annotation.method.configuration.EnableMethodSecurity;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.annotation.web.configurers.AbstractHttpConfigurer;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;

/**
 * XinJian Security 自动配置类
 */
@Configuration
@EnableWebSecurity
@EnableMethodSecurity(prePostEnabled = true, securedEnabled = true)
@ConditionalOnClass({SecurityFilterChain.class, UserDetailsServiceContract.class})
@EnableConfigurationProperties(XinJianSecurityProperties.class)
public class XinJianSecurityAutoConfiguration {

    @Bean
    @ConditionalOnMissingBean
    public PasswordEncoder passwordEncoder() {
        return new BCryptPasswordEncoder();
    }

    @Bean
    @ConditionalOnMissingBean
    public AuthenticationManager authenticationManager(
            AuthenticationConfiguration authConfig) throws Exception {
        return authConfig.getAuthenticationManager();
    }

    @Bean
    @ConditionalOnMissingBean
    public UserDetailsService userDetailsService(UserDetailsServiceContract contract) {
        return username -> contract.loadUserByUsername(username);
    }

    @Bean
    @ConditionalOnMissingBean
    public JwtAuthenticationTokenFilter jwtAuthenticationTokenFilter(TokenService tokenService) {
        return new JwtAuthenticationTokenFilter(tokenService);
    }

    @Bean
    @ConditionalOnMissingBean
    public SecurityFilterChain filterChain(HttpSecurity http,
                                         JwtAuthenticationTokenFilter jwtFilter,
                                         AuthenticationEntryPointImpl authenticationEntryPoint,
                                         AccessDeniedHandlerImpl accessDeniedHandler,
                                         LogoutSuccessHandlerImpl logoutSuccessHandler) throws Exception {
        
        http.csrf(AbstractHttpConfigurer::disable)
            .sessionManagement(session -> session.sessionCreationPolicy(SessionCreationPolicy.STATELESS))
            .authorizeHttpRequests(auth -> auth
                .requestMatchers("/api/*/login", "/api/*/captcha", "/api/*/register").permitAll()
                .requestMatchers("/doc.html", "/webjars/**", "/swagger-resources/**", "/v3/api-docs/**").permitAll()
                .requestMatchers("/actuator/**").permitAll()
                .anyRequest().authenticated()
            )
            .exceptionHandling(ex -> ex
                .authenticationEntryPoint(authenticationEntryPoint)
                .accessDeniedHandler(accessDeniedHandler)
            )
            .logout(logout -> logout
                .logoutUrl("/api/*/logout")
                .logoutSuccessHandler(logoutSuccessHandler)
            )
            .addFilterBefore(jwtFilter, UsernamePasswordAuthenticationFilter.class);

        return http.build();
    }
}
```

### 任务2.4：创建自动配置文件（1小时）

```properties
# xinjian-starter-security/src/main/resources/META-INF/spring.factories
org.springframework.boot.autoconfigure.EnableAutoConfiguration=\
com.xinjian.starter.security.config.XinJianSecurityAutoConfiguration
```

### 验收标准
- [x] xinjian-starter-security模块可以独立编译成功
- [x] 自动配置类正确配置Spring Security
- [x] JWT认证过滤器正常工作
- [x] 所有Security相关配置迁移完成

## 第4-5天：重构framework模块

### 任务4.1：更新依赖配置（1小时）

```xml
<!-- xinjian-framework/pom.xml -->
<dependencies>
    <!-- 移除直接的security依赖 -->
    <!-- 
    <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-security</artifactId>
    </dependency>
    -->
    
    <!-- 添加security starter依赖 -->
    <dependency>
        <groupId>com.xinjian</groupId>
        <artifactId>xinjian-starter-security</artifactId>
    </dependency>
    
    <!-- 添加契约模块依赖 -->
    <dependency>
        <groupId>com.xinjian</groupId>
        <artifactId>xinjian-platform-contract</artifactId>
    </dependency>
    
    <!-- 其他现有依赖保持不变 -->
</dependencies>
```

### 任务4.2：删除已迁移的类（2小时）

删除以下已迁移到starter的类：
- `SecurityConfiguration.java`
- `JwtAuthenticationTokenFilter.java`
- `TokenService.java`
- `LogoutSuccessHandlerImpl.java`
- `AuthenticationEntryPointImpl.java`
- `AccessDeniedHandlerImpl.java`

### 任务4.3：重构UserDetailsServiceImpl（3小时）

```java
// 新的UserDetailsServiceImpl - 基于契约接口
package com.xinjian.framework.web.service;

import com.xinjian.platform.contract.security.UserDetailsServiceContract;
import com.xinjian.platform.contract.security.dto.AuthUserDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Service;

/**
 * Spring Security 用户验证处理服务
 * 基于契约接口实现，解除与system模块的直接依赖
 */
@Slf4j
@Service
public class UserDetailsServiceImpl implements UserDetailsService {

    private final UserDetailsServiceContract userDetailsContract;

    public UserDetailsServiceImpl(UserDetailsServiceContract userDetailsContract) {
        this.userDetailsContract = userDetailsContract;
    }

    @Override
    public UserDetails loadUserByUsername(String username) throws UsernameNotFoundException {
        try {
            log.debug("开始加载用户详情，用户名：{}", username);
            AuthUserDTO authUser = userDetailsContract.loadUserByUsername(username);
            log.info("成功加载用户详情，用户名：{}, 用户ID: {}", username, authUser.getUserId());
            return authUser;
        } catch (Exception e) {
            log.error("加载用户详情时发生错误，用户名：{}, 错误：{}", username, e.getMessage(), e);
            throw new UsernameNotFoundException("用户名或密码错误");
        }
    }
}
```

### 任务4.4：重构SysLoginService（2小时）

简化SysLoginService，移除直接的业务依赖：

```java
// 重构后的SysLoginService
@Service
public class SysLoginService {
    
    private final AuthenticationManager authenticationManager;
    private final UserDetailsServiceContract userDetailsContract;
    private final TokenService tokenService;
    private final CaptchaService captchaService;
    
    public String login(String username, String password, String code, String uuid) {
        // 1. 校验验证码
        captchaService.validate(uuid, code);
        
        // 2. 执行用户身份认证
        Authentication authentication = performAuthentication(username, password);
        AuthUserDTO authUser = (AuthUserDTO) authentication.getPrincipal();
        
        // 3. 记录登录成功信息（通过契约接口）
        userDetailsContract.recordLoginSuccess(authUser.getUserId(), getClientIP());
        
        // 4. 生成并返回Token
        return tokenService.createToken(authUser);
    }
    
    private Authentication performAuthentication(String username, String password) {
        UsernamePasswordAuthenticationToken authToken = 
            new UsernamePasswordAuthenticationToken(username, password);
        return authenticationManager.authenticate(authToken);
    }
}
```

### 验收标准
- [x] framework模块编译成功
- [x] 移除了对system模块的直接依赖
- [x] 通过契约接口与system模块交互
- [x] 登录功能正常工作

## 第6-7天：重构system模块

### 任务6.1：添加契约模块依赖（30分钟）

```xml
<!-- xinjian-system/pom.xml 或相应的system模块pom.xml -->
<dependency>
    <groupId>com.xinjian</groupId>
    <artifactId>xinjian-platform-contract</artifactId>
</dependency>
```

### 任务6.2：实现UserDetailsServiceContract（4小时）

```java
// UserDetailsServiceContractImpl.java
package com.xinjian.system.service.impl;

import com.xinjian.common.core.domain.entity.SysUser;
import com.xinjian.common.enums.UserStatus;
import com.xinjian.common.service.ISysMenuService;
import com.xinjian.common.service.ISysRoleService;
import com.xinjian.common.service.ISysUserService;
import com.xinjian.common.utils.SecurityUtils;
import com.xinjian.platform.contract.security.UserDetailsServiceContract;
import com.xinjian.platform.contract.security.dto.AuthUserDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Service;

import java.util.HashSet;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 用户详情服务契约实现
 */
@Slf4j
@Service
public class UserDetailsServiceContractImpl implements UserDetailsServiceContract {
    
    private final ISysUserService userService;
    private final ISysRoleService roleService;
    private final ISysMenuService menuService;
    
    public UserDetailsServiceContractImpl(ISysUserService userService,
                                        ISysRoleService roleService,
                                        ISysMenuService menuService) {
        this.userService = userService;
        this.roleService = roleService;
        this.menuService = menuService;
    }
    
    @Override
    public AuthUserDTO loadUserByUsername(String username) {
        log.debug("开始加载用户详情，用户名：{}", username);
        
        // 1. 查找用户
        SysUser user = userService.selectUserByUserName(username);
        if (user == null) {
            log.info("用户不存在：{}", username);
            throw new UsernameNotFoundException("用户名或密码错误");
        }
        
        // 2. 验证用户状态
        validateUserStatus(user);
        
        // 3. 获取用户权限
        Set<String> authorities = getAllPermissions(user.getUserId());
        
        // 4. 构建AuthUserDTO
        return AuthUserDTO.builder()
            .userId(user.getUserId())
            .deptId(user.getDeptId())
            .username(user.getUserName())
            .password(user.getPassword())
            .enabled(!UserStatus.DISABLE.getCode().equals(user.getStatus()))
            .accountNonExpired(true)
            .accountNonLocked(true)
            .credentialsNonExpired(true)
            .authorities(authorities)
            .build();
    }
    
    @Override
    public boolean validateUserCredentials(String username, String password) {
        // 实现用户凭证验证逻辑
        SysUser user = userService.selectUserByUserName(username);
        return user != null && user.getPassword().equals(password);
    }
    
    @Override
    public void recordLoginSuccess(Long userId, String ipAddress) {
        // 记录登录成功信息
        SysUser updateUser = new SysUser();
        updateUser.setUserId(userId);
        updateUser.setLoginIp(ipAddress);
        updateUser.setLoginTime(new Date());
        userService.updateUserProfile(updateUser);
    }
    
    @Override
    public void recordLoginFailure(String username, String reason) {
        // 记录登录失败信息
        log.warn("用户登录失败：{}, 原因：{}", username, reason);
        // 可以在这里实现登录失败记录逻辑
    }
    
    private void validateUserStatus(SysUser user) {
        if (user.getIsDeleted()) {
            throw new RuntimeException("您的账号已被删除，请联系管理员");
        }
        
        if (UserStatus.DISABLE.getCode().equals(user.getStatus())) {
            throw new RuntimeException("您的账号已被停用，请联系管理员");
        }
    }
    
    private Set<String> getAllPermissions(Long userId) {
        Set<String> permissions = new HashSet<>();
        
        // 添加角色权限
        if (SecurityUtils.isAdminUser(userId)) {
            permissions.add("ROLE_ADMIN");
        } else {
            permissions.addAll(roleService.selectRolePermissionByUserId(userId)
                .stream().map(role -> "ROLE_" + role).collect(Collectors.toSet()));
        }
        
        // 添加菜单权限
        if (SecurityUtils.isAdminUser(userId)) {
            permissions.addAll(menuService.selectAllMenuPerms());
        } else {
            permissions.addAll(menuService.selectMenuPermsByUserId(userId));
        }
        
        return permissions;
    }
}
```

### 验收标准
- [x] system模块编译成功
- [x] 实现了所有契约接口方法
- [x] 用户认证功能正常
- [x] 权限验证功能正常

## 第8天：瘦身common模块

### 任务8.1：移除框架依赖（2小时）

```xml
<!-- 从xinjian-common/pom.xml中移除以下依赖 -->
<!--
<dependency>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-starter-security</artifactId>
</dependency>
-->
```

### 任务8.2：迁移Security相关类（4小时）

**迁移计划**：
- `LoginUser.java` → 已在contract模块中重新定义为`AuthUserDTO`
- `SecurityUtils.java` → `xinjian-starter-security`
- `JwtUtils.java` → `xinjian-starter-security`
- `AuthProperties.java` → `xinjian-starter-security`

### 任务8.3：更新引用（2小时）

更新所有对已迁移类的引用，确保编译通过。

### 验收标准
- [x] common模块体积显著减少
- [x] 移除了所有框架依赖
- [x] 保留的都是纯工具类和基础组件
- [x] 所有模块编译成功

## 第9-10天：集成测试和验证

### 任务9.1：功能验证（4小时）

1. **登录功能测试**
   - 正常登录流程
   - 错误用户名/密码
   - 验证码验证
   - Token生成和验证

2. **权限验证测试**
   - 角色权限验证
   - 菜单权限验证
   - 管理员权限验证

3. **登出功能测试**
   - 正常登出流程
   - Token失效验证

### 任务9.2：性能验证（2小时）

1. **响应时间测试**
   - 登录接口响应时间
   - 权限验证响应时间

2. **并发测试**
   - 多用户同时登录
   - 高并发权限验证

### 任务9.3：集成验证（2小时）

1. **模块独立性验证**
   - 各模块独立编译
   - 依赖关系正确

2. **配置验证**
   - 自动配置生效
   - 配置属性正确加载

### 验收标准
- [x] 所有功能测试通过
- [x] 性能指标符合要求
- [x] 集成测试通过
- [x] 无循环依赖
- [x] 模块职责清晰

## 风险控制措施

### 1. 备份策略
```bash
# 每天开始前创建备份分支
git checkout -b backup-day-$(date +%Y%m%d)
git push origin backup-day-$(date +%Y%m%d)
```

### 2. 回滚方案
```bash
# 如果出现问题，快速回滚
git checkout main
git reset --hard backup-day-$(date +%Y%m%d)
```

### 3. 监控告警
- 实时监控登录成功率
- 监控系统响应时间
- 监控错误日志

通过以上详细的实施步骤，可以安全、有序地完成xinjian-starter-security模块的创建和架构解耦工作。
