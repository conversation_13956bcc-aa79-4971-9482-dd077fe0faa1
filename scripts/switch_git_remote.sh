#!/bin/bash

# 定义变量
REMOTE_NAME="origin"
GIT_IP="***********"
GIT_DOMAIN="gitlab.wfgxic.com"
GIT_PROTOCOL="git@$GIT_IP:"
HTTPS_PROTOCOL="https://$GIT_DOMAIN/"

# 获取当前的远程仓库地址
get_current_url() {
    git remote get-url $REMOTE_NAME
}

# 提取仓库路径
extract_repo_path() {
    local url=$1
    if [[ $url =~ ^git@$GIT_IP:(.*)$ ]]; then
        echo "${BASH_REMATCH[1]}"
    elif [[ $url =~ ^https://$GIT_DOMAIN/(.*)$ ]]; then
        echo "${BASH_REMATCH[1]}"
    else
        echo ""
    fi
}

# 切换协议
switch_protocol() {
    local current_url=$1
    local repo_path=$2
    if [[ $current_url =~ ^git@$GIT_IP: ]]; then
        echo "${HTTPS_PROTOCOL}${repo_path}"
    elif [[ $current_url =~ ^https://$GIT_DOMAIN/ ]]; then
        echo "${GIT_PROTOCOL}${repo_path}"
    else
        echo ""
    fi
}

# 主函数
main() {
    local current_url=$(get_current_url)
    echo "-> 当前远程仓库地址为"
    git remote -v

    local repo_path=$(extract_repo_path "$current_url")
    if [[ -z $repo_path ]]; then
        echo "-> 当前远程仓库地址不符合预期格式"
        exit 1
    fi

    local new_url=$(switch_protocol "$current_url" "$repo_path")
    if [[ -n $new_url ]]; then
        git remote set-url $REMOTE_NAME $new_url
        echo "-> 切换到 $(if [[ $new_url =~ ^https:// ]]; then echo "HTTPS"; else echo "Git"; fi) 协议"
    else
        echo "-> 无法识别当前协议，无法切换"
        exit 1
    fi

    echo "-> 更新远程仓库地址为"
    git remote -v

    if git ls-remote $REMOTE_NAME &> /dev/null; then
        echo "-> 远程仓库连接正常"
    else
        echo "-> 远程仓库连接异常"
    fi
}

# 运行主函数
main
