<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>

  <parent>
    <groupId>com.xinjian</groupId>
    <artifactId>xinjian-modules</artifactId>
    <version>1.0.0</version>
  </parent>

  <artifactId>xinjian-module-captcha</artifactId>
  <packaging>jar</packaging>

  <name>xinjian-module-captcha</name>
  <description>信建验证码模块</description>

  <dependencies>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-configuration-processor</artifactId>
      <optional>true</optional>
    </dependency>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-autoconfigure</artifactId>
    </dependency>
    <dependency>
      <groupId>pro.fessional</groupId>
      <artifactId>kaptcha</artifactId>
    </dependency>
    <dependency>
      <groupId>com.pig4cloud.plugin</groupId>
      <artifactId>captcha-spring-boot-starter</artifactId>
    </dependency>
    <dependency>
      <groupId>org.projectlombok</groupId>
      <artifactId>lombok</artifactId>
      <scope>provided</scope>
    </dependency>
  </dependencies>

</project>
