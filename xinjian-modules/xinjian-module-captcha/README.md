# 验证码模块使用指南

## 模块概述

`xinjian-module-captcha` 是一个独立的验证码功能模块，提供了验证码生成和验证的完整解决方案。

## 特性

- 🔧 **多提供商支持**：支持 Kaptcha 和 EasyCaptcha
- 🔒 **高度解耦**：通过接口与存储层解耦，支持多种存储方式
- ⚙️ **自动配置**：基于 Spring Boot 自动配置
- 🎯 **一次性使用**：验证码验证后立即失效，防止重放攻击
- 📦 **独立模块**：不依赖其他业务模块，可单独使用

## 快速开始

### 1. 添加依赖

```xml
<dependency>
    <groupId>com.xinjian</groupId>
    <artifactId>xinjian-module-captcha</artifactId>
    <version>${xinjian.version}</version>
</dependency>
```

### 2. 配置 application.yml

```yaml
xinjian:
  captcha:
    enabled: true          # 是否启用验证码
    type: math            # 验证码类型：math（数学）| char（字符）
    expire-time: 2        # 过期时间（分钟）
    provider: kaptcha    # 提供商：kaptcha | easycaptcha
```

### 3. 使用验证码服务

#### 生成验证码

```java
@RestController
@RequiredArgsConstructor
public class CaptchaController {
    
    private final ICaptchaService captchaService;
    
    @GetMapping("/captcha")
    public CaptchaGenerationResult generateCaptcha() {
        return captchaService.generate();
    }
}
```

#### 验证验证码

```java
@Service
@RequiredArgsConstructor
public class LoginService {
    
    private final ICaptchaService captchaService;
    
    public void login(String username, String password, String code, String uuid) {
        // 验证码校验
        captchaService.validate(uuid, code);
        
        // 继续登录逻辑...
    }
}
```

## API 文档

### ICaptchaService

```java
public interface ICaptchaService {
    /**
     * 生成验证码
     * @return 包含 key 和 base64 图片的结果
     */
    CaptchaGenerationResult generate();
    
    /**
     * 验证验证码
     * @param key 验证码唯一标识
     * @param code 用户输入的验证码
     * @throws CaptchaValidationException 验证码错误
     * @throws CaptchaExpiredException 验证码已过期
     */
    void validate(String key, String code);
}
```

### CaptchaGenerationResult

```java
@Data
@Builder
public class CaptchaGenerationResult {
    private boolean enabled;  // 是否启用
    private String key;       // 验证码唯一标识
    private String image;     // base64 格式的图片
}
```

## 异常处理

验证码模块定义了以下异常：

- `CaptchaException`：验证码异常基类
- `CaptchaValidationException`：验证码验证失败
- `CaptchaExpiredException`：验证码已过期

## 扩展功能

### 自定义存储实现

如果需要使用 Redis 等其他存储方式，可以实现 `ICaptchaStore` 接口：

```java
@Component
public class RedisCaptchaStore implements ICaptchaStore {
    
    private final RedisTemplate<String, String> redisTemplate;
    
    @Override
    public void save(String key, String code, long expireTimeInMinutes) {
        String redisKey = "captcha:" + key;
        redisTemplate.opsForValue().set(redisKey, code, 
            Duration.ofMinutes(expireTimeInMinutes));
    }
    
    @Override
    public Optional<String> get(String key) {
        String redisKey = "captcha:" + key;
        String code = redisTemplate.opsForValue().get(redisKey);
        return Optional.ofNullable(code);
    }
    
    @Override
    public void delete(String key) {
        String redisKey = "captcha:" + key;
        redisTemplate.delete(redisKey);
    }
}
```

### 添加新的验证码提供商

1. 在 `provider` 包下创建新的提供商包
2. 实现 `ICaptchaService` 接口
3. 在自动配置类中添加新的 Bean 定义

## 示例流程

1. 前端调用 `/captcha` 获取验证码
2. 后端返回包含 `key` 和 `image` 的 JSON
3. 前端显示验证码图片
4. 用户输入验证码后，将 `key` 和验证码一起提交
5. 后端调用 `captchaService.validate(key, code)` 验证

## 注意事项

- 验证码默认过期时间为 2 分钟
- 验证码验证后立即失效，不能重复使用
- 数学验证码会自动计算结果，只需用户输入计算结果即可
- 建议在生产环境中使用 Redis 存储验证码