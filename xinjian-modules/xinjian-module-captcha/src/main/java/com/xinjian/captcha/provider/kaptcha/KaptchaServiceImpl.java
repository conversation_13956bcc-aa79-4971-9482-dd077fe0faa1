package com.xinjian.captcha.provider.kaptcha;

import com.google.code.kaptcha.Producer;
import com.xinjian.captcha.config.XinJianCaptchaProperties;
import com.xinjian.captcha.domain.CaptchaGenerationResult;
import com.xinjian.captcha.exception.CaptchaExpiredException;
import com.xinjian.captcha.exception.CaptchaValidationException;
import com.xinjian.captcha.service.ICaptchaService;
import com.xinjian.captcha.service.ICaptchaStore;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.UUID;
import javax.imageio.ImageIO;
import org.springframework.util.Base64Utils;

public class KaptchaServiceImpl implements ICaptchaService {

  private Producer captchaProducer;

  private Producer captchaProducerMath;

  private XinJianCaptchaProperties properties;

  private ICaptchaStore captchaStore;

  @Override
  public CaptchaGenerationResult generate() {
    if (!properties.isEnabled()) {
      return CaptchaGenerationResult.builder().enabled(false).build();
    }

    String captchaKey = UUID.randomUUID().toString();
    String capText;
    String captchaCode;
    BufferedImage image;

    if ("math".equals(properties.getType())) {
      String capTextMath = captchaProducerMath.createText();
      capText = capTextMath.substring(0, capTextMath.lastIndexOf("@"));
      captchaCode = capTextMath.substring(capTextMath.lastIndexOf("@") + 1);
      image = captchaProducerMath.createImage(capText);
    } else {
      capText = captchaCode = captchaProducer.createText();
      image = captchaProducer.createImage(capText);
    }

    captchaStore.save(captchaKey, captchaCode, properties.getExpireTime());

    String base64Image = imageToBase64(image);

    return CaptchaGenerationResult.builder()
        .enabled(true)
        .key(captchaKey)
        .image(base64Image)
        .build();
  }

  @Override
  public void validate(String key, String code) {
    // 1. 检查验证码功能是否启用
    if (!properties.isEnabled()) {
      return; // 如果未启用，直接跳过校验
    }

    // 2. 基础参数校验
    if (key == null || code == null || key.trim().isEmpty() || code.trim().isEmpty()) {
      throw new CaptchaValidationException("验证码 key 和 code 不能为空");
    }

    // 3. 从存储中获取正确的验证码，如果不存在则代表已过期
    if (captchaStore.get(key).isPresent()) {
      String storedCode = captchaStore.get(key).get();
      // 4. 校验后立即删除，确保验证码只能使用一次
      captchaStore.delete(key);

      // 5. 比较用户输入的验证码与存储的验证码（忽略大小写）
      if (!code.equalsIgnoreCase(storedCode)) {
        throw new CaptchaValidationException("验证码错误");
      }
    } else {
      throw new CaptchaExpiredException("验证码已过期或不存在");
    }
  }

  // Setter methods for dependency injection
  public void setCaptchaProducer(Producer captchaProducer) {
    this.captchaProducer = captchaProducer;
  }

  public void setCaptchaProducerMath(Producer captchaProducerMath) {
    this.captchaProducerMath = captchaProducerMath;
  }

  public void setProperties(XinJianCaptchaProperties properties) {
    this.properties = properties;
  }

  public void setCaptchaStore(ICaptchaStore captchaStore) {
    this.captchaStore = captchaStore;
  }

  private String imageToBase64(BufferedImage image) {
    try (ByteArrayOutputStream baos = new ByteArrayOutputStream()) {
      ImageIO.write(image, "png", baos);
      byte[] imageBytes = baos.toByteArray();
      return "data:image/png;base64," + Base64Utils.encodeToString(imageBytes);
    } catch (IOException e) {
      throw new RuntimeException("验证码生成失败");
    }
  }
}
