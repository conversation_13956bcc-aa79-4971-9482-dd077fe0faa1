package com.xinjian.captcha.service;

import com.xinjian.captcha.domain.CaptchaGenerationResult;

/**
 * 验证码服务统一接口 (契约)
 *
 * <p>定义了验证码的【生成】和【校验】两大核心能力。
 */
public interface ICaptchaService {

  /**
   * 生成一个新的验证码。
   *
   * @return 包含 key (唯一标识) 和 image (base64 图片) 的结果对象。
   */
  CaptchaGenerationResult generate();

  /**
   * 校验用户输入的验证码是否正确。
   *
   * <p>一个成功的校验会消耗掉这个验证码，使其不能被重复使用。
   *
   * @param key 前端在调用 generate() 时获取到的唯一标识。
   * @param code 用户输入的验证码字符串。
   * @throws RuntimeException 如果验证码失效或不正确，应抛出具体的业务异常。
   */
  void validate(String key, String code);
}
