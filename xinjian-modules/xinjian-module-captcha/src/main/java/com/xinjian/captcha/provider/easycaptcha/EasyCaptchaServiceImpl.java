package com.xinjian.captcha.provider.easycaptcha;

import com.pig4cloud.captcha.ArithmeticCaptcha;
import com.pig4cloud.captcha.ChineseCaptcha;
import com.pig4cloud.captcha.ChineseGifCaptcha;
import com.pig4cloud.captcha.GifCaptcha;
import com.pig4cloud.captcha.SpecCaptcha;
import com.xinjian.captcha.config.XinJianCaptchaProperties;
import com.xinjian.captcha.domain.CaptchaGenerationResult;
import com.xinjian.captcha.exception.CaptchaExpiredException;
import com.xinjian.captcha.exception.CaptchaValidationException;
import com.xinjian.captcha.service.ICaptchaService;
import com.xinjian.captcha.service.ICaptchaStore;
import java.util.UUID;
import lombok.Setter;

@Setter
public class EasyCaptchaServiceImpl implements ICaptchaService {

  private XinJianCaptchaProperties properties;

  private ICaptchaStore captchaStore;

  @Override
  public CaptchaGenerationResult generate() {
    if (!properties.isEnabled()) {
      return CaptchaGenerationResult.builder().enabled(false).build();
    }

    String captchaKey = UUID.randomUUID().toString();
    String captchaCode;
    String base64Image;

    // 使用 EasyCaptcha 库生成验证码
    if ("math".equals(properties.getType())) {
      // 数学验证码
      ArithmeticCaptcha captcha = new ArithmeticCaptcha(130, 48);
      captcha.setLen(2); // 几位数运算，默认是两位数
      captchaCode = captcha.text();
      base64Image = captcha.toBase64();
    } else if ("chinese".equals(properties.getType())) {
      // 中文验证码
      ChineseCaptcha captcha = new ChineseCaptcha(130, 48);
      captchaCode = captcha.text();
      base64Image = captcha.toBase64();
    } else if ("gif".equals(properties.getType())) {
      // 动态验证码
      GifCaptcha captcha = new GifCaptcha(130, 48);
      captchaCode = captcha.text();
      base64Image = captcha.toBase64();
    } else if ("chineseGif".equals(properties.getType())) {
      // 中文动态验证码
      ChineseGifCaptcha captcha = new ChineseGifCaptcha(130, 48);
      captchaCode = captcha.text();
      base64Image = captcha.toBase64();
    } else {
      // 默认普通字符验证码
      SpecCaptcha captcha = new SpecCaptcha(130, 48);
      captcha.setCharType(2); // 纯数字
      captchaCode = captcha.text();
      base64Image = captcha.toBase64();
    }

    captchaStore.save(captchaKey, captchaCode, properties.getExpireTime());

    return CaptchaGenerationResult.builder()
        .enabled(true)
        .key(captchaKey)
        .image(base64Image)
        .build();
  }

  @Override
  public void validate(String key, String code) {
    // 1. 检查验证码功能是否启用
    if (!properties.isEnabled()) {
      return; // 如果未启用，直接跳过校验
    }

    // 2. 基础参数校验
    if (key == null || code == null || key.trim().isEmpty() || code.trim().isEmpty()) {
      throw new CaptchaValidationException("验证码 key 和 code 不能为空");
    }

    // 3. 从存储中获取正确的验证码，如果不存在则代表已过期
    if (captchaStore.get(key).isPresent()) {
      String storedCode = captchaStore.get(key).get();
      // 4. 校验后立即删除，确保验证码只能使用一次
      captchaStore.delete(key);

      // 5. 比较用户输入的验证码与存储的验证码（忽略大小写）
      if (!code.equalsIgnoreCase(storedCode)) {
        throw new CaptchaValidationException("验证码错误");
      }
    } else {
      throw new CaptchaExpiredException("验证码已过期或不存在");
    }
  }
}
