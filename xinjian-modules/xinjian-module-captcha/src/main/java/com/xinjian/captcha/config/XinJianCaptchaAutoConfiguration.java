package com.xinjian.captcha.config;

import com.xinjian.captcha.service.ICaptchaService;
import com.xinjian.captcha.service.ICaptchaStore;
import com.xinjian.captcha.store.impl.InMemoryCaptchaStore;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
@EnableConfigurationProperties(XinJianCaptchaProperties.class)
public class XinJianCaptchaAutoConfiguration {

  @Bean
  @ConditionalOnMissingBean
  public ICaptchaStore captchaStore() {
    return new InMemoryCaptchaStore();
  }

  @Bean
  @ConditionalOnProperty(
      prefix = "xinjian.captcha",
      name = "provider",
      havingValue = "kaptcha",
      matchIfMissing = true)
  public ICaptchaService kaptchaCaptchaService(
      XinJianCaptchaProperties properties,
      ICaptchaStore captchaStore,
      com.xinjian.captcha.provider.kaptcha.KaptchaConfig kaptchaConfig) {
    com.xinjian.captcha.provider.kaptcha.KaptchaServiceImpl service =
        new com.xinjian.captcha.provider.kaptcha.KaptchaServiceImpl();
    service.setProperties(properties);
    service.setCaptchaStore(captchaStore);
    service.setCaptchaProducer(kaptchaConfig.getKaptchaBean());
    service.setCaptchaProducerMath(kaptchaConfig.getKaptchaBeanMath());
    return service;
  }

  @Bean
  @ConditionalOnProperty(prefix = "xinjian.captcha", name = "provider", havingValue = "easycaptcha")
  public ICaptchaService easycaptchaCaptchaService(
      XinJianCaptchaProperties properties, ICaptchaStore captchaStore) {
    com.xinjian.captcha.provider.easycaptcha.EasyCaptchaServiceImpl service =
        new com.xinjian.captcha.provider.easycaptcha.EasyCaptchaServiceImpl();
    service.setProperties(properties);
    service.setCaptchaStore(captchaStore);
    return service;
  }
}
