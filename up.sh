#!/bin/bash

memorySize="512m"
jarName=$(basename "$(pwd)")

while [[ $# -gt 0 ]]; do
    key="$1"
    case $key in
        -m|--memory)
            memorySize="$2"
            shift
            shift
            ;;
        -n|--name)
            jarName="$2"
            shift
            shift
            ;;
        *)
            echo "Unknown option: $key"
            exit 1
            ;;
    esac
done

echo "Memory Size: $memorySize"
echo "Jar Name   : $jarName"

for jar in $(ls -t $jarName*.jar); do
    id=$(pgrep -f $jar)
    if [ $id ]; then
        kill -9 $id
        echo "Stop Pid   : $jar [$id]"
    fi
done

newJar=$(ls -t $jarName*.jar | head -1)

if [ $newJar ]; then
    nohup java -jar -Xms$memorySize -Xmx$memorySize $newJar >${newJar%.*}.log 2>&1 &
    sleep 5
    newid=$(pgrep -f $newJar)
    echo "Start Pid  : $newJar [$newid]"
fi

for jar in $(ls -t $jarName*.jar); do
    if [ $jar != $newJar ]; then
        rm -f $jar && rm -f ${jar%.*}.log
    fi
done
