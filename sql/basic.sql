-- ----------------------------
-- 1、部门表
-- ----------------------------
drop table if exists sys_dept;
create table sys_dept (
  dept_id           bigint(20)      not null auto_increment    comment '部门 ID',
  parent_id         bigint(20)      default null               comment '父部门ID（NULL表示顶级部门）',
  ancestors         varchar(1000)   default ''                 comment '祖级列表（父级ID链，逗号分隔）',
  dept_name         varchar(50)     not null                   comment '部门名称',
  order_num         int(4)          not null default 0         comment '显示顺序',
  leader            bigint(20)      default null               comment '负责人 ID',
  mobile            varchar(20)     default null               comment '联系电话',
  email             varchar(254)    default null               comment '邮箱地址',
  status            boolean         not null default 1         comment '部门状态（0-停用 1-正常）',
  is_deleted        boolean         not null default 0         comment '删除标志（0-存在 1-删除）',
  create_by         bigint(20)      not null default 0.        comment '创建者 ID',
  create_time       datetime        not null default CURRENT_TIMESTAMP comment '创建时间',
  update_by         bigint(20)      not null default 0         comment '更新者 ID',
  update_time       datetime        not null default CURRENT_TIMESTAMP on update CURRENT_TIMESTAMP comment '更新时间',
  primary key (dept_id),
  unique key uk_dept_parent_name (parent_id, dept_name),
  index idx_sys_dept_parent_id (parent_id),
  index idx_sys_dept_status (status)
) engine = innodb comment = '部门表' charset = utf8mb4 collate = utf8mb4_general_ci;


-- ----------------------------
-- 初始化 - 部门表数据
-- ----------------------------
insert into sys_dept values(1, 0, '0', '高新信建', 0, null, '8816220', '<EMAIL>', 1,  0, 1, CURRENT_TIMESTAMP, 1, CURRENT_TIMESTAMP);

-- ----------------------------
-- 2、用户信息表
-- ----------------------------
drop table if exists sys_user;
create table sys_user (
  user_id           bigint(20)      not null auto_increment    comment '用户 ID',
  dept_id           bigint(20)      default null               comment '所属部门 ID',
  user_name         varchar(50)     not null                   comment '用户账号（登录名）',
  nick_name         varchar(50)     not null                   comment '用户昵称（显示名）',
  user_type         tinyint         not null default 0         comment '用户类型（0 系统用户 1 第三方用户 2 其他用户）',
  email             varchar(254)    default null               comment '用户邮箱',
  mobile            varchar(20)     default null               comment '手机号码',
  sex               tinyint         default null               comment '用户性别（1 男 2 女 3 未知）',
  avatar            varchar(500)    default null               comment '头像地址 URL',
  password          varchar(255)    not null                   comment '登录密码（加密存储）',
  status            boolean         not null default 1         comment '帐号状态（0-停用 1-正常）',
  is_deleted        boolean         not null default 0         comment '删除标志（0-存在 1-删除）',
  login_ip          varchar(128)    default null               comment '最后登录 IP 地址',
  login_time        datetime        not null default CURRENT_TIMESTAMP comment '最后登录时间',
  create_by         bigint(20)      not null                   comment '创建者 ID',
  create_time       datetime        not null default CURRENT_TIMESTAMP comment '创建时间',
  update_by         bigint(20)      not null                   comment '更新者 ID',
  update_time       datetime        not null default CURRENT_TIMESTAMP on update CURRENT_TIMESTAMP comment '更新时间',
  remark            varchar(500)    default ''                 comment '用户备注信息',
  primary key (user_id),
  unique key uk_sys_user_username (user_name),
  index idx_sys_user_dept_id (dept_id),
  index idx_sys_user_status (status),
  index idx_sys_user_mobile (mobile),
  index idx_sys_user_email (email)
) engine = innodb comment = '用户信息表' charset = utf8mb4 collate = utf8mb4_general_ci;


-- ----------------------------
-- 初始化 - 用户信息表数据
-- ----------------------------
insert into sys_user values(1, 1, 'admin', '高新信建', 0, '<EMAIL>', '', 3, '', '$2a$10$z/JHOSBTm/Lb0RebHO/X4.oNl7khDssmSZXU/pug86ACbxtk3qhsq', 1, 0, 1, CURRENT_TIMESTAMP, 1, CURRENT_TIMESTAMP, 1, CURRENT_TIMESTAMP, '');

-- ----------------------------
-- 3、岗位信息表
-- ----------------------------
drop table if exists sys_post;
create table sys_post
(
  post_id       bigint(20)      not null auto_increment    comment '岗位 ID',
  post_code     varchar(64)     not null                   comment '岗位编码（唯一标识）',
  post_name     varchar(50)     not null                   comment '岗位名称（显示名称）',
  post_sort     int(4)          not null                   comment '显示顺序（排序号）',
  status        boolean         not null default 1         comment '岗位状态（1 正常 0 停用）',
  create_by     bigint(20)      not null default 0         comment '创建者 ID',
  create_time   datetime        not null default CURRENT_TIMESTAMP comment '创建时间',
  update_by     bigint(20)      not null default 0         comment '更新者 ID',
  update_time   datetime        not null default CURRENT_TIMESTAMP on update CURRENT_TIMESTAMP comment '更新时间',
  remark        varchar(500)    default ''                 comment '岗位备注信息',
  primary key (post_id),
  unique key uk_sys_post_code (post_code),
  index idx_sys_post_status (status)
) engine = innodb comment = '岗位信息表' charset = utf8mb4 collate = utf8mb4_general_ci;


-- ----------------------------
-- 4、角色信息表
-- ----------------------------
drop table if exists sys_role;
create table sys_role (
  role_id              bigint(20)      not null auto_increment    comment '角色 ID',
  role_name            varchar(50)     not null                   comment '角色名称（显示名称）',
  role_key             varchar(100)    not null                   comment '角色权限标识（唯一编码）',
  role_sort            int(4)          not null                   comment '显示顺序（排序号）',
  data_scope           char(1)         not null default '1'       comment '数据权限范围（1：全部数据 2：自定义数据 3：本部门数据 4：本部门及以下数据）',
  menu_check_strictly  boolean         not null default 1         comment '菜单树选择项是否关联显示（1 是 0 否）',
  dept_check_strictly  boolean         not null default 1         comment '部门树选择项是否关联显示（1 是 0 否）',
  status               boolean         not null default 1         comment '角色状态（1 正常 0 停用）',
  is_deleted           boolean         not null default 0         comment '删除标志（0 存在 1 删除）',
  create_by            bigint(20)      not null default 0         comment '创建者 ID',
  create_time          datetime        not null default CURRENT_TIMESTAMP comment '创建时间',
  update_by            bigint(20)      not null default 0         comment '更新者 ID',
  update_time          datetime        not null default CURRENT_TIMESTAMP on update CURRENT_TIMESTAMP comment '更新时间',
  remark               varchar(500)    default ''                 comment '角色备注信息',
  primary key (role_id),
  unique key uk_sys_role_key (role_key),
  index idx_sys_role_status (status)
) engine = innodb comment = '角色信息表' charset = utf8mb4 collate = utf8mb4_general_ci;


-- ----------------------------
-- 初始化 - 角色信息表数据
-- ----------------------------
insert into sys_role values(1, '超级管理员', 'ADMIN', 1, '1', 1, 1, 1, 0, 1, CURRENT_TIMESTAMP, 1, CURRENT_TIMESTAMP, '超级管理员');

-- ----------------------------
-- 5、菜单权限表
-- ----------------------------
drop table if exists sys_menu;
create table sys_menu (
  menu_id           bigint(20)      not null auto_increment    comment '菜单 ID',
  menu_name         varchar(50)     not null                   comment '菜单名称（显示名称）',
  parent_id         bigint(20)      default null               comment '父菜单 ID（NULL表示顶级菜单）',
  order_num         int(4)          not null default 0         comment '显示顺序（排序号）',
  path              varchar(200)    default ''                 comment '路由地址（URL 路径）',
  component         varchar(255)    default ''                 comment '组件路径（前端组件路径）',
  query             varchar(255)    default ''                 comment '路由参数（JSON 格式）',
  is_frame          boolean         not null default 0         comment '是否为外链（1 是 0 否）',
  is_cache          boolean         not null default 0         comment '是否缓存（1 缓存 0 不缓存）',
  menu_type         char(1)         not null                   comment '菜单类型（M 目录 C 菜单 F 按钮）',
  visible           boolean         not null default 1         comment '菜单可见性（1 显示 0 隐藏）',
  status            boolean         not null default 1         comment '菜单状态（1 正常 0 停用）',
  perms             varchar(100)    default ''                 comment '权限标识（权限字符串）',
  icon              varchar(100)    default ''                 comment '菜单图标（图标名称）',
  create_by         bigint(20)      not null default 0         comment '创建者 ID',
  create_time       datetime        not null default CURRENT_TIMESTAMP comment '创建时间',
  update_by         bigint(20)      not null default 0         comment '更新者 ID',
  update_time       datetime        not null default CURRENT_TIMESTAMP on update CURRENT_TIMESTAMP comment '更新时间',
  remark            varchar(500)    default ''                 comment '菜单备注信息',
  primary key (menu_id),
  unique key uk_sys_menu_parent_name (parent_id, menu_name),
  index idx_sys_menu_parent_id (parent_id),
  index idx_sys_menu_status (status),
  index idx_sys_menu_type (menu_type)
) engine = innodb comment = '菜单权限表' charset = utf8mb4 collate = utf8mb4_general_ci;


-- ----------------------------
-- 初始化 - 菜单信息表数据
-- ----------------------------
-- 一级菜单
insert into sys_menu values('1',    '系统管理', '0',   '1', 'system',                           null, '', 0, 0, 'M', 1, 1, '',                        'mingcute:settings-3-fill',      1, CURRENT_TIMESTAMP, 1, CURRENT_TIMESTAMP, '系统管理目录');
insert into sys_menu values('2',    '系统监控', '0',   '2', 'monitor',                          null, '', 0, 0, 'M', 1, 1, '',                        'mingcute:presentation-1-fill',  1, CURRENT_TIMESTAMP, 1, CURRENT_TIMESTAMP, '系统监控目录');
insert into sys_menu values('3',    '系统工具', '0',   '3', 'tool',                             null, '', 0, 0, 'M', 1, 1, '',                        'mingcute:tool-fill',            1, CURRENT_TIMESTAMP, 1, CURRENT_TIMESTAMP, '系统工具目录');
-- 二级菜单
insert into sys_menu values('100',  '用户管理', '1',   '1', 'user',       'system/user/index',        '', 0, 0, 'C', 1, 1, 'system:user:list',        'mingcute:group-3-fill',         1, CURRENT_TIMESTAMP, 1, CURRENT_TIMESTAMP, '用户管理菜单');
insert into sys_menu values('101',  '角色管理', '1',   '2', 'role',       'system/role/index',        '', 0, 0, 'C', 1, 1, 'system:role:list',        'mingcute:palette-fill',         1, CURRENT_TIMESTAMP, 1, CURRENT_TIMESTAMP, '角色管理菜单');
insert into sys_menu values('102',  '菜单管理', '1',   '3', 'menu',       'system/menu/index',        '', 0, 0, 'C', 1, 1, 'system:menu:list',        'mingcute:menu-fill',            1, CURRENT_TIMESTAMP, 1, CURRENT_TIMESTAMP, '菜单管理菜单');
insert into sys_menu values('103',  '部门管理', '1',   '4', 'dept',       'system/dept/index',        '', 0, 0, 'C', 1, 1, 'system:dept:list',        'mingcute:department-fill',      1, CURRENT_TIMESTAMP, 1, CURRENT_TIMESTAMP, '部门管理菜单');
insert into sys_menu values('104',  '岗位管理', '1',   '5', 'post',       'system/post/index',        '', 0, 0, 'C', 1, 1, 'system:post:list',        'mingcute:sword-fill',           1, CURRENT_TIMESTAMP, 1, CURRENT_TIMESTAMP, '岗位管理菜单');
insert into sys_menu values('105',  '字典管理', '1',   '6', 'dict',       'system/dict/index',        '', 0, 0, 'C', 1, 1, 'system:dict:list',        'mingcute:list-search-fill',     1, CURRENT_TIMESTAMP, 1, CURRENT_TIMESTAMP, '字典管理菜单');
insert into sys_menu values('106',  '参数设置', '1',   '7', 'config',     'system/config/index',      '', 0, 0, 'C', 1, 1, 'system:config:list',      'mingcute:settings-2-fill',      1, CURRENT_TIMESTAMP, 1, CURRENT_TIMESTAMP, '参数设置菜单');
insert into sys_menu values('107',  '通知公告', '1',   '8', 'notice',     'system/notice/index',      '', 0, 0, 'C', 1, 1, 'system:notice:list',      'mingcute:notification-fill',    1, CURRENT_TIMESTAMP, 1, CURRENT_TIMESTAMP, '通知公告菜单');
insert into sys_menu values('108',  '日志管理', '1',   '9', 'log',        '',                         '', 0, 0, 'M', 1, 1, '',                        'mingcute:file-locked-fill',     1, CURRENT_TIMESTAMP, 1, CURRENT_TIMESTAMP, '日志管理菜单');
insert into sys_menu values('109',  '在线用户', '2',   '1', 'online',     'monitor/online/index',     '', 0, 0, 'C', 1, 1, 'monitor:online:list',     'mingcute:user-visible-fill',    1, CURRENT_TIMESTAMP, 1, CURRENT_TIMESTAMP, '在线用户菜单');
insert into sys_menu values('110',  '定时任务', '2',   '2', 'job',        'monitor/job/index',        '', 0, 0, 'C', 1, 1, 'monitor:job:list',        'mingcute:task-fill',            1, CURRENT_TIMESTAMP, 1, CURRENT_TIMESTAMP, '定时任务菜单');
insert into sys_menu values('111',  '服务监控', '2',   '4', 'server',     'monitor/server/index',     '', 0, 0, 'C', 1, 1, 'monitor:server:list',     'mingcute:cube-fill',            1, CURRENT_TIMESTAMP, 1, CURRENT_TIMESTAMP, '服务监控菜单');
insert into sys_menu values('112',  '缓存监控', '2',   '5', 'cache',      'monitor/cache/index',      '', 0, 0, 'C', 1, 1, 'monitor:cache:list',      'mingcute:dashboard-4-fill',     1, CURRENT_TIMESTAMP, 1, CURRENT_TIMESTAMP, '缓存监控菜单');
insert into sys_menu values('113',  '缓存列表', '2',   '6', 'cache-list', 'monitor/cache/list',       '', 0, 0, 'C', 1, 1, 'monitor:cache:list',      'mingcute:list-check-3-fill',    1, CURRENT_TIMESTAMP, 1, CURRENT_TIMESTAMP, '缓存列表菜单');
insert into sys_menu values('114',  '表单构建', '3',   '1', 'build',      'tool/build/index',         '', 0, 0, 'C', 1, 1, 'tool:build:list',         'mingcute:auction-fill',         1, CURRENT_TIMESTAMP, 1, CURRENT_TIMESTAMP, '表单构建菜单');
insert into sys_menu values('115',  '代码生成', '3',   '2', 'gen',        'tool/gen/index',           '', 0, 0, 'C', 1, 1, 'tool:gen:list',           'mingcute:code-fill',            1, CURRENT_TIMESTAMP, 1, CURRENT_TIMESTAMP, '代码生成菜单');
-- 三级菜单
insert into sys_menu values('500',  '操作日志', '108', '1', 'operlog',    'monitor/operlog/index',    '', 0, 0, 'C', 1, 1, 'monitor:operlog:list',    'mingcute:finger-press-fill',    1, CURRENT_TIMESTAMP, 1, CURRENT_TIMESTAMP, '操作日志菜单');
insert into sys_menu values('501',  '登录日志', '108', '2', 'logininfor', 'monitor/logininfor/index', '', 0, 0, 'C', 1, 1, 'monitor:logininfor:list', 'mingcute:key-2-fill',           1, CURRENT_TIMESTAMP, 1, CURRENT_TIMESTAMP, '登录日志菜单');
-- 用户管理按钮
insert into sys_menu values('1000', '用户查询', '100', '1',  '', '', '', 0, 0, 'F', 1, 1, 'system:user:query',          '', 1, CURRENT_TIMESTAMP, 1, CURRENT_TIMESTAMP, '');
insert into sys_menu values('1001', '用户新增', '100', '2',  '', '', '', 0, 0, 'F', 1, 1, 'system:user:add',            '', 1, CURRENT_TIMESTAMP, 1, CURRENT_TIMESTAMP, '');
insert into sys_menu values('1002', '用户修改', '100', '3',  '', '', '', 0, 0, 'F', 1, 1, 'system:user:edit',           '', 1, CURRENT_TIMESTAMP, 1, CURRENT_TIMESTAMP, '');
insert into sys_menu values('1003', '用户删除', '100', '4',  '', '', '', 0, 0, 'F', 1, 1, 'system:user:remove',         '', 1, CURRENT_TIMESTAMP, 1, CURRENT_TIMESTAMP, '');
insert into sys_menu values('1004', '用户导出', '100', '5',  '', '', '', 0, 0, 'F', 1, 1, 'system:user:export',         '', 1, CURRENT_TIMESTAMP, 1, CURRENT_TIMESTAMP, '');
insert into sys_menu values('1005', '用户导入', '100', '6',  '', '', '', 0, 0, 'F', 1, 1, 'system:user:import',         '', 1, CURRENT_TIMESTAMP, 1, CURRENT_TIMESTAMP, '');
insert into sys_menu values('1006', '重置密码', '100', '7',  '', '', '', 0, 0, 'F', 1, 1, 'system:user:resetPwd',       '', 1, CURRENT_TIMESTAMP, 1, CURRENT_TIMESTAMP, '');
-- 角色管理按钮
insert into sys_menu values('1007', '角色查询', '101', '1',  '', '', '', 0, 0, 'F', 1, 1, 'system:role:query',          '', 1, CURRENT_TIMESTAMP, 1, CURRENT_TIMESTAMP, '');
insert into sys_menu values('1008', '角色新增', '101', '2',  '', '', '', 0, 0, 'F', 1, 1, 'system:role:add',            '', 1, CURRENT_TIMESTAMP, 1, CURRENT_TIMESTAMP, '');
insert into sys_menu values('1009', '角色修改', '101', '3',  '', '', '', 0, 0, 'F', 1, 1, 'system:role:edit',           '', 1, CURRENT_TIMESTAMP, 1, CURRENT_TIMESTAMP, '');
insert into sys_menu values('1010', '角色删除', '101', '4',  '', '', '', 0, 0, 'F', 1, 1, 'system:role:remove',         '', 1, CURRENT_TIMESTAMP, 1, CURRENT_TIMESTAMP, '');
insert into sys_menu values('1011', '角色导出', '101', '5',  '', '', '', 0, 0, 'F', 1, 1, 'system:role:export',         '', 1, CURRENT_TIMESTAMP, 1, CURRENT_TIMESTAMP, '');
-- 菜单管理按钮
insert into sys_menu values('1012', '菜单查询', '102', '1',  '', '', '', 0, 0, 'F', 1, 1, 'system:menu:query',          '', 1, CURRENT_TIMESTAMP, 1, CURRENT_TIMESTAMP, '');
insert into sys_menu values('1013', '菜单新增', '102', '2',  '', '', '', 0, 0, 'F', 1, 1, 'system:menu:add',            '', 1, CURRENT_TIMESTAMP, 1, CURRENT_TIMESTAMP, '');
insert into sys_menu values('1014', '菜单修改', '102', '3',  '', '', '', 0, 0, 'F', 1, 1, 'system:menu:edit',           '', 1, CURRENT_TIMESTAMP, 1, CURRENT_TIMESTAMP, '');
insert into sys_menu values('1015', '菜单删除', '102', '4',  '', '', '', 0, 0, 'F', 1, 1, 'system:menu:remove',         '', 1, CURRENT_TIMESTAMP, 1, CURRENT_TIMESTAMP, '');
-- 部门管理按钮
insert into sys_menu values('1016', '部门查询', '103', '1',  '', '', '', 0, 0, 'F', 1, 1, 'system:dept:query',          '', 1, CURRENT_TIMESTAMP, 1, CURRENT_TIMESTAMP, '');
insert into sys_menu values('1017', '部门新增', '103', '2',  '', '', '', 0, 0, 'F', 1, 1, 'system:dept:add',            '', 1, CURRENT_TIMESTAMP, 1, CURRENT_TIMESTAMP, '');
insert into sys_menu values('1018', '部门修改', '103', '3',  '', '', '', 0, 0, 'F', 1, 1, 'system:dept:edit',           '', 1, CURRENT_TIMESTAMP, 1, CURRENT_TIMESTAMP, '');
insert into sys_menu values('1019', '部门删除', '103', '4',  '', '', '', 0, 0, 'F', 1, 1, 'system:dept:remove',         '', 1, CURRENT_TIMESTAMP, 1, CURRENT_TIMESTAMP, '');
-- 岗位管理按钮
insert into sys_menu values('1020', '岗位查询', '104', '1',  '', '', '', 0, 0, 'F', 1, 1, 'system:post:query',          '', 1, CURRENT_TIMESTAMP, 1, CURRENT_TIMESTAMP, '');
insert into sys_menu values('1021', '岗位新增', '104', '2',  '', '', '', 0, 0, 'F', 1, 1, 'system:post:add',            '', 1, CURRENT_TIMESTAMP, 1, CURRENT_TIMESTAMP, '');
insert into sys_menu values('1022', '岗位修改', '104', '3',  '', '', '', 0, 0, 'F', 1, 1, 'system:post:edit',           '', 1, CURRENT_TIMESTAMP, 1, CURRENT_TIMESTAMP, '');
insert into sys_menu values('1023', '岗位删除', '104', '4',  '', '', '', 0, 0, 'F', 1, 1, 'system:post:remove',         '', 1, CURRENT_TIMESTAMP, 1, CURRENT_TIMESTAMP, '');
insert into sys_menu values('1024', '岗位导出', '104', '5',  '', '', '', 0, 0, 'F', 1, 1, 'system:post:export',         '', 1, CURRENT_TIMESTAMP, 1, CURRENT_TIMESTAMP, '');
-- 字典管理按钮
insert into sys_menu values('1025', '字典查询', '105', '1', '', '', '', 0, 0, 'F', 1, 1, 'system:dict:query',          '', 1, CURRENT_TIMESTAMP, 1, CURRENT_TIMESTAMP, '');
insert into sys_menu values('1026', '字典新增', '105', '2', '', '', '', 0, 0, 'F', 1, 1, 'system:dict:add',            '', 1, CURRENT_TIMESTAMP, 1, CURRENT_TIMESTAMP, '');
insert into sys_menu values('1027', '字典修改', '105', '3', '', '', '', 0, 0, 'F', 1, 1, 'system:dict:edit',           '', 1, CURRENT_TIMESTAMP, 1, CURRENT_TIMESTAMP, '');
insert into sys_menu values('1028', '字典删除', '105', '4', '', '', '', 0, 0, 'F', 1, 1, 'system:dict:remove',         '', 1, CURRENT_TIMESTAMP, 1, CURRENT_TIMESTAMP, '');
insert into sys_menu values('1029', '字典导出', '105', '5', '', '', '', 0, 0, 'F', 1, 1, 'system:dict:export',         '', 1, CURRENT_TIMESTAMP, 1, CURRENT_TIMESTAMP, '');
-- 参数设置按钮
insert into sys_menu values('1030', '参数查询', '106', '1', '', '', '', 0, 0, 'F', 1, 1, 'system:config:query',        '', 1, CURRENT_TIMESTAMP, 1, CURRENT_TIMESTAMP, '');
insert into sys_menu values('1031', '参数新增', '106', '2', '', '', '', 0, 0, 'F', 1, 1, 'system:config:add',          '', 1, CURRENT_TIMESTAMP, 1, CURRENT_TIMESTAMP, '');
insert into sys_menu values('1032', '参数修改', '106', '3', '', '', '', 0, 0, 'F', 1, 1, 'system:config:edit',         '', 1, CURRENT_TIMESTAMP, 1, CURRENT_TIMESTAMP, '');
insert into sys_menu values('1033', '参数删除', '106', '4', '', '', '', 0, 0, 'F', 1, 1, 'system:config:remove',       '', 1, CURRENT_TIMESTAMP, 1, CURRENT_TIMESTAMP, '');
insert into sys_menu values('1034', '参数导出', '106', '5', '', '', '', 0, 0, 'F', 1, 1, 'system:config:export',       '', 1, CURRENT_TIMESTAMP, 1, CURRENT_TIMESTAMP, '');
-- 通知公告按钮
insert into sys_menu values('1035', '公告查询', '107', '1', '', '', '', 0, 0, 'F', 1, 1, 'system:notice:query',        '', 1, CURRENT_TIMESTAMP, 1, CURRENT_TIMESTAMP, '');
insert into sys_menu values('1036', '公告新增', '107', '2', '', '', '', 0, 0, 'F', 1, 1, 'system:notice:add',          '', 1, CURRENT_TIMESTAMP, 1, CURRENT_TIMESTAMP, '');
insert into sys_menu values('1037', '公告修改', '107', '3', '', '', '', 0, 0, 'F', 1, 1, 'system:notice:edit',         '', 1, CURRENT_TIMESTAMP, 1, CURRENT_TIMESTAMP, '');
insert into sys_menu values('1038', '公告删除', '107', '4', '', '', '', 0, 0, 'F', 1, 1, 'system:notice:remove',       '', 1, CURRENT_TIMESTAMP, 1, CURRENT_TIMESTAMP, '');
-- 操作日志按钮
insert into sys_menu values('1039', '操作查询', '500', '1', '', '', '', 0, 0, 'F', 1, 1, 'monitor:operlog:query',      '', 1, CURRENT_TIMESTAMP, 1, CURRENT_TIMESTAMP, '');
insert into sys_menu values('1040', '操作删除', '500', '2', '', '', '', 0, 0, 'F', 1, 1, 'monitor:operlog:remove',     '', 1, CURRENT_TIMESTAMP, 1, CURRENT_TIMESTAMP, '');
insert into sys_menu values('1041', '日志导出', '500', '3', '', '', '', 0, 0, 'F', 1, 1, 'monitor:operlog:export',     '', 1, CURRENT_TIMESTAMP, 1, CURRENT_TIMESTAMP, '');
-- 登录日志按钮
insert into sys_menu values('1042', '登录查询', '501', '1', '', '', '', 0, 0, 'F', 1, 1, 'monitor:logininfor:query',   '', 1, CURRENT_TIMESTAMP, 1, CURRENT_TIMESTAMP, '');
insert into sys_menu values('1043', '登录删除', '501', '2', '', '', '', 0, 0, 'F', 1, 1, 'monitor:logininfor:remove',  '', 1, CURRENT_TIMESTAMP, 1, CURRENT_TIMESTAMP, '');
insert into sys_menu values('1044', '日志导出', '501', '3', '', '', '', 0, 0, 'F', 1, 1, 'monitor:logininfor:export',  '', 1, CURRENT_TIMESTAMP, 1, CURRENT_TIMESTAMP, '');
insert into sys_menu values('1045', '账户解锁', '501', '4', '', '', '', 0, 0, 'F', 1, 1, 'monitor:logininfor:unlock',  '', 1, CURRENT_TIMESTAMP, 1, CURRENT_TIMESTAMP, '');
-- 在线用户按钮
insert into sys_menu values('1046', '在线查询', '109', '1', '', '', '', 0, 0, 'F', 1, 1, 'monitor:online:query',       '', 1, CURRENT_TIMESTAMP, 1, CURRENT_TIMESTAMP, '');
insert into sys_menu values('1047', '批量强退', '109', '2', '', '', '', 0, 0, 'F', 1, 1, 'monitor:online:batchLogout', '', 1, CURRENT_TIMESTAMP, 1, CURRENT_TIMESTAMP, '');
insert into sys_menu values('1048', '单条强退', '109', '3', '', '', '', 0, 0, 'F', 1, 1, 'monitor:online:forceLogout', '', 1, CURRENT_TIMESTAMP, 1, CURRENT_TIMESTAMP, '');
-- 定时任务按钮
insert into sys_menu values('1049', '任务查询', '110', '1', '', '', '', 0, 0, 'F', 1, 1, 'monitor:job:query',          '', 1, CURRENT_TIMESTAMP, 1, CURRENT_TIMESTAMP, '');
insert into sys_menu values('1050', '任务新增', '110', '2', '', '', '', 0, 0, 'F', 1, 1, 'monitor:job:add',            '', 1, CURRENT_TIMESTAMP, 1, CURRENT_TIMESTAMP, '');
insert into sys_menu values('1051', '任务修改', '110', '3', '', '', '', 0, 0, 'F', 1, 1, 'monitor:job:edit',           '', 1, CURRENT_TIMESTAMP, 1, CURRENT_TIMESTAMP, '');
insert into sys_menu values('1052', '任务删除', '110', '4', '', '', '', 0, 0, 'F', 1, 1, 'monitor:job:remove',         '', 1, CURRENT_TIMESTAMP, 1, CURRENT_TIMESTAMP, '');
insert into sys_menu values('1053', '状态修改', '110', '5', '', '', '', 0, 0, 'F', 1, 1, 'monitor:job:changeStatus',   '', 1, CURRENT_TIMESTAMP, 1, CURRENT_TIMESTAMP, '');
insert into sys_menu values('1054', '任务导出', '110', '6', '', '', '', 0, 0, 'F', 1, 1, 'monitor:job:export',         '', 1, CURRENT_TIMESTAMP, 1, CURRENT_TIMESTAMP, '');
-- 代码生成按钮
insert into sys_menu values('1055', '生成查询', '115', '1', '', '', '', 0, 0, 'F', 1, 1, 'tool:gen:query',             '', 1, CURRENT_TIMESTAMP, 1, CURRENT_TIMESTAMP, '');
insert into sys_menu values('1056', '生成修改', '115', '2', '', '', '', 0, 0, 'F', 1, 1, 'tool:gen:edit',              '', 1, CURRENT_TIMESTAMP, 1, CURRENT_TIMESTAMP, '');
insert into sys_menu values('1057', '生成删除', '115', '3', '', '', '', 0, 0, 'F', 1, 1, 'tool:gen:remove',            '', 1, CURRENT_TIMESTAMP, 1, CURRENT_TIMESTAMP, '');
insert into sys_menu values('1058', '导入代码', '115', '4', '', '', '', 0, 0, 'F', 1, 1, 'tool:gen:import',            '', 1, CURRENT_TIMESTAMP, 1, CURRENT_TIMESTAMP, '');
insert into sys_menu values('1059', '预览代码', '115', '5', '', '', '', 0, 0, 'F', 1, 1, 'tool:gen:preview',           '', 1, CURRENT_TIMESTAMP, 1, CURRENT_TIMESTAMP, '');
insert into sys_menu values('1060', '生成代码', '115', '6', '', '', '', 0, 0, 'F', 1, 1, 'tool:gen:code',              '', 1, CURRENT_TIMESTAMP, 1, CURRENT_TIMESTAMP, '');


-- ----------------------------
-- 6、用户和角色关联表  用户 N-1 角色
-- ----------------------------
drop table if exists sys_user_role;
create table sys_user_role (
  user_id   bigint(20) not null comment '用户 ID',
  role_id   bigint(20) not null comment '角色 ID',
  primary key(user_id, role_id),
  index idx_sys_user_role_user_id (user_id),
  index idx_sys_user_role_role_id (role_id)
) engine = innodb comment = '用户和角色关联表' charset = utf8mb4 collate = utf8mb4_general_ci;

-- ----------------------------
-- 初始化 - 用户和角色关联表数据
-- ----------------------------
insert into sys_user_role values (1, 1);

-- ----------------------------
-- 7、角色和菜单关联表  角色 1-N 菜单
-- ----------------------------
drop table if exists sys_role_menu;
create table sys_role_menu (
  role_id   bigint(20) not null comment '角色 ID',
  menu_id   bigint(20) not null comment '菜单 ID',
  primary key(role_id, menu_id),
  index idx_sys_role_menu_role_id (role_id),
  index idx_sys_role_menu_menu_id (menu_id)
) engine = innodb comment = '角色和菜单关联表' charset = utf8mb4 collate = utf8mb4_general_ci;

-- ----------------------------
-- 8、角色和部门关联表  角色 1-N 部门
-- ----------------------------
drop table if exists sys_role_dept;
create table sys_role_dept (
  role_id   bigint(20) not null comment '角色 ID',
  dept_id   bigint(20) not null comment '部门 ID',
  primary key(role_id, dept_id),
  index idx_sys_role_dept_role_id (role_id),
  index idx_sys_role_dept_dept_id (dept_id)
) engine = innodb comment = '角色和部门关联表' charset = utf8mb4 collate = utf8mb4_general_ci;


-- ----------------------------
-- 9、用户与岗位关联表  用户 1-N 岗位
-- ----------------------------
drop table if exists sys_user_post;
create table sys_user_post (
  user_id   bigint(20) not null comment '用户 ID',
  post_id   bigint(20) not null comment '岗位 ID',
  primary key (user_id, post_id),
  index idx_sys_user_post_user_id (user_id),
  index idx_sys_user_post_post_id (post_id)
) engine = innodb comment = '用户与岗位关联表' charset = utf8mb4 collate = utf8mb4_general_ci;


-- ----------------------------
-- 10、操作日志记录
-- ----------------------------
drop table if exists sys_oper_log;
create table sys_oper_log (
  oper_id           bigint(20)      not null auto_increment    comment '操作日志 ID',
  title             varchar(100)    not null default ''                 comment '模块标题（功能模块名称）',
  business_type     int(2)          not null default 0                  comment '业务类型（0 其它 1 新增 2 修改 3 删除 4 授权 5 导出 6 导入 7 强退 8 生成代码 9 清空数据）',
  method            varchar(200)    not null default ''                 comment '方法名称（Java 方法名）',
  request_method    varchar(10)     not null default ''                 comment '请求方式（GET POST PUT DELETE 等）',
  operator_type     int(1)          not null default 0                  comment '操作类别（0 其它 1 后台用户 2 手机端用户）',
  oper_name         varchar(50)     not null default ''                 comment '操作人员（用户名）',
  dept_name         varchar(50)     not null default ''                 comment '部门名称（所属部门）',
  oper_url          varchar(255)    not null default ''                 comment '请求 URL（接口地址）',
  oper_ip           varchar(128)    not null default ''                 comment '主机地址（客户端 IP）',
  oper_location     varchar(255)    not null default ''                 comment '操作地点（地理位置）',
  oper_param        text            not null                            comment '请求参数（JSON 格式）',
  json_result       text            not null                            comment '返回参数（JSON 格式）',
  status            boolean         not null default 1                  comment '操作状态（1 正常 0 异常）',
  error_msg         text            not null                            comment '错误消息（异常信息）',
  oper_time         datetime        not null default CURRENT_TIMESTAMP  comment '操作时间',
  cost_time         bigint(20)      not null default 0                  comment '消耗时间（毫秒）',
  primary key (oper_id),
  index idx_sys_oper_log_bt (business_type),
  index idx_sys_oper_log_s (status),
  index idx_sys_oper_log_ot (oper_time),
  index idx_sys_oper_log_on (oper_name),
  index idx_sys_oper_log_ip (oper_ip)
) engine = innodb comment = '操作日志记录' charset = utf8mb4 collate = utf8mb4_general_ci;


-- ----------------------------
-- 11、字典类型表
-- ----------------------------
drop table if exists sys_dict_type;
create table sys_dict_type (
  dict_id          bigint(20)      not null auto_increment            comment '字典类型 ID',
  dict_name        varchar(100)    not null default ''                comment '字典名称（显示名称）',
  dict_type        varchar(100)    not null default ''                comment '字典类型（唯一标识）',
  status           boolean         not null default 1                 comment '字典状态（1 正常 0 停用）',
  create_by        bigint(20)      not null default 0                 comment '创建者 ID',
  create_time      datetime        not null default CURRENT_TIMESTAMP comment '创建时间',
  update_by        bigint(20)      not null default 0                 comment '更新者 ID',
  update_time      datetime        not null default CURRENT_TIMESTAMP on update CURRENT_TIMESTAMP comment '更新时间',
  remark           varchar(500)    default ''                         comment '字典类型备注',
  primary key (dict_id),
  unique key uk_sys_dict_type (dict_type),
  index idx_sys_dict_type_status (status)
) engine = innodb comment = '字典类型表' charset = utf8mb4 collate = utf8mb4_general_ci;


insert into sys_dict_type values(1,  '用户性别', 'sys_user_sex',        1, 1, CURRENT_TIMESTAMP, 1, CURRENT_TIMESTAMP, '用户性别列表');
insert into sys_dict_type values(2,  '菜单状态', 'sys_show_hide',       1, 1, CURRENT_TIMESTAMP, 1, CURRENT_TIMESTAMP, '菜单状态列表');
insert into sys_dict_type values(3,  '系统开关', 'sys_normal_disable',  1, 1, CURRENT_TIMESTAMP, 1, CURRENT_TIMESTAMP, '系统开关列表');
insert into sys_dict_type values(4,  '任务状态', 'sys_job_status',      1, 1, CURRENT_TIMESTAMP, 1, CURRENT_TIMESTAMP, '任务状态列表');
insert into sys_dict_type values(5,  '任务分组', 'sys_job_group',       1, 1, CURRENT_TIMESTAMP, 1, CURRENT_TIMESTAMP, '任务分组列表');
insert into sys_dict_type values(6,  '系统是否', 'sys_yes_no',          1, 1, CURRENT_TIMESTAMP, 1, CURRENT_TIMESTAMP, '系统是否列表');
insert into sys_dict_type values(7,  '通知类型', 'sys_notice_type',     1, 1, CURRENT_TIMESTAMP, 1, CURRENT_TIMESTAMP, '通知类型列表');
insert into sys_dict_type values(8,  '通知状态', 'sys_notice_status',   1, 1, CURRENT_TIMESTAMP, 1, CURRENT_TIMESTAMP, '通知状态列表');
insert into sys_dict_type values(9,  '操作类型', 'sys_oper_type',       1, 1, CURRENT_TIMESTAMP, 1, CURRENT_TIMESTAMP, '操作类型列表');
insert into sys_dict_type values(10, '系统状态', 'sys_common_status',   1, 1, CURRENT_TIMESTAMP, 1, CURRENT_TIMESTAMP, '登录状态列表');


-- ----------------------------
-- 12、字典数据表
-- ----------------------------
drop table if exists sys_dict_data;
create table sys_dict_data (
  dict_code        bigint(20)      not null auto_increment    comment '字典数据 ID',
  dict_sort        int(4)          not null default 0         comment '字典排序（排序号）',
  dict_label       varchar(100)    not null default ''        comment '字典标签（显示文本）',
  dict_value       varchar(100)    not null default ''        comment '字典键值（存储值）',
  dict_type        varchar(100)    not null default ''        comment '字典类型（关联字典类型表）',
  css_class        varchar(100)    default ''                 comment 'CSS 样式类名（用于显示样式）',
  list_class       varchar(100)    default ''                 comment '表格显示样式类名',
  is_default       char(1)         not null default 'N'       comment '是否默认项（Y 是 N 否）',
  status           boolean         not null default 1         comment '字典状态（1 正常 0 停用）',
  create_by        bigint(20)      not null default 0         comment '创建者 ID',
  create_time      datetime        not null default CURRENT_TIMESTAMP comment '创建时间',
  update_by        bigint(20)      not null default 0         comment '更新者 ID',
  update_time      datetime        not null default CURRENT_TIMESTAMP on update CURRENT_TIMESTAMP comment '更新时间',
  remark           varchar(500)    default ''                 comment '字典数据备注',
  primary key (dict_code),
  index idx_sys_dict_data_type (dict_type),
  index idx_sys_dict_data_status (status),
  index idx_sys_dict_data_sort (dict_sort)
) engine = innodb comment = '字典数据表' charset = utf8mb4 collate = utf8mb4_general_ci;

insert into sys_dict_data values(1,  1,  '男',       '0',       'sys_user_sex',        '',   '',        'Y', 1, 1, CURRENT_TIMESTAMP, 1, CURRENT_TIMESTAMP, '性别男');
insert into sys_dict_data values(2,  2,  '女',       '1',       'sys_user_sex',        '',   '',        'N', 1, 1, CURRENT_TIMESTAMP, 1, CURRENT_TIMESTAMP, '性别女');
insert into sys_dict_data values(3,  3,  '未知',     '2',       'sys_user_sex',        '',   '',        'N', 1, 1, CURRENT_TIMESTAMP, 1, CURRENT_TIMESTAMP, '性别未知');
insert into sys_dict_data values(4,  1,  '显示',     '1',       'sys_show_hide',       '',   'primary', 'Y', 1, 1, CURRENT_TIMESTAMP, 1, CURRENT_TIMESTAMP, '显示菜单');
insert into sys_dict_data values(5,  2,  '隐藏',     '0',       'sys_show_hide',       '',   'danger',  'N', 1, 1, CURRENT_TIMESTAMP, 1, CURRENT_TIMESTAMP, '隐藏菜单');
insert into sys_dict_data values(6,  1,  '正常',     '1',       'sys_normal_disable',  '',   'primary', 'Y', 1, 1, CURRENT_TIMESTAMP, 1, CURRENT_TIMESTAMP, '正常状态');
insert into sys_dict_data values(7,  2,  '停用',     '0',       'sys_normal_disable',  '',   'danger',  'N', 1, 1, CURRENT_TIMESTAMP, 1, CURRENT_TIMESTAMP, '停用状态');
insert into sys_dict_data values(8,  1,  '正常',     '1',       'sys_job_status',      '',   'primary', 'Y', 1, 1, CURRENT_TIMESTAMP, 1, CURRENT_TIMESTAMP, '正常状态');
insert into sys_dict_data values(9,  2,  '暂停',     '0',       'sys_job_status',      '',   'danger',  'N', 1, 1, CURRENT_TIMESTAMP, 1, CURRENT_TIMESTAMP, '停用状态');
insert into sys_dict_data values(10, 1,  '默认',     'DEFAULT', 'sys_job_group',       '',   '',        'Y', 1, 1, CURRENT_TIMESTAMP, 1, CURRENT_TIMESTAMP, '默认分组');
insert into sys_dict_data values(11, 2,  '系统',     'SYSTEM',  'sys_job_group',       '',   '',        'N', 1, 1, CURRENT_TIMESTAMP, 1, CURRENT_TIMESTAMP, '系统分组');
insert into sys_dict_data values(12, 1,  '是',       'Y',       'sys_yes_no',          '',   'primary', 'Y', 1, 1, CURRENT_TIMESTAMP, 1, CURRENT_TIMESTAMP, '系统默认是');
insert into sys_dict_data values(13, 2,  '否',       'N',       'sys_yes_no',          '',   'danger',  'N', 1, 1, CURRENT_TIMESTAMP, 1, CURRENT_TIMESTAMP, '系统默认否');
insert into sys_dict_data values(14, 1,  '通知',     '1',       'sys_notice_type',     '',   'warning', 'Y', 1, 1, CURRENT_TIMESTAMP, 1, CURRENT_TIMESTAMP, '通知');
insert into sys_dict_data values(15, 2,  '公告',     '2',       'sys_notice_type',     '',   'success', 'N', 1, 1, CURRENT_TIMESTAMP, 1, CURRENT_TIMESTAMP, '公告');
insert into sys_dict_data values(16, 1,  '正常',     '1',       'sys_notice_status',   '',   'primary', 'Y', 1, 1, CURRENT_TIMESTAMP, 1, CURRENT_TIMESTAMP, '正常状态');
insert into sys_dict_data values(17, 2,  '关闭',     '0',       'sys_notice_status',   '',   'danger',  'N', 1, 1, CURRENT_TIMESTAMP, 1, CURRENT_TIMESTAMP, '关闭状态');
insert into sys_dict_data values(18, 99, '其他',     '0',       'sys_oper_type',       '',   'info',    'N', 1, 1, CURRENT_TIMESTAMP, 1, CURRENT_TIMESTAMP, '其他操作');
insert into sys_dict_data values(19, 1,  '新增',     '1',       'sys_oper_type',       '',   'info',    'N', 1, 1, CURRENT_TIMESTAMP, 1, CURRENT_TIMESTAMP, '新增操作');
insert into sys_dict_data values(20, 2,  '修改',     '2',       'sys_oper_type',       '',   'info',    'N', 1, 1, CURRENT_TIMESTAMP, 1, CURRENT_TIMESTAMP, '修改操作');
insert into sys_dict_data values(21, 3,  '删除',     '3',       'sys_oper_type',       '',   'danger',  'N', 1, 1, CURRENT_TIMESTAMP, 1, CURRENT_TIMESTAMP, '删除操作');
insert into sys_dict_data values(22, 4,  '授权',     '4',       'sys_oper_type',       '',   'primary', 'N', 1, 1, CURRENT_TIMESTAMP, 1, CURRENT_TIMESTAMP, '授权操作');
insert into sys_dict_data values(23, 5,  '导出',     '5',       'sys_oper_type',       '',   'warning', 'N', 1, 1, CURRENT_TIMESTAMP, 1, CURRENT_TIMESTAMP, '导出操作');
insert into sys_dict_data values(24, 6,  '导入',     '6',       'sys_oper_type',       '',   'warning', 'N', 1, 1, CURRENT_TIMESTAMP, 1, CURRENT_TIMESTAMP, '导入操作');
insert into sys_dict_data values(25, 7,  '强退',     '7',       'sys_oper_type',       '',   'danger',  'N', 1, 1, CURRENT_TIMESTAMP, 1, CURRENT_TIMESTAMP, '强退操作');
insert into sys_dict_data values(26, 8,  '生成代码',  '8',       'sys_oper_type',       '',   'warning', 'N', 1, 1, CURRENT_TIMESTAMP, 1, CURRENT_TIMESTAMP, '生成操作');
insert into sys_dict_data values(27, 9,  '清空数据',  '9',       'sys_oper_type',       '',   'danger',  'N', 1, 1, CURRENT_TIMESTAMP, 1, CURRENT_TIMESTAMP, '清空操作');
insert into sys_dict_data values(28, 1,  '成功',     '1',       'sys_common_status',   '',   'primary', 'N', 1, 1, CURRENT_TIMESTAMP, 1, CURRENT_TIMESTAMP, '正常状态');
insert into sys_dict_data values(29, 2,  '失败',     '0',       'sys_common_status',   '',   'danger',  'N', 1, 1, CURRENT_TIMESTAMP, 1, CURRENT_TIMESTAMP, '停用状态');


-- ----------------------------
-- 13、参数配置表
-- ----------------------------
drop table if exists sys_config;
create table sys_config (
  config_id         bigint(20)      not null auto_increment    comment '参数配置 ID',
  config_name       varchar(100)    not null default ''        comment '参数名称（显示名称）',
  config_key        varchar(100)    not null default ''        comment '参数键名（唯一标识）',
  config_value      varchar(500)    not null default ''        comment '参数键值（配置值）',
  config_type       char(1)         not null default 'N'       comment '系统内置（Y 是 N 否）',
  create_by         bigint(20)      not null default 0         comment '创建者 ID',
  create_time       datetime        not null default CURRENT_TIMESTAMP comment '创建时间',
  update_by         bigint(20)      not null default 0         comment '更新者 ID',
  update_time       datetime        not null default CURRENT_TIMESTAMP on update CURRENT_TIMESTAMP comment '更新时间',
  remark            varchar(500)    default ''                 comment '参数配置备注',
  primary key (config_id),
  unique key uk_sys_config_key (config_key),
  index idx_sys_config_type (config_type)
) engine = innodb comment = '参数配置表' charset = utf8mb4 collate = utf8mb4_general_ci;


insert into sys_config values(1, '用户管理 - 账号初始密码',         'sys.user.initPassword',         '123456',        'Y', 1, CURRENT_TIMESTAMP, 1, CURRENT_TIMESTAMP, '初始化密码 123456' );
insert into sys_config values(2, '账号自助 - 验证码开关',           'sys.account.captchaEnabled',    'true',          'Y', 1, CURRENT_TIMESTAMP, 1, CURRENT_TIMESTAMP, '是否开启验证码功能（true 开启，false 关闭）');
insert into sys_config values(3, '账号自助 - 是否开启用户注册功能',   'sys.account.registerUser',      'false',         'Y', 1, CURRENT_TIMESTAMP, 1, CURRENT_TIMESTAMP, '是否开启注册用户功能（true 开启，false 关闭）');
insert into sys_config values(4, '用户登录 - 黑名单列表',           'sys.login.blackIPList',         '',              'Y', 1, CURRENT_TIMESTAMP, 1, CURRENT_TIMESTAMP, '设置登录 IP 黑名单限制，多个匹配项以;分隔，支持匹配（*通配、网段）');


-- ----------------------------
-- 14、系统访问记录
-- ----------------------------
drop table if exists sys_logininfor;
create table sys_logininfor (
  info_id        bigint(20)     not null auto_increment   comment '登录日志 ID',
  user_name      varchar(50)    not null default ''                comment '用户账号（登录名）',
  ipaddr         varchar(128)   not null default ''                comment '登录 IP 地址',
  login_location varchar(255)   not null default ''                comment '登录地点（地理位置）',
  browser        varchar(100)   not null default ''                comment '浏览器类型及版本',
  os             varchar(100)   not null default ''                comment '操作系统',
  status         boolean        not null default 1                 comment '登录状态（1 成功 0 失败）',
  msg            varchar(255)   not null default ''                comment '提示消息（登录结果）',
  login_time     datetime       not null default CURRENT_TIMESTAMP comment '访问时间（登录时间）',
  primary key (info_id),
  index idx_sys_logininfor_s (status),
  index idx_sys_logininfor_lt (login_time),
  index idx_sys_logininfor_un (user_name),
  index idx_sys_logininfor_ip (ipaddr)
) engine = innodb comment = '系统访问记录' charset = utf8mb4 collate = utf8mb4_general_ci;


-- ----------------------------
-- 15、定时任务调度表
-- ----------------------------
drop table if exists sys_job;
create table sys_job (
  job_id              bigint(20)    not null auto_increment    comment '定时任务 ID',
  job_name            varchar(100)  not null default ''                 comment '任务名称（显示名称）',
  job_group           varchar(64)   not null default 'DEFAULT'          comment '任务组名',
  invoke_target       varchar(500)  not null default ''                  comment '调用目标字符串（方法调用）',
  cron_expression     varchar(255)  not null default ''                 comment 'cron 执行表达式',
  misfire_policy      varchar(20)   not null default '3'                comment '计划执行错误策略（1 立即执行 2 执行一次 3 放弃执行）',
  concurrent          boolean       not null default 1                 comment '是否并发执行（1 允许 0 禁止）',
  status              boolean       not null default 1                 comment '定时任务状态（1 正常 0 暂停）',
  create_by           bigint(20)    not null default 0                 comment '创建者 ID',
  create_time         datetime      not null default CURRENT_TIMESTAMP comment '创建时间',
  update_by           bigint(20)    not null default 0                 comment '更新者 ID',
  update_time         datetime      not null default CURRENT_TIMESTAMP on update CURRENT_TIMESTAMP comment '更新时间',
  remark              varchar(500)  default ''                         comment '任务备注信息',
  primary key (job_id),
  unique key uk_sys_job_name_group (job_name, job_group),
  index idx_sys_job_status (status),
  index idx_sys_job_group (job_group)
) engine = innodb comment = '定时任务调度表' charset = utf8mb4 collate = utf8mb4_general_ci;

-- ----------------------------
-- 16、定时任务调度日志表
-- ----------------------------
drop table if exists sys_job_log;
create table sys_job_log (
  job_log_id          bigint(20)     not null auto_increment    comment '任务日志 ID',
  job_name            varchar(100)   not null                   comment '任务名称',
  job_group           varchar(64)    not null                   comment '任务组名',
  invoke_target       varchar(500)   not null                   comment '调用目标字符串（方法调用）',
  job_message         varchar(500)   default ''                 comment '执行日志信息',
  status              boolean        not null default 1          comment '定时任务执行状态（1 正常 0 失败）',
  exception_info      text           null                       comment '异常信息（错误堆栈）',
  create_time         datetime       not null default CURRENT_TIMESTAMP comment '创建时间（执行时间）',
  primary key (job_log_id),
  index idx_sys_job_log_status (status),
  index idx_sys_job_log_jn (job_name),
  index idx_sys_job_log_jg (job_group),
  index idx_sys_job_log_ct (create_time),
  index idx_sys_job_log_jng (job_name, job_group)
) engine = innodb comment = '定时任务调度日志表' charset = utf8mb4 collate = utf8mb4_general_ci;

-- ----------------------------
-- 17、通知公告表
-- ----------------------------
drop table if exists sys_notice;
drop table if exists sys_notice;
create table sys_notice (
  notice_id         bigint(20)      not null auto_increment    comment '公告 ID',
  notice_title      varchar(100)    not null                   comment '公告标题',
  notice_type       char(1)         not null                   comment '公告类型（1 通知 2 公告）',
  notice_content    longtext        null                       comment '公告内容（HTML 格式）',
  status            boolean         not null default 1         comment '公告状态（1 正常 0 关闭）',
  create_by         bigint(20)      not null default 0         comment '创建者 ID',
  create_time       datetime        not null default CURRENT_TIMESTAMP comment '创建时间',
  update_by         bigint(20)      not null default 0         comment '更新者 ID',
  update_time       datetime        not null default CURRENT_TIMESTAMP on update CURRENT_TIMESTAMP comment '更新时间',
  remark            varchar(500)    default ''                 comment '公告备注信息',
  primary key (notice_id),
  index idx_sys_notice_status (status),
  index idx_sys_notice_type (notice_type),
  index idx_sys_notice_ct (create_time)
) engine = innodb comment = '通知公告表' charset = utf8mb4 collate = utf8mb4_general_ci;


-- ----------------------------
-- 18、代码生成业务表
-- ----------------------------
drop table if exists gen_table;
create table gen_table (
  table_id          bigint(20)      not null auto_increment    comment '代码生成配置 ID',
  table_name        varchar(200)    not null                   comment '数据库表名称',
  table_comment     varchar(500)    not null                   comment '表描述信息',
  sub_table_name    varchar(64)     default null               comment '关联子表的表名',
  sub_table_fk_name varchar(64)     default null               comment '子表关联的外键名',
  class_name        varchar(100)    not null                   comment '实体类名称',
  tpl_category      varchar(200)    not null default 'crud'    comment '使用的模板（crud 单表操作 tree 树表操作）',
  tpl_web_type      varchar(30)     not null default ''        comment '前端模板类型（element-ui 模版 element-plus 模版）',
  package_name      varchar(100)    not null                   comment '生成包路径',
  module_name       varchar(30)     not null                   comment '生成模块名',
  business_name     varchar(30)     not null                   comment '生成业务名',
  function_name     varchar(50)     not null                   comment '生成功能名',
  function_author   varchar(50)     not null default ''        comment '生成功能作者',
  gen_type          char(1)         not null default '0'       comment '生成代码方式（0zip 压缩包 1 自定义路径）',
  gen_path          varchar(200)    not null default '/'       comment '生成路径（不填默认项目路径）',
  options           varchar(1000)   not null default ''        comment '其它生成选项（JSON 格式）',
  create_by         bigint(20)      not null default 0         comment '创建者 ID',
  create_time       datetime        not null default CURRENT_TIMESTAMP comment '创建时间',
  update_by         bigint(20)      not null default 0         comment '更新者 ID',
  update_time       datetime        not null default CURRENT_TIMESTAMP on update CURRENT_TIMESTAMP comment '更新时间',
  remark            varchar(500)    default ''                 comment '代码生成配置备注',
  primary key (table_id),
  index idx_gen_table_name (table_name),
  index idx_gen_table_module (module_name)
) engine = innodb comment = '代码生成业务表' charset = utf8mb4 collate = utf8mb4_general_ci;

-- ----------------------------
-- 19、代码生成业务表字段
-- ----------------------------
drop table if exists gen_table_column;
create table gen_table_column (
  column_id         bigint(20)      not null auto_increment    comment '字段配置 ID',
  table_id          bigint(20)      not null                   comment '归属表编号',
  column_name       varchar(200)    not null                   comment '数据库列名称',
  column_comment    varchar(500)    not null                   comment '列描述信息',
  column_type       varchar(100)    not null                   comment '数据库列类型',
  java_type         varchar(500)    not null                   comment 'Java 类型',
  java_field        varchar(200)    not null                   comment 'Java 字段名',
  is_pk             char(1)         not null default '0'       comment '是否主键（1 是 0 否）',
  is_increment      char(1)         not null default '0'       comment '是否自增（1 是 0 否）',
  is_required       char(1)         not null default '0'       comment '是否必填（1 是 0 否）',
  is_insert         char(1)         not null default '1'       comment '是否为插入字段（1 是 0 否）',
  is_edit           char(1)         not null default '1'       comment '是否编辑字段（1 是 0 否）',
  is_list           char(1)         not null default '1'       comment '是否列表字段（1 是 0 否）',
  is_query          char(1)         not null default '0'       comment '是否查询字段（1 是 0 否）',
  query_type        varchar(200)    not null default 'EQ'      comment '查询方式（EQ 等于 NE 不等于 GT 大于 LT 小于 LIKE 模糊 BETWEEN 范围）',
  html_type         varchar(200)    not null default ''        comment '显示类型（input 文本框 textarea 文本域 select 下拉框 checkbox 复选框 radio 单选框 datetime 日期控件）',
  dict_type         varchar(200)    not null default ''        comment '字典类型',
  sort              int             not null default 0          comment '排序号',
  create_by         bigint(20)      not null default 0         comment '创建者 ID',
  create_time       datetime        not null default CURRENT_TIMESTAMP comment '创建时间',
  update_by         bigint(20)      not null default 0         comment '更新者 ID',
  update_time       datetime        not null default CURRENT_TIMESTAMP on update CURRENT_TIMESTAMP comment '更新时间',
  primary key (column_id),
  index idx_gen_table_column_tid (table_id),
  index idx_gen_table_column_sort (sort)
) engine = innodb comment = '代码生成业务表字段' charset = utf8mb4 collate = utf8mb4_general_ci;
