DROP TABLE IF EXISTS QRTZ_FIRED_TRIGGERS;
DROP TABLE IF EXISTS QRTZ_PAUSED_TRIGGER_GRPS;
DROP TABLE IF EXISTS QRTZ_SCHEDULER_STATE;
DROP TABLE IF EXISTS QRTZ_LOCKS;
DROP TABLE IF EXISTS QRTZ_SIMPLE_TRIGGERS;
DROP TABLE IF EXISTS QRTZ_SIMPROP_TRIGGERS;
DROP TABLE IF EXISTS QRTZ_CRON_TRIGGERS;
DROP TABLE IF EXISTS QRTZ_BLOB_TRIGGERS;
DROP TABLE IF EXISTS QRTZ_TRIGGERS;
DROP TABLE IF EXISTS QRTZ_JOB_DETAILS;
DROP TABLE IF EXISTS QRTZ_CALENDARS;

-- ----------------------------
-- 1、存储每一个已配置的 jobDetail 的详细信息
-- ----------------------------
create table QRTZ_JOB_DETAILS (
    sched_name           varchar(120)    not null            comment '调度器名称',
    job_name             varchar(200)    not null            comment '任务名称（Job 名称）',
    job_group            varchar(200)    not null            comment '任务组名（Job 组名）',
    description          varchar(250)    null                comment '任务描述信息',
    job_class_name       varchar(250)    not null            comment '执行任务类名称（全限定类名）',
    is_durable           varchar(1)      not null            comment '是否持久化（1 是 0 否）',
    is_nonconcurrent     varchar(1)      not null            comment '是否并发执行（1 是 0 否）',
    is_update_data       varchar(1)      not null            comment '是否更新数据（1 是 0 否）',
    requests_recovery    varchar(1)      not null            comment '是否接受恢复执行（1 是 0 否）',
    job_data             blob            null                comment '存放持久化 Job 对象（序列化数据）',
    primary key (sched_name, job_name, job_group)
) engine=innodb comment = '任务详细信息表' charset = utf8mb4 collate = utf8mb4_general_ci;

-- ----------------------------
-- 2、存储已配置的 Trigger 的信息
-- ----------------------------
create table QRTZ_TRIGGERS (
    sched_name           varchar(120)    not null            comment '调度器名称',
    trigger_name         varchar(200)    not null            comment '触发器名称（Trigger 名称）',
    trigger_group        varchar(200)    not null            comment '触发器组名（Trigger 组名）',
    job_name             varchar(200)    not null            comment '关联的 Job 名称',
    job_group            varchar(200)    not null            comment '关联的 Job 组名',
    description          varchar(250)    null                comment '触发器描述信息',
    next_fire_time       bigint(13)      null                comment '下次触发时间（毫秒时间戳）',
    prev_fire_time       bigint(13)      null                comment '上次触发时间（毫秒时间戳）',
    priority             integer         null                comment '优先级（数值越大优先级越高）',
    trigger_state        varchar(16)     not null            comment '触发器状态（WAITING 等待中 ACQUIRED 已获取 PAUSED 暂停中 COMPLETE 完成 ERROR 错误 BLOCKED 阻塞）',
    trigger_type         varchar(8)      not null            comment '触发器类型（SIMPLE 简单触发器 CRON 触发器 CALENDAR 日历触发器 BLOB 二进制触发器）',
    start_time           bigint(13)      not null            comment '开始时间（毫秒时间戳）',
    end_time             bigint(13)      null                comment '结束时间（毫秒时间戳）',
    calendar_name        varchar(200)    null                comment '日历名称',
    misfire_instr        smallint(2)     null                comment '补偿执行策略（1 立即执行 2 执行一次 3 放弃执行）',
    job_data             blob            null                comment '存放持久化 Job 对象（序列化数据）',
    primary key (sched_name, trigger_name, trigger_group),
    foreign key (sched_name, job_name, job_group) references QRTZ_JOB_DETAILS(sched_name, job_name, job_group)
) engine=innodb comment = '触发器详细信息表' charset = utf8mb4 collate = utf8mb4_general_ci;

-- ----------------------------
-- 3、存储简单的 Trigger，包括重复次数，间隔，以及已触发的次数
-- ----------------------------
create table QRTZ_SIMPLE_TRIGGERS (
    sched_name           varchar(120)    not null            comment '调度器名称',
    trigger_name         varchar(200)    not null            comment '关联的 Trigger 名称',
    trigger_group        varchar(200)    not null            comment '关联的 Trigger 组名',
    repeat_count         bigint(7)       not null            comment '重复次数（-1 表示无限重复）',
    repeat_interval      bigint(12)      not null            comment '重复间隔时间（毫秒）',
    times_triggered      bigint(10)      not null            comment '已触发次数',
    primary key (sched_name, trigger_name, trigger_group),
    foreign key (sched_name, trigger_name, trigger_group) references QRTZ_TRIGGERS(sched_name, trigger_name, trigger_group)
) engine=innodb comment = '简单触发器的信息表' charset = utf8mb4 collate = utf8mb4_general_ci;

-- ----------------------------
-- 4、存储 Cron Trigger，包括 Cron 表达式和时区信息
-- ---------------------------- 
create table QRTZ_CRON_TRIGGERS (
    sched_name           varchar(120)    not null            comment '调度器名称',
    trigger_name         varchar(200)    not null            comment '关联的 Trigger 名称',
    trigger_group        varchar(200)    not null            comment '关联的 Trigger 组名',
    cron_expression      varchar(200)    not null            comment 'Cron 表达式（秒 分 时 日 月 周 年）',
    time_zone_id         varchar(80)     null                comment '时区 ID（如：Asia/Shanghai）',
    primary key (sched_name, trigger_name, trigger_group),
    foreign key (sched_name, trigger_name, trigger_group) references QRTZ_TRIGGERS(sched_name, trigger_name, trigger_group)
) engine=innodb comment = 'Cron 类型的触发器表' charset = utf8mb4 collate = utf8mb4_general_ci;

-- ----------------------------
-- 5、Trigger 作为 Blob 类型存储 (用于 Quartz 用户用 JDBC 创建他们自己定制的 Trigger 类型，JobStore 并不知道如何存储实例的时候)
-- ---------------------------- 
create table QRTZ_BLOB_TRIGGERS (
    sched_name           varchar(120)    not null            comment '调度器名称',
    trigger_name         varchar(200)    not null            comment '关联的 Trigger 名称',
    trigger_group        varchar(200)    not null            comment '关联的 Trigger 组名',
    blob_data            blob            null                comment '存放持久化 Trigger 对象（序列化数据）',
    primary key (sched_name, trigger_name, trigger_group),
    foreign key (sched_name, trigger_name, trigger_group) references QRTZ_TRIGGERS(sched_name, trigger_name, trigger_group)
) engine=innodb comment = 'Blob 类型的触发器表' charset = utf8mb4 collate = utf8mb4_general_ci;

-- ----------------------------
-- 6、以 Blob 类型存储存放日历信息，quartz 可配置一个日历来指定一个时间范围
-- ---------------------------- 
create table QRTZ_CALENDARS (
    sched_name           varchar(120)    not null            comment '调度器名称',
    calendar_name        varchar(200)    not null            comment '日历名称',
    calendar             blob            not null            comment '存放持久化 Calendar 对象（序列化数据）',
    primary key (sched_name, calendar_name)
) engine=innodb comment = '日历信息表' charset = utf8mb4 collate = utf8mb4_general_ci;

-- ----------------------------
-- 7、存储已暂停的 Trigger 组的信息
-- ---------------------------- 
create table QRTZ_PAUSED_TRIGGER_GRPS (
    sched_name           varchar(120)    not null            comment '调度器名称',
    trigger_group        varchar(200)    not null            comment '暂停的 Trigger 组名',
    primary key (sched_name, trigger_group)
) engine=innodb comment = '暂停的触发器表' charset = utf8mb4 collate = utf8mb4_general_ci;

-- ----------------------------
-- 8、存储与已触发的 Trigger 相关的状态信息，以及相联 Job 的执行信息
-- ---------------------------- 
create table QRTZ_FIRED_TRIGGERS (
    sched_name           varchar(120)    not null            comment '调度器名称',
    entry_id             varchar(95)     not null            comment '调度器实例 ID',
    trigger_name         varchar(200)    not null            comment '关联的 Trigger 名称',
    trigger_group        varchar(200)    not null            comment '关联的 Trigger 组名',
    instance_name        varchar(200)    not null            comment '调度器实例名称',
    fired_time           bigint(13)      not null            comment '实际触发时间（毫秒时间戳）',
    sched_time           bigint(13)      not null            comment '计划触发时间（毫秒时间戳）',
    priority             integer         not null            comment '优先级（数值越大优先级越高）',
    state                varchar(16)     not null            comment '状态（EXECUTING 执行中 ACQUIRED 已获取 WAITING 等待中）',
    job_name             varchar(200)    null                comment '关联的 Job 名称',
    job_group            varchar(200)    null                comment '关联的 Job 组名',
    is_nonconcurrent     varchar(1)      null                comment '是否并发执行（1 是 0 否）',
    requests_recovery    varchar(1)      null                comment '是否接受恢复执行（1 是 0 否）',
    primary key (sched_name, entry_id)
) engine=innodb comment = '已触发的触发器表' charset = utf8mb4 collate = utf8mb4_general_ci;

-- ----------------------------
-- 9、存储少量的有关 Scheduler 的状态信息，假如是用于集群中，可以看到其他的 Scheduler 实例
-- ---------------------------- 
create table QRTZ_SCHEDULER_STATE (
    sched_name           varchar(120)    not null            comment '调度器名称',
    instance_name        varchar(200)    not null            comment '调度器实例名称',
    last_checkin_time    bigint(13)      not null            comment '上次检查时间（毫秒时间戳）',
    checkin_interval     bigint(13)      not null            comment '检查间隔时间（毫秒）',
    primary key (sched_name, instance_name)
) engine=innodb comment = '调度器状态表' charset = utf8mb4 collate = utf8mb4_general_ci;

-- ----------------------------
-- 10、存储程序的悲观锁的信息 (假如使用了悲观锁)
-- ---------------------------- 
create table QRTZ_LOCKS (
    sched_name           varchar(120)    not null            comment '调度器名称',
    lock_name            varchar(40)     not null            comment '悲观锁名称',
    primary key (sched_name, lock_name)
) engine=innodb comment = '存储的悲观锁信息表' charset = utf8mb4 collate = utf8mb4_general_ci;

-- ----------------------------
-- 11、Quartz 集群实现同步机制的行锁表
-- ---------------------------- 
create table QRTZ_SIMPROP_TRIGGERS (
    sched_name           varchar(120)    not null            comment '调度器名称',
    trigger_name         varchar(200)    not null            comment '关联的 Trigger 名称',
    trigger_group        varchar(200)    not null            comment '关联的 Trigger 组名',
    str_prop_1           varchar(512)    null                comment 'String 类型的 Trigger 参数 1',
    str_prop_2           varchar(512)    null                comment 'String 类型的 Trigger 参数 2',
    str_prop_3           varchar(512)    null                comment 'String 类型的 Trigger 参数 3',
    int_prop_1           int             null                comment 'Integer 类型的 Trigger 参数 1',
    int_prop_2           int             null                comment 'Integer 类型的 Trigger 参数 2',
    long_prop_1          bigint          null                comment 'Long 类型的 Trigger 参数 1',
    long_prop_2          bigint          null                comment 'Long 类型的 Trigger 参数 2',
    dec_prop_1           decimal(13,4)   null                comment 'Decimal 类型的 Trigger 参数 1',
    dec_prop_2           decimal(13,4)   null                comment 'Decimal 类型的 Trigger 参数 2',
    bool_prop_1          varchar(1)      null                comment 'Boolean 类型的 Trigger 参数 1',
    bool_prop_2          varchar(1)      null                comment 'Boolean 类型的 Trigger 参数 2',
    primary key (sched_name, trigger_name, trigger_group),
    foreign key (sched_name, trigger_name, trigger_group) references QRTZ_TRIGGERS(sched_name, trigger_name, trigger_group)
) engine=innodb comment = '同步机制的行锁表' charset = utf8mb4 collate = utf8mb4_general_ci;

commit;