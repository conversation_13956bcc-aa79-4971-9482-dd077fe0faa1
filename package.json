{"scripts": {"dev": "./mvnw -pl xinjian-admin spring-boot:run", "build:staging": "./mvnw clean package -P staging -DskipTest", "build:production": "./mvnw clean package -P production -DskipTest", "publish:staging": "git stash && git checkout main && git reset --hard origin/dev && git push -f && git checkout dev && git stash pop", "publish:production": "git stash && git checkout release && git reset --hard origin/dev && git push -f && git checkout dev && git stash pop", "publish:production.xc": "git stash && git checkout xc && git pull && git reset --hard origin/dev && git push -f && git checkout dev", "publish:all": "npm run publish:staging && npm run publish:production", "update:deps": "./mvnw versions:update-properties", "check:deps": "./mvnw dependency:tree > dependency_tree.log", "format": "./mvnw spotless:apply -q", "git:upstream": "git remote add upstream git@***********:project-templates/xinjian-template/xinjian-service-api.git", "git:switch-remote": "chmod +x ./scripts/switch_git_remote.sh && ./scripts/switch_git_remote.sh"}}