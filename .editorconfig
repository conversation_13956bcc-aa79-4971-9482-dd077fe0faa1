# EditorConfig for RuoYi Template Project
# 符合 Google Java Format 规范的配置

# top-most EditorConfig file
root = true

# Unix-style newlines with a newline ending every file
[*]
end_of_line = lf
insert_final_newline = true
charset = utf-8
trim_trailing_whitespace = true

# Java files - Google Java Format 规范
[*.{java}]
indent_style = space
indent_size = 2
continuation_indent_size = 4

# XML files - Maven 风格
[*.{xml}]
indent_style = space
indent_size = 4

# YAML files - 项目风格
[*.{yml,yaml}]
indent_style = space
indent_size = 2

# Properties files
[*.{properties}]
indent_style = space
indent_size = 2

# SQL files
[*.{sql}]
indent_style = space
indent_size = 2

# JavaScript, JSON, CSS, HTML files
[*.{js,json,css,html,vue}]
indent_style = space
indent_size = 2

# Markdown files
[*.md]
trim_trailing_whitespace = false
indent_style = space
indent_size = 2

# Shell scripts
[*.{sh,bash}]
indent_style = space
indent_size = 2

# Batch files
[*.{bat,cmd}]
indent_style = tab
indent_size = 4

# Makefile
[Makefile]
indent_style = tab

# Docker files
[*.{dockerfile,dockerignore}]
indent_style = space
indent_size = 2

# Velocity template files - 禁止格式化
[*.{vm}]
indent_style = unset
indent_size = unset
trim_trailing_whitespace = false
insert_final_newline = false

# Configuration files
[*.{conf,config,ini}]
indent_style = space
indent_size = 2

# Git files
[.gitignore]
indent_style = space
indent_size = 2

# IDE files
[*.{iml,iws,ipr}]
indent_style = space
indent_size = 4

# VS Code files
[.vscode/**]
indent_style = space
indent_size = 2

# IDEA files
[.idea/**]
indent_style = space
indent_size = 2
